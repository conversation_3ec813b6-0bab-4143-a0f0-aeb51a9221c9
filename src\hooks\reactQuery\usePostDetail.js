import { useQuery } from './useQuery';
import { useMutation } from './useMutation';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Hook to fetch individual post details by slug
 * Always fetches fresh data to ensure likes/comments are up-to-date
 */
export function usePostDetail(slug, options = {}) {
  return useQuery("postItem", {
    slug, // Pass slug to get specific post
    
    // Always fetch fresh data for post details
    staleTime: 0, // Always consider stale to get fresh data
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when user returns to tab
    
    // Enable only when slug is provided
    enabled: !!slug,
    
    // Transform data
    select: (response) => {
      const post = response?.data || response;
      
      if (process.env.NODE_ENV === 'development') {
        console.log('Post detail data:', post);
      }
      
      return {
        ...post,
        // Ensure comments array exists
        comments: post.comments || [],
        // Ensure counts exist
        likesCount: post.likesCount || post.likes_count || 0,
        commentsCount: post.commentsCount || post.comments_count || post.comments?.length || 0,
      };
    },
    
    ...options,
  });
}

/**
 * Hook to post comments with optimistic updates
 */
export function usePostComment(options = {}) {
  const queryClient = useQueryClient();
  
  return useMutation("comments", {
    // Don't use FormData for comments - use JSON
    useFormData: false,
    
    onMutate: async (newComment) => {
      // Optimistic update for better UX
      const { post_id } = newComment;
      
      // Cancel outgoing refetches
      await queryClient.cancelQueries(['postItem', post_id]);
      
      // Snapshot previous value
      const previousPost = queryClient.getQueryData(['postItem', post_id]);
      
      // Optimistically update
      if (previousPost) {
        queryClient.setQueryData(['postItem', post_id], (old) => ({
          ...old,
          comments: [
            ...(old.comments || []),
            {
              id: 'temp-' + Date.now(),
              message: newComment.message,
              user: {
                name: 'You', // Placeholder for current user
              },
              created_at: new Date().toISOString(),
              isOptimistic: true,
            }
          ],
          commentsCount: (old.commentsCount || 0) + 1,
        }));
      }
      
      return { previousPost };
    },
    
    onError: (err, newComment, context) => {
      // Rollback optimistic update on error
      const { post_id } = newComment;
      if (context?.previousPost) {
        queryClient.setQueryData(['postItem', post_id], context.previousPost);
      }
    },
    
    onSuccess: (data, variables) => {
      // Invalidate and refetch post data to get real comment data
      const { post_id } = variables;
      
      // Invalidate post detail
      queryClient.invalidateQueries(['postItem', post_id]);
      
      // Also invalidate post lists to update comment counts
      queryClient.invalidateQueries(['postItem']); // For post listings
      
      if (process.env.NODE_ENV === 'development') {
        console.log('Comment posted successfully:', data);
      }
    },
    
    ...options,
  });
}

/**
 * Hook to like/unlike posts with optimistic updates
 */
export function usePostLike(options = {}) {
  const queryClient = useQueryClient();
  
  return useMutation("likePost", {
    onMutate: async (likeData) => {
      const { post_id } = likeData;
      
      // Cancel outgoing refetches
      await queryClient.cancelQueries(['postItem', post_id]);
      
      // Snapshot previous value
      const previousPost = queryClient.getQueryData(['postItem', post_id]);
      
      // Optimistically update
      if (previousPost) {
        queryClient.setQueryData(['postItem', post_id], (old) => ({
          ...old,
          isLiked: !old.isLiked,
          likesCount: old.isLiked ? (old.likesCount || 0) - 1 : (old.likesCount || 0) + 1,
        }));
      }
      
      return { previousPost };
    },
    
    onError: (err, likeData, context) => {
      // Rollback optimistic update on error
      const { post_id } = likeData;
      if (context?.previousPost) {
        queryClient.setQueryData(['postItem', post_id], context.previousPost);
      }
    },
    
    onSuccess: (data, variables) => {
      // Invalidate queries to sync with server
      const { post_id } = variables;
      
      // Invalidate post detail
      queryClient.invalidateQueries(['postItem', post_id]);
      
      // Also invalidate post lists
      queryClient.invalidateQueries(['postItem']);
    },
    
    ...options,
  });
}

export default usePostDetail;
