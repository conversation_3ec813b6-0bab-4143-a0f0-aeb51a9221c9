import React from "react";
import BaseInput from "../../shared/inputs";
import { Form } from "antd";
import CustomUpload from "../../shared/upload";

const CreateTaskForm = () => {
  return (
    <Form
      name="login"
      layout="vertical"
      onFinish={() => setIsModalOpen(false)}
      initialValues={{
        remember: true,
      }}
      autoComplete="off"
    >
      <div className="row">
        <div className="col-12 col-md-6">
          <BaseInput
            name="projectName"
            type="select"
            placeholder=""
            label="Project Name"
          />
        </div>
        <div className="col-12 col-md-6">
          <BaseInput
            name="category"
            type="select"
            placeholder=""
            label="Category"
          />
        </div>
        <div className="col-12 col-md-6">
          <BaseInput
            name="assignee"
            type="select"
            placeholder=""
            label="Assignee"
          />
        </div>
        <div className="col-12 col-md-6">
          <div className="row">
            <div className="col-12 col-md-6">
              <BaseInput
                name="datepiker"
                type="datepiker"
                placeholder=""
                label="Start Date"
              />
            </div>
            <div className="col-12 col-md-6">
              <BaseInput
                name="datepiker2"
                type="datepiker"
                placeholder=""
                label="Due Date"
              />
            </div>
          </div>
        </div>
        <div className="col-12">
          <BaseInput
            name="textarea"
            type="textarea"
            placeholder=""
            label="Description"
            rows="5"
          />
        </div>
        <div className="col-12">
          <label className="color-black font-600 mt-3 mb-1">
            Select Drawing
          </label>
          <CustomUpload multiple={true} maxFiles={5} />
        </div>
      </div>
    </Form>
  );
};

export default CreateTaskForm;
