import { useQuery, usePaginatedQuery } from "./useQuery";
import useMutation from "./useMutation";
import { usePrefetch } from "./usePrefetch";
import {
  useOptimisticMutation,
  createOptimisticUpdaters,
} from "./useOptimisticMutation";
import useStartupData from "./useStartupData";
import {
  usePaginatedData,
  usePaginatedUsers,
  usePaginatedPosts,
} from "./usePaginatedData";
import { useSubscriptionProducts } from "./useSubscriptionProducts";
import { usePostDetail, usePostComment, usePostLike } from "./usePostDetail";

export {
  useQuery,
  usePaginatedQuery,
  useMutation,
  usePrefetch,
  useOptimisticMutation,
  createOptimisticUpdaters,
  useStartupData,
  usePaginatedData,
  usePaginatedUsers,
  usePaginatedPosts,
  useSubscriptionProducts,
  usePostDetail,
  usePostComment,
  usePostLike,
};
