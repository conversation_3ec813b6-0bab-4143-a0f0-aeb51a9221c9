import { useState, useEffect } from "react";
import { useQuery, useMutation } from "./reactQuery";
import { message } from "antd";

const useSubscription = () => {
  const [subscriptionStatus, setSubscriptionStatus] = useState(null);

  // Query to get current subscription
  const { 
    data: currentSubscription, 
    isLoading: isLoadingSubscription,
    refetch: refetchSubscription 
  } = useQuery("getCurrentSubscription", {
    onSuccess: (data) => {
      setSubscriptionStatus(data);
    },
    onError: (error) => {
      console.error("Failed to fetch subscription:", error);
      setSubscriptionStatus(null);
    },
  });

  // Mutation to cancel subscription
  const { mutate: cancelSubscription, isPending: isCancellingSubscription } = useMutation("cancelSubscription", {
    onSuccess: (data) => {
      message.success("Subscription cancelled successfully!");
      setSubscriptionStatus(null);
      refetchSubscription(); // Refresh subscription status
    },
    onError: (error) => {
      console.error("Failed to cancel subscription:", error);
      message.error(error?.message || "Failed to cancel subscription. Please try again.");
    },
  });

  // Check if user is subscribed
  const isSubscribed = subscriptionStatus && subscriptionStatus.status === 'active';

  // Handle subscription success (called after successful payment)
  const handleSubscriptionSuccess = () => {
    refetchSubscription(); // Refresh subscription status
  };

  // Handle cancel subscription
  const handleCancelSubscription = () => {
    cancelSubscription();
  };

  return {
    currentSubscription: subscriptionStatus,
    isSubscribed,
    isLoadingSubscription,
    isCancellingSubscription,
    handleSubscriptionSuccess,
    handleCancelSubscription,
    refetchSubscription,
  };
};

export default useSubscription;