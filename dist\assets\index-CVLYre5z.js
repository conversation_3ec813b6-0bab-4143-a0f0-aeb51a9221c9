import{j as o}from"./index-3mE9H3a0.js";import{I as i}from"./index-C-I6oq0X.js";import{O as t}from"./optionlist-BlkuFOca.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";import"./index-BUUUOhAK.js";const m=()=>{const s=[{label:"Terms & Conditions",link:"/about/terms"},{label:"Privacy Policy",link:"/about/privacy"},{label:"FAQs",link:"/about/faq"}];return o.jsx(i,{children:o.jsx("div",{className:"container-fluid",children:o.jsxs("div",{className:"row",children:[o.jsx("div",{className:"col-12 mt-4",children:o.jsx("p",{className:"font-36 font-600 color-black",children:"About"})}),o.jsx("div",{className:"col-12",children:o.jsx(t,{options:s})})]})})})};export{m as default};
