import React, { memo, useState } from "react";
import { Form, Skeleton } from "antd";
import BaseInput from "@/components/shared/inputs";
import FlatButton from "@/components/shared/button/flatbutton";
import { useFetch } from "@/hooks";
import { create_company } from "@/config/rules";
import PhoneInput from "react-phone-number-input";
import CustomUpload from "@/components/shared/upload";

const EditProfileForm = ({ onCancel }) => {
  let user = window.user.user;
  const { loading: loader, data } = useFetch(
    "get_profile",
    "mount",
    `/${user._id}`,
    [],
    false
  );
  const companySizeOptions = window.helper.generateRanges(50, 1000);
  const { loading, postData } = useFetch("update_profile", "submit");
  const [form] = Form.useForm();
  const [imageFile, setImageFile] = useState(null); // Store the file object here

  // Callback function for file upload
  const fileUploadCallback = (file) => {
    setImageFile(file); // Store file object correctly
  };

  const onFinish = (values) => {
    const fd = new FormData();
    // Check if a new file has been uploaded
    if (imageFile) {
      fd.append("image_url", imageFile); // Append the actual file object
    }

    // Append other form values to FormData
    for (const key in values) {
      fd.append(key, values[key]);
    }

    postData(fd, cbSuccess, user._id);
  };

  const cbSuccess = (res) => {
    if (res.code === 200) {
      onCancel();
      window.user.user = res.data;
      form.resetFields();
      window.location.reload();
    }
  };

  return loader || !data ? (
    <Skeleton active paragraph={{ rows: 10 }} />
  ) : (
    <Form
      name="edit"
      layout="vertical"
      onFinish={onFinish}
      form={form}
      initialValues={{
        name: user?.role === "user" ? data && data?.name : undefined,
        company_name: user.role === "company" ? data?.company_name : undefined,
        company_size: user.role === "company" ? data?.company_size : undefined,
        mobile_no: data?.mobile_no || "",
        company_details:
          user.role === "company" ? data?.company_details : undefined,
        remember: true,
      }}
      autoComplete="off"
    >
      {/* <div className="text-center">
        
          <CustomUpload
            callback={fileUploadCallback}
            static_img={user?.image_url} // Pass static image URL from the user data
          />
        </div> */}
      {user.role === "user" ? (
        <BaseInput
          name="name"
          placeholder="Enter Name"
          label="Name"
          rules={create_company.name}
        />
      ) : (
        <>
          <BaseInput
            name="company_name"
            placeholder="Enter company name"
            label="Company Name"
            rules={create_company.company_name}
          />
          <BaseInput
            type="select"
            name="company_size"
            placeholder="Company Size"
            label="Company Size"
            options={companySizeOptions?.map((item) => ({
              value: item.key,
              label: item.value,
            }))}
            rules={create_company.company_size}
          />
          <div className="phone-input-container">
            <Form.Item
              label="Phone Number"
              name="mobile_no"
              validateTrigger="onBlur"
              rules={[
                {
                  required: true,
                  message: "Phone Number is required!",
                },
                {
                  validator: (_, value) =>
                    value && value.replace(/\D/g, "").slice(1).length !== 10
                      ? Promise.reject("Phone number must be 10 digits")
                      : Promise.resolve(),
                },
              ]}
            >
              <PhoneInput
                id="mobile_no"
                placeholder="Enter phone number"
                className="base-input"
                international={false}
                defaultCountry="US"
                disabled
              />
            </Form.Item>
          </div>
          <BaseInput
            type="textarea"
            name="company_details"
            placeholder="Enter company details"
            label="Company Details"
            rules={create_company.company_details}
          />
        </>
      )}

      <div className="text-end mt-4">
        <FlatButton
          title="Update"
          className="add-new-btn"
          htmlType="submit"
          loading={loading}
        />
      </div>
    </Form>
  );
};

export default memo(EditProfileForm);
