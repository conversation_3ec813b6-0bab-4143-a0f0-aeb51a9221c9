import React from "react";
import { Badge } from "antd";

const MessageBubble = ({ msg }) => {
  const isMyMessage = msg.from === "me";
  return (
    <div className={isMyMessage ? "my-chat-box" : "user-chat-box"}>
      <div className="d-flex ms-3">
        {!isMyMessage && (
          <div className="chat-user-img">
            <Badge color="green" dot={msg.onlineStatus === "online"}>
              <img src={msg.avatar} alt="" />
            </Badge>
          </div>
        )}
        <div className={isMyMessage ? "me-3" : "ms-3"}>
          {msg.text && (
            <div className="text-user">
              <p>{msg.text}</p>
            </div>
          )}
          {msg.images && (
            <div className="d-flex align-items-center gap-2 flex-wrap">
              {msg.images.map((img, i) => (
                <div className="chat-img-area" key={i}>
                  <img src={img} alt="" />
                </div>
              ))}
            </div>
          )}
          <p className="color-light mt-2">{msg.time}</p>
        </div>
        {isMyMessage && (
          <div className="chat-user-img">
            <Badge color="gray" dot>
              <img src={msg.avatar} alt="" />
            </Badge>
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageBubble;
