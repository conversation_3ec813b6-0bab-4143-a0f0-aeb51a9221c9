import lodash from "lodash";
import constants from "@/config/constants";
import helper from "@/helpers";
import apiClient from "@/services/apiClient";

async function bootstrap() {
  window.lodash = lodash;
  window.constants = constants;
  window.helper = helper;
  window.apiClient = apiClient;

  try {
    window.user = await window.helper.getStorageData("session");
  } catch (error) {
    window.user = {};
  }
}

export default bootstrap;