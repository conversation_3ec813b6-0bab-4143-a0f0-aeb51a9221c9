import React, { memo, useState } from "react";
import { Link, NavLink, useLocation, useNavigate } from "react-router-dom";
import {
  BellOutlined,
  MessageOutlined,
  SettingOutlined,
  MenuOutlined,
  CloseOutlined,
  HeartOutlined,
  CreditCardOutlined,
} from "@ant-design/icons";
import { Avatar, Badge, Layout } from "antd";
import CustomDropdown from "@/components/shared/dropdown";
import useSweetAlert from "@/hooks/useSweetAlert";
const { Header } = Layout;

const navItems = [
  { label: "Home", path: "/home" },
  { label: "Sphere it", path: "/sphare-it" },
  { label: "Agents", path: "/agent" },
  { label: "Listings", path: "/listing" },
  { label: "States", path: "/state" },
  { label: "Contract Q", path: "/contract" },
];

const InnerHeader = () => {
  let user = window.user;
  const [menuOpen, setMenuOpen] = useState(false);
  const navigate = useNavigate();
  const { showAlert } = useSweetAlert();

  const location = useLocation();
  const currentPath = location.pathname;

  const handleSignout = async () => {
    const result = await showAlert({
      title: "Sign Out",
      text: "Are you sure you want to sign out?",
      icon: "question",
      showCancelButton: true,
      confirmButtonText: "Yes, Sign Out",
      cancelButtonText: "Cancel",
    });

    if (result.isConfirmed) {
      // Only clear session data, preserve Remember Me
      localStorage.removeItem("session");
      window.user = {};
      window.location.replace("/login");
    }
  };

  const items = [
    {
      key: "1",
      label: (
        <Link
          to="/profile/posts"
          className={`d-flex align-items-center ${
            currentPath === "/profile/posts" ? "active-dropdown-link" : ""
          }`}
        >
          <div>
            <img
              src={
                currentPath === "/profile/posts"
                  ? "/assets/img/heart-icon.png"
                  : "/assets/img/heart-icon.png"
              }
              alt=""
            />
          </div>
          <div className="ms-2">
            <p>My Profile</p>
          </div>
        </Link>
      ),
    },
    {
      key: "2",
      label: (
        <Link
          to="/favourite"
          className={`d-flex align-items-center ${
            currentPath === "/favourite" ? "active-dropdown-link" : ""
          }`}
        >
          <div>
            <img
              src={
                currentPath === "/favourite"
                  ? "/assets/img/heart-icon.png"
                  : "/assets/img/heart-icon.png"
              }
              alt=""
            />
          </div>
          <div className="ms-2">
            <p>Favorite Properties</p>
          </div>
        </Link>
      ),
    },
    {
      key: "3",
      label: (
        <Link
          to="/subscription"
          className={`d-flex align-items-center ${
            currentPath === "/subscription" ? "active-dropdown-link" : ""
          }`}
        >
          <div>
            <img
              src={
                currentPath === "/subscription"
                  ? "/assets/img/card-icon.png"
                  : "/assets/img/card-icon.png"
              }
              alt=""
            />
          </div>
          <div className="ms-2">
            <p>Subscription</p>
          </div>
        </Link>
      ),
    },
    {
      key: "4",
      label: (
        <Link
          to="/about"
          className={`d-flex align-items-center ${
            currentPath === "/about" ? "active-dropdown-link" : ""
          }`}
        >
          <div>
            <img
              src={
                currentPath === "/about"
                  ? "/assets/img/about-icon.png"
                  : "/assets/img/about-icon.png"
              }
              alt=""
            />
          </div>
          <div className="ms-2">
            <p>About</p>
          </div>
        </Link>
      ),
    },
    {
      key: "5",
      label: (
        <Link
          to="/setting"
          className={`d-flex align-items-center ${
            currentPath === "/setting" ? "active-dropdown-link" : ""
          }`}
        >
          <div>
            <img
              src={
                currentPath === "/setting"
                  ? "/assets/img/heart-icon.png"
                  : "/assets/img/heart-icon.png"
              }
              alt=""
            />
          </div>
          <div className="ms-2">
            <p>Settings</p>
          </div>
        </Link>
      ),
    },
    {
      key: "6",
      label: (
        <div
          className="d-flex align-items-center"
          onClick={handleSignout}
          style={{ cursor: "pointer" }}
        >
          <div>
            <img src="/assets/img/logout-icon.png" alt="" />
          </div>
          <div className="ms-2">
            <p>Sign out</p>
          </div>
        </div>
      ),
    },
  ];
  return (
    <Header className="app-header">
      <div className="header-left">
        <Link to="/home">
          <img src="/assets/img/logo.png" alt="Logo" className="logo" />
        </Link>
        <div className="mobile-toggle" onClick={() => setMenuOpen(!menuOpen)}>
          {menuOpen ? <CloseOutlined /> : <MenuOutlined />}
        </div>
        <nav
          className={`nav-links d-flex align-items-center ${
            menuOpen ? "open" : ""
          }`}
        >
          {navItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              onClick={() => setMenuOpen(false)}
              className={({ isActive }) =>
                isActive ? "nav-link active" : "nav-link"
              }
            >
              {item.label}
            </NavLink>
          ))}
        </nav>
      </div>

      <div className="header-right">
        <div className="icons d-flex align-items-center">
          <div className="ms-3 ">
            <Badge
              count={5}
              style={{ backgroundColor: "#3883E2", color: "#fff" }}
            >
              <Link to="/notifications">
                <Avatar
                  shape="square"
                  size="large"
                  icon={<BellOutlined className="icon" />}
                />
              </Link>
            </Badge>
          </div>

          <div className="ms-3 me-3">
            <Badge
              count={5}
              style={{ backgroundColor: "#3883E2", color: "#fff" }}
            >
              <Link to="/inbox">
                <Avatar
                  shape="square"
                  size="large"
                  icon={<img src="/assets/img/message-icon.png" alt="" />}
                />
              </Link>
            </Badge>
          </div>
        </div>
        <CustomDropdown
          overlayClassName="profle-dropdown"
          title={
            <div className="user-info">
              <img
                src={user?.image_url}
                alt={user?.name}
                className="user-img"
              />

              <span className="user-name">{user?.name}</span>
            </div>
          }
          items={items}
        />
      </div>
    </Header>
  );
};

export default memo(InnerHeader);