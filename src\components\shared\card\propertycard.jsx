import { Link } from "react-router-dom";
import { EditOutlined, DeleteOutlined } from "@ant-design/icons";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import apiClient from "@/services/apiClient";

const PropertyCard = ({
  title,
  location,
  price,
  detail,
  bath_tub,
  bed_room,
  square_feet,
  src,
  is_favorite = false,
  showActions = false,
  onEdit = null,
  onDelete = null,
  id,
  slug,
  image,
}) => {
  const queryClient = useQueryClient();

  // Clean and simple favorite mutation
  const favoriteMutation = useMutation({
    mutationFn: async (newFavoriteStatus) => {
      return await apiClient.request("toggleFavorite", {
        slug: `${id}/favorite`,
        data: { value: newFavoriteStatus },
      });
    },
    onSuccess: () => {
      // Only invalidate queries - React Query will refetch when needed
      queryClient.invalidateQueries({
        queryKey: ["properties"],
        exact: false,
      });
      queryClient.invalidateQueries({
        queryKey: ["getProperty"],
        exact: false,
      });
    },
  });

  const handleFavoriteClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    // Prevent multiple clicks while API call is in progress
    if (favoriteMutation.isPending) return;

    const newValue = !is_favorite;
    favoriteMutation.mutate(newValue);
  };

  const handleEdit = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (onEdit) {
      onEdit({ id, slug, title });
    }
  };

  const handleDelete = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (onDelete) {
      onDelete({ id, slug, title });
    }
  };

  return (
    <Link to={src}>
      <div className="property-card border rounded overflow-hidden bg-white">
        <div className="p-card-header position-relative">
          <img
            src={image || "/assets/img/card-img.png"}
            alt={title}
            className="w-100"
            style={{ height: "200px", objectFit: "cover" }}
          />

          {/* Favorite Icon */}
          <div
            className="favorite-icon position-absolute"
            onClick={handleFavoriteClick}
            style={{
              top: "10px",
              right: "10px",
              borderRadius: "50%",
              cursor: favoriteMutation.isPending ? "not-allowed" : "pointer",
              opacity: favoriteMutation.isPending ? 0.7 : 1,
              transition: "opacity 0.2s ease",
            }}
          >
            <img
              src={
                is_favorite
                  ? "/assets/img/heart-active.png"
                  : "/assets/img/heart-icon.png"
              }
              alt="favorite"
            />
          </div>

          {/* Edit/Delete Actions */}
          {showActions && (
            <div
              className="action-buttons position-absolute"
              style={{
                top: "10px",
                left: "10px",
                display: "flex",
                gap: "8px",
              }}
            >
              <div
                onClick={handleEdit}
                style={{
                  backgroundColor: "rgba(0, 0, 0, 0.9)",
                  borderRadius: "50%",
                  width: "32px",
                  height: "32px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  cursor: "pointer",
                  color: "white",
                  border: "1px solid rgba(255, 255, 255, 0.3)",
                }}
                title="Edit Property"
              >
                <EditOutlined style={{ fontSize: "14px" }} />
              </div>
              <div
                onClick={handleDelete}
                style={{
                  backgroundColor: "rgba(220, 53, 69, 0.95)",
                  borderRadius: "50%",
                  width: "32px",
                  height: "32px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  cursor: "pointer",
                  color: "white",
                  border: "1px solid rgba(255, 255, 255, 0.3)",
                }}
                title="Delete Property"
              >
                <DeleteOutlined style={{ fontSize: "14px" }} />
              </div>
            </div>
          )}
        </div>
        <div className="">
          <div className="p-card-body">
            <h6 className="mt-3">{title}</h6>
            <p className="mt-3 mb-3"> {price}</p>
            <div className="d-flex align-items-center">
              <div>
                <img src="/assets/img/card-location.png" alt="" />
              </div>
              <div className="ms-2">
                <p>{location}</p>
              </div>
            </div>
            <p
              className="detail-para mt-2"
              style={{
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {detail}
            </p>
          </div>
          <div className="p-card-footer mt-3 mb-3">
            <div className="d-flex align-items-center">
              <div>
                <img src="/assets/img/bathtub-icon.png" alt="" />
              </div>
              <div className="ms-2">
                <p>{bath_tub}</p>
              </div>
            </div>
            <div className="d-flex align-items-center ms-3">
              <div>
                <img src="/assets/img/bed-icon.png" alt="" />
              </div>
              <div className="ms-2">
                <p>{bed_room}</p>
              </div>
            </div>
            <div className="d-flex align-items-center ms-3">
              <div>
                <img src="/assets/img/squarefoot-icon.png" alt="" />
              </div>
              <div className="ms-2">
                <p>{square_feet}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default PropertyCard;
