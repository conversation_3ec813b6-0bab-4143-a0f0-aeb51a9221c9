// components/FaqAccordion.js
import { useState } from "react";
import { Collapse } from "antd";
import { RightOutlined, DownOutlined, CaretDownFilled, CaretUpFilled } from "@ant-design/icons";



const FaqAccordion = ({items,  title ,defaultActiveKey}) => {
  return (
    <div className="mt-5">
      <p className="font-36 font-600 color-black">{title}</p>
      <Collapse
        accordion
        items={items}
         defaultActiveKey={defaultActiveKey}
        className="mt-4 my-collape"
        expandIconPosition="end"
        expandIcon={({ isActive }) =>
          isActive ? <CaretDownFilled /> : <CaretUpFilled />
        }
      />
    </div>
  );
};

export default FaqAccordion;
