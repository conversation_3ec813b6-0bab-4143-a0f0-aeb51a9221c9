import React, { memo, useEffect } from "react";
import { Form } from "antd";
import BaseInput from "../../shared/inputs";
import FlatButton from "../../shared/button/flatbutton";
import { useFetch } from "../../../hooks";
import { create_user_type } from "../../../config/rules";

const AddUserTypeForm = ({ onCancel, refreshDataTable, editData }) => {
  const [form] = Form.useForm();
  const { loading: createLoading, postData } = useFetch(
    "create_user_type",
    "submit"
  );
  const { loading: editLoading, postData: updateData } = useFetch(
    "edit_user_type",
    "submit"
  );

  // Pre-fill the form if `editData` is available (edit mode)
  useEffect(() => {
    if (editData) {
      form.setFieldsValue({
        title: editData.title, // Pre-fill with existing data
      });
    } else {
      // Reset the form if there's no edit data (Add mode)
      form.resetFields();
    }
  }, [editData, form]);

  // Form submission logic based on edit mode or create mode
  const onFinish = (values) => {
    const fd = new FormData();
    for (const key in values) {
      fd.append(key, values[key]);
    }

    if (editData) {
      // Edit mode: Hit the update API with the existing user type's ID
      updateData(fd, cbSuccess, editData._id);
    } else {
      // Create mode: Hit the create API
      postData(fd, cbSuccess);
    }
  };

  const cbSuccess = (res) => {
    if (res.code === 200) {
      onCancel();
      form.resetFields();
      refreshDataTable();
    }
  };

  return (
    <Form
      name="userTypeForm"
      layout="vertical"
      onFinish={onFinish}
      initialValues={{
        remember: true,
      }}
      form={form}
      autoComplete="off"
    >
      <BaseInput
        name="title"
        placeholder=""
        label="Type Name"
        rules={create_user_type.title}
      />
      <div className="text-end mt-4">
        <FlatButton
          title={editData ? "Update" : "Save"}
          className="add-new-btn"
          htmlType="submit"
          loading={editData ? editLoading : createLoading} // Show the correct loading state
        />
      </div>
    </Form>
  );
};

export default memo(AddUserTypeForm);
