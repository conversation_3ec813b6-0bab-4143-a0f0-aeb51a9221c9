# ShareIt Module

This module contains all components and functionality related to the ShareIt feature.

## Structure

```
sphereit/
├── index.jsx                              # Main ShareIt page
├── detail.jsx                             # Post detail page
└── README.md                              # This file
```

## Hooks

### usePostSearchFilterPagination

A custom hook that provides search, filter, and pagination functionality for posts.

**Location**: `@/hooks/usePostSearchFilterPagination.js` (Global hooks folder)

**Usage**:
```jsx
import { usePostSearchFilterPagination } from "@/hooks/usePostSearchFilterPagination";

const { 
  data, 
  isLoading, 
  pagination, 
  handlePageChange, 
  handleFilterClick 
} = usePostSearchFilterPagination({ pageSize: 12 });
```

**Features**:
- Debounced search (500ms delay)
- State, city, professional type, and recent posts filters
- Pagination with customizable page sizes
- Loading states and error handling
- Empty state detection

## Components

### ShareIt (index.jsx)

Main page component that displays a grid of posts with search, filter, and pagination functionality.

**Features**:
- Post creation input
- Search bar with filter integration
- Responsive grid layout (4 columns on desktop)
- Loading skeletons
- Empty states
- Error handling
- Pagination

### Post Detail (detail.jsx)

Individual post detail view component.

## API Integration

Uses the `postItem` endpoint from the main API configuration for fetching posts.

## Dependencies

- React Query for data fetching
- Ant Design for UI components
- Lodash for utility functions
- Context providers for search and filter state