import {
  useMutation as useReactMutation,
  useQueryClient,
} from "@tanstack/react-query";
import { useMemo, useCallback } from "react";
import apiClient from "@/services/apiClient";
import api from "@/services/api";

export function useMutation(endpoint, options = {}) {
  const {
    useFormData = true,
    showSuccessNotification = true,
    invalidateQueries = [],
    method,
    headers,
    token,
    onSuccess: userOnSuccess,
    ...mutationOptions
  } = options;
  const queryClient = useQueryClient();

  const apiEndpoint = api[endpoint];
  if (!apiEndpoint) {
    throw new Error(`API endpoint "${endpoint}" not found`);
  }

  const requestMethod = method || apiEndpoint.method;

  const mutationFn = useCallback(
    async (payload) => {
      const { data, slug } =
        payload && typeof payload === "object" && "data" in payload
          ? payload
          : { data: payload, slug: undefined };

      const isDeleteRequest = requestMethod === "DELETE";

      const response = await apiClient.request(endpoint, {
        ...(!isDeleteRequest && { data }),
        slug,
        useFormData: !isDeleteRequest && useFormData,
        showSuccessNotification,
        method: requestMethod,
        customHeaders: headers,
        token,
      });
      return response;
    },
    [endpoint, requestMethod, useFormData, showSuccessNotification, token]
  );

  const onSuccessHandler = useCallback(
    (data, variables, context) => {
      if (typeof invalidateQueries === "function") {
        invalidateQueries({ data, variables }, queryClient);
      } else {
        handleQueryInvalidation(invalidateQueries, queryClient, {
          data,
          variables,
        });
      }

      if (userOnSuccess) {
        userOnSuccess(data, variables, context);
      }
    },
    [invalidateQueries, queryClient, userOnSuccess]
  );

  return useReactMutation({
    mutationFn,
    onSuccess: onSuccessHandler,
    networkMode: "online",
    ...mutationOptions,
  });
}

function handleQueryInvalidation(
  invalidateQueries,
  queryClient,
  { data, variables }
) {
  if (!invalidateQueries) return;

  try {
    let queriesToInvalidate = [];

    if (typeof invalidateQueries === "function") {
      queriesToInvalidate = invalidateQueries({ data, variables });
    }
    else if (Array.isArray(invalidateQueries)) {
      queriesToInvalidate = invalidateQueries;
    }

    if (queriesToInvalidate.length > 0) {
      queriesToInvalidate.forEach((queryItem) => {
        if (queryItem) {
          if (typeof queryItem === "object" && queryItem.queryKey) {
            const invalidateOptions = {
              queryKey: Array.isArray(queryItem.queryKey)
                ? queryItem.queryKey
                : [queryItem.queryKey],
            };

            if (queryItem.exact !== undefined) {
              invalidateOptions.exact = queryItem.exact;
            }

            if (queryItem.type === "paginated") {
              invalidateOptions.predicate = (query) => {
                const queryKey = query.queryKey;
                const targetKey = invalidateOptions.queryKey;
                return (
                  queryKey[0] === targetKey[0] &&
                  (queryKey.length > 1 ||
                    queryKey.some(
                      (key) => typeof key === "string" && key.includes('"page"')
                    ))
                );
              };
              delete invalidateOptions.exact;
            }

            queryClient.invalidateQueries(invalidateOptions);
          }
          else {
            queryClient.invalidateQueries({
              queryKey: Array.isArray(queryItem) ? queryItem : [queryItem],
            });
          }
        }
      });
    }
  } catch (error) {
  }
}

export default useMutation;