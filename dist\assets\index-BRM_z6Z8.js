import{j as a}from"./index-3mE9H3a0.js";import{I as t}from"./index-C-I6oq0X.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";import"./index-BUUUOhAK.js";const r=({avatar:s,message:i,time:e})=>a.jsxs("div",{className:"not-box d-flex align-items-center",children:[a.jsx("div",{className:"notifoication-avatar",children:a.jsx("img",{src:s,alt:"avatar"})}),a.jsxs("div",{className:"ms-2",children:[a.jsx("p",{children:i}),a.jsx("p",{children:e})]})]}),n=[{avatar:"/assets/img/avatar.png",message:"<PERSON> started following you.",time:"5 days ago"},{avatar:"/assets/img/avatar.png",message:"<PERSON><PERSON><PERSON> liked your post.",time:"2 days ago"},{avatar:"/assets/img/avatar.png",message:"<PERSON> mentioned you in a comment.",time:"1 day ago"}],f=()=>a.jsx("div",{children:a.jsx(t,{children:a.jsx("div",{className:"container-fluid",children:a.jsxs("div",{className:"row",children:[a.jsx("div",{className:"col-12 mt-4",children:a.jsx("p",{className:"font-36 color-black font-600 ",children:"Notifications"})}),a.jsx("div",{className:"col-12",children:n.map((s,i)=>a.jsx(r,{avatar:s.avatar,message:s.message,time:s.time},i))})]})})})});export{f as default};
