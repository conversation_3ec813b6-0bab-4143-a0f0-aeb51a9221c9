import{r as f,aC as $n,aD as xn,aE as Ht,aG as Ut,b3 as Mn,a$ as da,ax as Vn,k as S,bk as an,i as xe,e as Ne,U as In,f as q,X as rt,aV as Tn,bl as Ba,ab as X,W as _r,M as j,aQ as $e,bm as va,bn as dn,aK as ma,bo as Ka,J as gt,bp as Ga,bq as ha,br as Za,h as C,aF as K,aA as lr,aB as at,bs as rr,b0 as Ot,b1 as mt,at as An,F as nt,R as Nr,a3 as Qa,N as ga,g as Oe,au as Ja,G as pa,aO as Xa,bt as Ya,bu as vn,bv as mn,I as ei,v as ya,o as or,l as ti,m as ba,n as ri,w as ni,b2 as ai,bw as ii,a0 as oi,P as si,a1 as li}from"./index-3mE9H3a0.js";import{t as wa,P as kr,o as ui,N as ci,i as fi}from"./button-CMBVME-6.js";var hn=f.createContext(null);function di(r){var t=r.children,e=r.onBatchResize,a=f.useRef(0),n=f.useRef([]),i=f.useContext(hn),o=f.useCallback(function(s,l,c){a.current+=1;var u=a.current;n.current.push({size:s,element:l,data:c}),Promise.resolve().then(function(){u===a.current&&(e==null||e(n.current),n.current=[])}),i==null||i(s,l,c)},[e,i]);return f.createElement(hn.Provider,{value:o},t)}var Ca=function(){if(typeof Map<"u")return Map;function r(t,e){var a=-1;return t.some(function(n,i){return n[0]===e?(a=i,!0):!1}),a}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(e){var a=r(this.__entries__,e),n=this.__entries__[a];return n&&n[1]},t.prototype.set=function(e,a){var n=r(this.__entries__,e);~n?this.__entries__[n][1]=a:this.__entries__.push([e,a])},t.prototype.delete=function(e){var a=this.__entries__,n=r(a,e);~n&&a.splice(n,1)},t.prototype.has=function(e){return!!~r(this.__entries__,e)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,a){a===void 0&&(a=null);for(var n=0,i=this.__entries__;n<i.length;n++){var o=i[n];e.call(a,o[1],o[0])}},t}()}(),gn=typeof window<"u"&&typeof document<"u"&&window.document===document,Ir=function(){return typeof global<"u"&&global.Math===Math?global:typeof self<"u"&&self.Math===Math?self:typeof window<"u"&&window.Math===Math?window:Function("return this")()}(),vi=function(){return typeof requestAnimationFrame=="function"?requestAnimationFrame.bind(Ir):function(r){return setTimeout(function(){return r(Date.now())},1e3/60)}}(),mi=2;function hi(r,t){var e=!1,a=!1,n=0;function i(){e&&(e=!1,r()),a&&s()}function o(){vi(i)}function s(){var l=Date.now();if(e){if(l-n<mi)return;a=!0}else e=!0,a=!1,setTimeout(o,t);n=l}return s}var gi=20,pi=["top","right","bottom","left","width","height","size","weight"],yi=typeof MutationObserver<"u",bi=function(){function r(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=hi(this.refresh.bind(this),gi)}return r.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},r.prototype.removeObserver=function(t){var e=this.observers_,a=e.indexOf(t);~a&&e.splice(a,1),!e.length&&this.connected_&&this.disconnect_()},r.prototype.refresh=function(){var t=this.updateObservers_();t&&this.refresh()},r.prototype.updateObservers_=function(){var t=this.observers_.filter(function(e){return e.gatherActive(),e.hasActive()});return t.forEach(function(e){return e.broadcastActive()}),t.length>0},r.prototype.connect_=function(){!gn||this.connected_||(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),yi?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},r.prototype.disconnect_=function(){!gn||!this.connected_||(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},r.prototype.onTransitionEnd_=function(t){var e=t.propertyName,a=e===void 0?"":e,n=pi.some(function(i){return!!~a.indexOf(i)});n&&this.refresh()},r.getInstance=function(){return this.instance_||(this.instance_=new r),this.instance_},r.instance_=null,r}(),Fa=function(r,t){for(var e=0,a=Object.keys(t);e<a.length;e++){var n=a[e];Object.defineProperty(r,n,{value:t[n],enumerable:!1,writable:!1,configurable:!0})}return r},Wt=function(r){var t=r&&r.ownerDocument&&r.ownerDocument.defaultView;return t||Ir},Ea=Dr(0,0,0,0);function Tr(r){return parseFloat(r)||0}function Ln(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return t.reduce(function(a,n){var i=r["border-"+n+"-width"];return a+Tr(i)},0)}function wi(r){for(var t=["top","right","bottom","left"],e={},a=0,n=t;a<n.length;a++){var i=n[a],o=r["padding-"+i];e[i]=Tr(o)}return e}function Ci(r){var t=r.getBBox();return Dr(0,0,t.width,t.height)}function Fi(r){var t=r.clientWidth,e=r.clientHeight;if(!t&&!e)return Ea;var a=Wt(r).getComputedStyle(r),n=wi(a),i=n.left+n.right,o=n.top+n.bottom,s=Tr(a.width),l=Tr(a.height);if(a.boxSizing==="border-box"&&(Math.round(s+i)!==t&&(s-=Ln(a,"left","right")+i),Math.round(l+o)!==e&&(l-=Ln(a,"top","bottom")+o)),!Pi(r)){var c=Math.round(s+i)-t,u=Math.round(l+o)-e;Math.abs(c)!==1&&(s-=c),Math.abs(u)!==1&&(l-=u)}return Dr(n.left,n.top,s,l)}var Ei=function(){return typeof SVGGraphicsElement<"u"?function(r){return r instanceof Wt(r).SVGGraphicsElement}:function(r){return r instanceof Wt(r).SVGElement&&typeof r.getBBox=="function"}}();function Pi(r){return r===Wt(r).document.documentElement}function Oi(r){return gn?Ei(r)?Ci(r):Fi(r):Ea}function Ri(r){var t=r.x,e=r.y,a=r.width,n=r.height,i=typeof DOMRectReadOnly<"u"?DOMRectReadOnly:Object,o=Object.create(i.prototype);return Fa(o,{x:t,y:e,width:a,height:n,top:e,right:t+a,bottom:n+e,left:t}),o}function Dr(r,t,e,a){return{x:r,y:t,width:e,height:a}}var Si=function(){function r(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=Dr(0,0,0,0),this.target=t}return r.prototype.isActive=function(){var t=Oi(this.target);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},r.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t},r}(),$i=function(){function r(t,e){var a=Ri(e);Fa(this,{target:t,contentRect:a})}return r}(),xi=function(){function r(t,e,a){if(this.activeObservations_=[],this.observations_=new Ca,typeof t!="function")throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=t,this.controller_=e,this.callbackCtx_=a}return r.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(t instanceof Wt(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)||(e.set(t,new Si(t)),this.controller_.addObserver(this),this.controller_.refresh())}},r.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(t instanceof Wt(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)&&(e.delete(t),e.size||this.controller_.removeObserver(this))}},r.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},r.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach(function(e){e.isActive()&&t.activeObservations_.push(e)})},r.prototype.broadcastActive=function(){if(this.hasActive()){var t=this.callbackCtx_,e=this.activeObservations_.map(function(a){return new $i(a.target,a.broadcastRect())});this.callback_.call(t,e,t),this.clearActive()}},r.prototype.clearActive=function(){this.activeObservations_.splice(0)},r.prototype.hasActive=function(){return this.activeObservations_.length>0},r}(),Pa=typeof WeakMap<"u"?new WeakMap:new Ca,Oa=function(){function r(t){if(!(this instanceof r))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var e=bi.getInstance(),a=new xi(t,e,this);Pa.set(this,a)}return r}();["observe","unobserve","disconnect"].forEach(function(r){Oa.prototype[r]=function(){var t;return(t=Pa.get(this))[r].apply(t,arguments)}});var Mi=function(){return typeof Ir.ResizeObserver<"u"?Ir.ResizeObserver:Oa}(),ht=new Map;function Vi(r){r.forEach(function(t){var e,a=t.target;(e=ht.get(a))===null||e===void 0||e.forEach(function(n){return n(a)})})}var Ra=new Mi(Vi);function _i(r,t){ht.has(r)||(ht.set(r,new Set),Ra.observe(r)),ht.get(r).add(t)}function Ni(r,t){ht.has(r)&&(ht.get(r).delete(t),ht.get(r).size||(Ra.unobserve(r),ht.delete(r)))}var ki=function(r){$n(e,r);var t=xn(e);function e(){return Ht(this,e),t.apply(this,arguments)}return Ut(e,[{key:"render",value:function(){return this.props.children}}]),e}(f.Component);function Ii(r,t){var e=r.children,a=r.disabled,n=f.useRef(null),i=f.useRef(null),o=f.useContext(hn),s=typeof e=="function",l=s?e(n):e,c=f.useRef({width:-1,height:-1,offsetWidth:-1,offsetHeight:-1}),u=!s&&f.isValidElement(l)&&Mn(l),v=u?da(l):null,h=Vn(v,n),y=function(){var b;return an(n.current)||(n.current&&xe(n.current)==="object"?an((b=n.current)===null||b===void 0?void 0:b.nativeElement):null)||an(i.current)};f.useImperativeHandle(t,function(){return y()});var m=f.useRef(r);m.current=r;var p=f.useCallback(function(d){var b=m.current,g=b.onResize,w=b.data,P=d.getBoundingClientRect(),E=P.width,F=P.height,R=d.offsetWidth,x=d.offsetHeight,O=Math.floor(E),$=Math.floor(F);if(c.current.width!==O||c.current.height!==$||c.current.offsetWidth!==R||c.current.offsetHeight!==x){var V={width:O,height:$,offsetWidth:R,offsetHeight:x};c.current=V;var L=R===Math.round(E)?E:R,_=x===Math.round(F)?F:x,k=S(S({},V),{},{offsetWidth:L,offsetHeight:_});o==null||o(k,d,w),g&&Promise.resolve().then(function(){g(k,d)})}},[]);return f.useEffect(function(){var d=y();return d&&!a&&_i(d,p),function(){return Ni(d,p)}},[n.current,a]),f.createElement(ki,{ref:i},u?f.cloneElement(l,{ref:h}):l)}var Ti=f.forwardRef(Ii),Ai="rc-observer-key";function Li(r,t){var e=r.children,a=typeof e=="function"?[e]:wa(e);return a.map(function(n,i){var o=(n==null?void 0:n.key)||"".concat(Ai,"-").concat(i);return f.createElement(Ti,Ne({},r,{key:o,ref:i===0?t:void 0}),n)})}var ur=f.forwardRef(Li);ur.Collection=di;function on(r){return r!==void 0}function Sa(r,t){var e=t||{},a=e.defaultValue,n=e.value,i=e.onChange,o=e.postState,s=In(function(){return on(n)?n:on(a)?typeof a=="function"?a():a:typeof r=="function"?r():r}),l=q(s,2),c=l[0],u=l[1],v=n!==void 0?n:c,h=o?o(v):v,y=rt(i),m=In([v]),p=q(m,2),d=p[0],b=p[1];Tn(function(){var w=d[0];c!==w&&y(c,w)},[d]),Tn(function(){on(n)||u(n)},[n]);var g=rt(function(w,P){u(w,P),b([v],P)});return[h,g]}function Di(r,t){return kr.reduce((e,a)=>{const n=r[`${a}1`],i=r[`${a}3`],o=r[`${a}6`],s=r[`${a}7`];return Object.assign(Object.assign({},e),t(a,{lightColor:n,lightBorderColor:i,darkColor:o,textColor:s}))},{})}const sn=()=>({height:0,opacity:0}),Dn=r=>{const{scrollHeight:t}=r;return{height:t,opacity:1}},zi=r=>({height:r?r.offsetHeight:0}),ln=(r,t)=>(t==null?void 0:t.deadline)===!0||t.propertyName==="height",$l=function(){return{motionName:`${arguments.length>0&&arguments[0]!==void 0?arguments[0]:Ba}-motion-collapse`,onAppearStart:sn,onEnterStart:sn,onAppearActive:Dn,onEnterActive:Dn,onLeaveStart:zi,onLeaveActive:sn,onAppearEnd:ln,onEnterEnd:ln,onLeaveEnd:ln,motionDeadline:500}},ji=(r,t,e)=>e!==void 0?e:`${r}-${t}`,xl=r=>({[r.componentCls]:{[`${r.antCls}-motion-collapse-legacy`]:{overflow:"hidden","&-active":{transition:`height ${r.motionDurationMid} ${r.motionEaseInOut},
        opacity ${r.motionDurationMid} ${r.motionEaseInOut} !important`}},[`${r.antCls}-motion-collapse`]:{overflow:"hidden",transition:`height ${r.motionDurationMid} ${r.motionEaseInOut},
        opacity ${r.motionDurationMid} ${r.motionEaseInOut} !important`}}}),Wi=r=>({animationDuration:r,animationFillMode:"both"}),qi=r=>({animationDuration:r,animationFillMode:"both"}),_n=function(r,t,e,a){const i=(arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1)?"&":"";return{[`
      ${i}${r}-enter,
      ${i}${r}-appear
    `]:Object.assign(Object.assign({},Wi(a)),{animationPlayState:"paused"}),[`${i}${r}-leave`]:Object.assign(Object.assign({},qi(a)),{animationPlayState:"paused"}),[`
      ${i}${r}-enter${r}-enter-active,
      ${i}${r}-appear${r}-appear-active
    `]:{animationName:t,animationPlayState:"running"},[`${i}${r}-leave${r}-leave-active`]:{animationName:e,animationPlayState:"running",pointerEvents:"none"}}},Hi=new X("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),Ui=new X("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),Bi=new X("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),Ki=new X("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),Gi=new X("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),Zi=new X("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),Qi=new X("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),Ji=new X("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}}),Xi={"move-up":{inKeyframes:Qi,outKeyframes:Ji},"move-down":{inKeyframes:Hi,outKeyframes:Ui},"move-left":{inKeyframes:Bi,outKeyframes:Ki},"move-right":{inKeyframes:Gi,outKeyframes:Zi}},Ml=(r,t)=>{const{antCls:e}=r,a=`${e}-${t}`,{inKeyframes:n,outKeyframes:i}=Xi[t];return[_n(a,n,i,r.motionDurationMid),{[`
        ${a}-enter,
        ${a}-appear
      `]:{opacity:0,animationTimingFunction:r.motionEaseOutCirc},[`${a}-leave`]:{animationTimingFunction:r.motionEaseInOutCirc}}]},Yi=new X("antSlideUpIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1}}),eo=new X("antSlideUpOut",{"0%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0}}),to=new X("antSlideDownIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1}}),ro=new X("antSlideDownOut",{"0%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0}}),no=new X("antSlideLeftIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1}}),ao=new X("antSlideLeftOut",{"0%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0}}),io=new X("antSlideRightIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1}}),oo=new X("antSlideRightOut",{"0%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0}}),so={"slide-up":{inKeyframes:Yi,outKeyframes:eo},"slide-down":{inKeyframes:to,outKeyframes:ro},"slide-left":{inKeyframes:no,outKeyframes:ao},"slide-right":{inKeyframes:io,outKeyframes:oo}},Vl=(r,t)=>{const{antCls:e}=r,a=`${e}-${t}`,{inKeyframes:n,outKeyframes:i}=so[t];return[_n(a,n,i,r.motionDurationMid),{[`
      ${a}-enter,
      ${a}-appear
    `]:{transform:"scale(0)",transformOrigin:"0% 0%",opacity:0,animationTimingFunction:r.motionEaseOutQuint,"&-prepare":{transform:"scale(1)"}},[`${a}-leave`]:{animationTimingFunction:r.motionEaseInQuint}}]},lo=new X("antZoomIn",{"0%":{transform:"scale(0.2)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),uo=new X("antZoomOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.2)",opacity:0}}),zn=new X("antZoomBigIn",{"0%":{transform:"scale(0.8)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),jn=new X("antZoomBigOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.8)",opacity:0}}),co=new X("antZoomUpIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 0%"}}),fo=new X("antZoomUpOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 0%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0}}),vo=new X("antZoomLeftIn",{"0%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"0% 50%"}}),mo=new X("antZoomLeftOut",{"0%":{transform:"scale(1)",transformOrigin:"0% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0}}),ho=new X("antZoomRightIn",{"0%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"100% 50%"}}),go=new X("antZoomRightOut",{"0%":{transform:"scale(1)",transformOrigin:"100% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0}}),po=new X("antZoomDownIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 100%"}}),yo=new X("antZoomDownOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 100%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0}}),bo={zoom:{inKeyframes:lo,outKeyframes:uo},"zoom-big":{inKeyframes:zn,outKeyframes:jn},"zoom-big-fast":{inKeyframes:zn,outKeyframes:jn},"zoom-left":{inKeyframes:vo,outKeyframes:mo},"zoom-right":{inKeyframes:ho,outKeyframes:go},"zoom-up":{inKeyframes:co,outKeyframes:fo},"zoom-down":{inKeyframes:po,outKeyframes:yo}},wo=(r,t)=>{const{antCls:e}=r,a=`${e}-${t}`,{inKeyframes:n,outKeyframes:i}=bo[t];return[_n(a,n,i,t==="zoom-big-fast"?r.motionDurationFast:r.motionDurationMid),{[`
        ${a}-enter,
        ${a}-appear
      `]:{transform:"scale(0)",opacity:0,animationTimingFunction:r.motionEaseOutCirc,"&-prepare":{transform:"none"}},[`${a}-leave`]:{animationTimingFunction:r.motionEaseInOutCirc}}]};var $a=f.createContext(null),Wn=[];function Co(r,t){var e=f.useState(function(){if(!_r())return null;var m=document.createElement("div");return m}),a=q(e,1),n=a[0],i=f.useRef(!1),o=f.useContext($a),s=f.useState(Wn),l=q(s,2),c=l[0],u=l[1],v=o||(i.current?void 0:function(m){u(function(p){var d=[m].concat(j(p));return d})});function h(){n.parentElement||document.body.appendChild(n),i.current=!0}function y(){var m;(m=n.parentElement)===null||m===void 0||m.removeChild(n),i.current=!1}return $e(function(){return r?o?o(h):h():y(),y},[r]),$e(function(){c.length&&(c.forEach(function(m){return m()}),u(Wn))},[c]),[n,v]}function Fo(r){var t="rc-scrollbar-measure-".concat(Math.random().toString(36).substring(7)),e=document.createElement("div");e.id=t;var a=e.style;a.position="absolute",a.left="0",a.top="0",a.width="100px",a.height="100px",a.overflow="scroll";var n,i;if(r){var o=getComputedStyle(r);a.scrollbarColor=o.scrollbarColor,a.scrollbarWidth=o.scrollbarWidth;var s=getComputedStyle(r,"::-webkit-scrollbar"),l=parseInt(s.width,10),c=parseInt(s.height,10);try{var u=l?"width: ".concat(s.width,";"):"",v=c?"height: ".concat(s.height,";"):"";va(`
#`.concat(t,`::-webkit-scrollbar {
`).concat(u,`
`).concat(v,`
}`),t)}catch(m){console.error(m),n=l,i=c}}document.body.appendChild(e);var h=r&&n&&!isNaN(n)?n:e.offsetWidth-e.clientWidth,y=r&&i&&!isNaN(i)?i:e.offsetHeight-e.clientHeight;return document.body.removeChild(e),dn(t),{width:h,height:y}}function Eo(r){return typeof document>"u"||!r||!(r instanceof Element)?{width:0,height:0}:Fo(r)}function Po(){return document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)&&window.innerWidth>document.body.offsetWidth}var Oo="rc-util-locker-".concat(Date.now()),qn=0;function Ro(r){var t=!!r,e=f.useState(function(){return qn+=1,"".concat(Oo,"_").concat(qn)}),a=q(e,1),n=a[0];$e(function(){if(t){var i=Eo(document.body).width,o=Po();va(`
html body {
  overflow-y: hidden;
  `.concat(o?"width: calc(100% - ".concat(i,"px);"):"",`
}`),n)}else dn(n);return function(){dn(n)}},[t,n])}var So=!1;function $o(r){return So}var Hn=function(t){return t===!1?!1:!_r()||!t?null:typeof t=="string"?document.querySelector(t):typeof t=="function"?t():t},xa=f.forwardRef(function(r,t){var e=r.open,a=r.autoLock,n=r.getContainer;r.debug;var i=r.autoDestroy,o=i===void 0?!0:i,s=r.children,l=f.useState(e),c=q(l,2),u=c[0],v=c[1],h=u||e;f.useEffect(function(){(o||e)&&v(e)},[e,o]);var y=f.useState(function(){return Hn(n)}),m=q(y,2),p=m[0],d=m[1];f.useEffect(function(){var V=Hn(n);d(V??null)});var b=Co(h&&!p),g=q(b,2),w=g[0],P=g[1],E=p??w;Ro(a&&e&&_r()&&(E===w||E===document.body));var F=null;if(s&&Mn(s)&&t){var R=s;F=R.ref}var x=Vn(F,t);if(!h||!_r()||p===void 0)return null;var O=E===!1||$o(),$=s;return t&&($=f.cloneElement(s,{ref:x})),f.createElement($a.Provider,{value:P},O?$:ma.createPortal($,E))});function xo(){var r=S({},Ka);return r.useId}var Un=0,Bn=xo();const Ma=Bn?function(t){var e=Bn();return t||e}:function(t){var e=f.useState("ssr-id"),a=q(e,2),n=a[0],i=a[1];return f.useEffect(function(){var o=Un;Un+=1,i("rc_unique_".concat(o))},[]),t||n};var Rt="RC_FORM_INTERNAL_HOOKS",J=function(){gt(!1,"Can not find FormContext. Please make sure you wrap Field under Form.")},qt=f.createContext({getFieldValue:J,getFieldsValue:J,getFieldError:J,getFieldWarning:J,getFieldsError:J,isFieldsTouched:J,isFieldTouched:J,isFieldValidating:J,isFieldsValidating:J,resetFields:J,setFields:J,setFieldValue:J,setFieldsValue:J,validateFields:J,submit:J,getInternalHooks:function(){return J(),{dispatch:J,initEntityValue:J,registerField:J,useSubscribe:J,setInitialValues:J,destroyForm:J,setCallbacks:J,registerWatch:J,getFields:J,setValidateMessages:J,setPreserve:J,getInitialValue:J}}}),Ar=f.createContext(null);function pn(r){return r==null?[]:Array.isArray(r)?r:[r]}function Mo(r){return r&&!!r._init}function yn(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var t=JSON.parse(JSON.stringify(this));return t.clone=this.clone,t}}}var bn=yn();function Vo(r){try{return Function.toString.call(r).indexOf("[native code]")!==-1}catch{return typeof r=="function"}}function _o(r,t,e){if(Ga())return Reflect.construct.apply(null,arguments);var a=[null];a.push.apply(a,t);var n=new(r.bind.apply(r,a));return e&&ha(n,e.prototype),n}function wn(r){var t=typeof Map=="function"?new Map:void 0;return wn=function(a){if(a===null||!Vo(a))return a;if(typeof a!="function")throw new TypeError("Super expression must either be null or a function");if(t!==void 0){if(t.has(a))return t.get(a);t.set(a,n)}function n(){return _o(a,arguments,Za(this).constructor)}return n.prototype=Object.create(a.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),ha(n,a)},wn(r)}var No=/%[sdj%]/g,ko=function(){};function Cn(r){if(!r||!r.length)return null;var t={};return r.forEach(function(e){var a=e.field;t[a]=t[a]||[],t[a].push(e)}),t}function je(r){for(var t=arguments.length,e=new Array(t>1?t-1:0),a=1;a<t;a++)e[a-1]=arguments[a];var n=0,i=e.length;if(typeof r=="function")return r.apply(null,e);if(typeof r=="string"){var o=r.replace(No,function(s){if(s==="%%")return"%";if(n>=i)return s;switch(s){case"%s":return String(e[n++]);case"%d":return Number(e[n++]);case"%j":try{return JSON.stringify(e[n++])}catch{return"[Circular]"}break;default:return s}});return o}return r}function Io(r){return r==="string"||r==="url"||r==="hex"||r==="email"||r==="date"||r==="pattern"}function pe(r,t){return!!(r==null||t==="array"&&Array.isArray(r)&&!r.length||Io(t)&&typeof r=="string"&&!r)}function To(r,t,e){var a=[],n=0,i=r.length;function o(s){a.push.apply(a,j(s||[])),n++,n===i&&e(a)}r.forEach(function(s){t(s,o)})}function Kn(r,t,e){var a=0,n=r.length;function i(o){if(o&&o.length){e(o);return}var s=a;a=a+1,s<n?t(r[s],i):e([])}i([])}function Ao(r){var t=[];return Object.keys(r).forEach(function(e){t.push.apply(t,j(r[e]||[]))}),t}var Gn=function(r){$n(e,r);var t=xn(e);function e(a,n){var i;return Ht(this,e),i=t.call(this,"Async Validation Error"),C(K(i),"errors",void 0),C(K(i),"fields",void 0),i.errors=a,i.fields=n,i}return Ut(e)}(wn(Error));function Lo(r,t,e,a,n){if(t.first){var i=new Promise(function(h,y){var m=function(b){return a(b),b.length?y(new Gn(b,Cn(b))):h(n)},p=Ao(r);Kn(p,e,m)});return i.catch(function(h){return h}),i}var o=t.firstFields===!0?Object.keys(r):t.firstFields||[],s=Object.keys(r),l=s.length,c=0,u=[],v=new Promise(function(h,y){var m=function(d){if(u.push.apply(u,d),c++,c===l)return a(u),u.length?y(new Gn(u,Cn(u))):h(n)};s.length||(a(u),h(n)),s.forEach(function(p){var d=r[p];o.indexOf(p)!==-1?Kn(d,e,m):To(d,e,m)})});return v.catch(function(h){return h}),v}function Do(r){return!!(r&&r.message!==void 0)}function zo(r,t){for(var e=r,a=0;a<t.length;a++){if(e==null)return e;e=e[t[a]]}return e}function Zn(r,t){return function(e){var a;return r.fullFields?a=zo(t,r.fullFields):a=t[e.field||r.fullField],Do(e)?(e.field=e.field||r.fullField,e.fieldValue=a,e):{message:typeof e=="function"?e():e,fieldValue:a,field:e.field||r.fullField}}}function Qn(r,t){if(t){for(var e in t)if(t.hasOwnProperty(e)){var a=t[e];xe(a)==="object"&&xe(r[e])==="object"?r[e]=S(S({},r[e]),a):r[e]=a}}return r}var It="enum",jo=function(t,e,a,n,i){t[It]=Array.isArray(t[It])?t[It]:[],t[It].indexOf(e)===-1&&n.push(je(i.messages[It],t.fullField,t[It].join(", ")))},Wo=function(t,e,a,n,i){if(t.pattern){if(t.pattern instanceof RegExp)t.pattern.lastIndex=0,t.pattern.test(e)||n.push(je(i.messages.pattern.mismatch,t.fullField,e,t.pattern));else if(typeof t.pattern=="string"){var o=new RegExp(t.pattern);o.test(e)||n.push(je(i.messages.pattern.mismatch,t.fullField,e,t.pattern))}}},qo=function(t,e,a,n,i){var o=typeof t.len=="number",s=typeof t.min=="number",l=typeof t.max=="number",c=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,u=e,v=null,h=typeof e=="number",y=typeof e=="string",m=Array.isArray(e);if(h?v="number":y?v="string":m&&(v="array"),!v)return!1;m&&(u=e.length),y&&(u=e.replace(c,"_").length),o?u!==t.len&&n.push(je(i.messages[v].len,t.fullField,t.len)):s&&!l&&u<t.min?n.push(je(i.messages[v].min,t.fullField,t.min)):l&&!s&&u>t.max?n.push(je(i.messages[v].max,t.fullField,t.max)):s&&l&&(u<t.min||u>t.max)&&n.push(je(i.messages[v].range,t.fullField,t.min,t.max))},Va=function(t,e,a,n,i,o){t.required&&(!a.hasOwnProperty(t.field)||pe(e,o||t.type))&&n.push(je(i.messages.required,t.fullField))},Mr;const Ho=function(){if(Mr)return Mr;var r="[a-fA-F\\d:]",t=function(F){return F&&F.includeBoundaries?"(?:(?<=\\s|^)(?=".concat(r,")|(?<=").concat(r,")(?=\\s|$))"):""},e="(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",a="[a-fA-F\\d]{1,4}",n=["(?:".concat(a,":){7}(?:").concat(a,"|:)"),"(?:".concat(a,":){6}(?:").concat(e,"|:").concat(a,"|:)"),"(?:".concat(a,":){5}(?::").concat(e,"|(?::").concat(a,"){1,2}|:)"),"(?:".concat(a,":){4}(?:(?::").concat(a,"){0,1}:").concat(e,"|(?::").concat(a,"){1,3}|:)"),"(?:".concat(a,":){3}(?:(?::").concat(a,"){0,2}:").concat(e,"|(?::").concat(a,"){1,4}|:)"),"(?:".concat(a,":){2}(?:(?::").concat(a,"){0,3}:").concat(e,"|(?::").concat(a,"){1,5}|:)"),"(?:".concat(a,":){1}(?:(?::").concat(a,"){0,4}:").concat(e,"|(?::").concat(a,"){1,6}|:)"),"(?::(?:(?::".concat(a,"){0,5}:").concat(e,"|(?::").concat(a,"){1,7}|:))")],i="(?:%[0-9a-zA-Z]{1,})?",o="(?:".concat(n.join("|"),")").concat(i),s=new RegExp("(?:^".concat(e,"$)|(?:^").concat(o,"$)")),l=new RegExp("^".concat(e,"$")),c=new RegExp("^".concat(o,"$")),u=function(F){return F&&F.exact?s:new RegExp("(?:".concat(t(F)).concat(e).concat(t(F),")|(?:").concat(t(F)).concat(o).concat(t(F),")"),"g")};u.v4=function(E){return E&&E.exact?l:new RegExp("".concat(t(E)).concat(e).concat(t(E)),"g")},u.v6=function(E){return E&&E.exact?c:new RegExp("".concat(t(E)).concat(o).concat(t(E)),"g")};var v="(?:(?:[a-z]+:)?//)",h="(?:\\S+(?::\\S*)?@)?",y=u.v4().source,m=u.v6().source,p="(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)",d="(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*",b="(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))",g="(?::\\d{2,5})?",w='(?:[/?#][^\\s"]*)?',P="(?:".concat(v,"|www\\.)").concat(h,"(?:localhost|").concat(y,"|").concat(m,"|").concat(p).concat(d).concat(b,")").concat(g).concat(w);return Mr=new RegExp("(?:^".concat(P,"$)"),"i"),Mr};var Jn={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i},nr={integer:function(t){return nr.number(t)&&parseInt(t,10)===t},float:function(t){return nr.number(t)&&!nr.integer(t)},array:function(t){return Array.isArray(t)},regexp:function(t){if(t instanceof RegExp)return!0;try{return!!new RegExp(t)}catch{return!1}},date:function(t){return typeof t.getTime=="function"&&typeof t.getMonth=="function"&&typeof t.getYear=="function"&&!isNaN(t.getTime())},number:function(t){return isNaN(t)?!1:typeof t=="number"},object:function(t){return xe(t)==="object"&&!nr.array(t)},method:function(t){return typeof t=="function"},email:function(t){return typeof t=="string"&&t.length<=320&&!!t.match(Jn.email)},url:function(t){return typeof t=="string"&&t.length<=2048&&!!t.match(Ho())},hex:function(t){return typeof t=="string"&&!!t.match(Jn.hex)}},Uo=function(t,e,a,n,i){if(t.required&&e===void 0){Va(t,e,a,n,i);return}var o=["integer","float","array","regexp","object","method","email","number","date","url","hex"],s=t.type;o.indexOf(s)>-1?nr[s](e)||n.push(je(i.messages.types[s],t.fullField,t.type)):s&&xe(e)!==t.type&&n.push(je(i.messages.types[s],t.fullField,t.type))},Bo=function(t,e,a,n,i){(/^\s+$/.test(e)||e==="")&&n.push(je(i.messages.whitespace,t.fullField))};const H={required:Va,whitespace:Bo,type:Uo,range:qo,enum:jo,pattern:Wo};var Ko=function(t,e,a,n,i){var o=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(pe(e)&&!t.required)return a();H.required(t,e,n,o,i)}a(o)},Go=function(t,e,a,n,i){var o=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(e==null&&!t.required)return a();H.required(t,e,n,o,i,"array"),e!=null&&(H.type(t,e,n,o,i),H.range(t,e,n,o,i))}a(o)},Zo=function(t,e,a,n,i){var o=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(pe(e)&&!t.required)return a();H.required(t,e,n,o,i),e!==void 0&&H.type(t,e,n,o,i)}a(o)},Qo=function(t,e,a,n,i){var o=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(pe(e,"date")&&!t.required)return a();if(H.required(t,e,n,o,i),!pe(e,"date")){var l;e instanceof Date?l=e:l=new Date(e),H.type(t,l,n,o,i),l&&H.range(t,l.getTime(),n,o,i)}}a(o)},Jo="enum",Xo=function(t,e,a,n,i){var o=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(pe(e)&&!t.required)return a();H.required(t,e,n,o,i),e!==void 0&&H[Jo](t,e,n,o,i)}a(o)},Yo=function(t,e,a,n,i){var o=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(pe(e)&&!t.required)return a();H.required(t,e,n,o,i),e!==void 0&&(H.type(t,e,n,o,i),H.range(t,e,n,o,i))}a(o)},es=function(t,e,a,n,i){var o=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(pe(e)&&!t.required)return a();H.required(t,e,n,o,i),e!==void 0&&(H.type(t,e,n,o,i),H.range(t,e,n,o,i))}a(o)},ts=function(t,e,a,n,i){var o=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(pe(e)&&!t.required)return a();H.required(t,e,n,o,i),e!==void 0&&H.type(t,e,n,o,i)}a(o)},rs=function(t,e,a,n,i){var o=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(e===""&&(e=void 0),pe(e)&&!t.required)return a();H.required(t,e,n,o,i),e!==void 0&&(H.type(t,e,n,o,i),H.range(t,e,n,o,i))}a(o)},ns=function(t,e,a,n,i){var o=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(pe(e)&&!t.required)return a();H.required(t,e,n,o,i),e!==void 0&&H.type(t,e,n,o,i)}a(o)},as=function(t,e,a,n,i){var o=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(pe(e,"string")&&!t.required)return a();H.required(t,e,n,o,i),pe(e,"string")||H.pattern(t,e,n,o,i)}a(o)},is=function(t,e,a,n,i){var o=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(pe(e)&&!t.required)return a();H.required(t,e,n,o,i),pe(e)||H.type(t,e,n,o,i)}a(o)},os=function(t,e,a,n,i){var o=[],s=Array.isArray(e)?"array":xe(e);H.required(t,e,n,o,i,s),a(o)},ss=function(t,e,a,n,i){var o=[],s=t.required||!t.required&&n.hasOwnProperty(t.field);if(s){if(pe(e,"string")&&!t.required)return a();H.required(t,e,n,o,i,"string"),pe(e,"string")||(H.type(t,e,n,o,i),H.range(t,e,n,o,i),H.pattern(t,e,n,o,i),t.whitespace===!0&&H.whitespace(t,e,n,o,i))}a(o)},un=function(t,e,a,n,i){var o=t.type,s=[],l=t.required||!t.required&&n.hasOwnProperty(t.field);if(l){if(pe(e,o)&&!t.required)return a();H.required(t,e,n,s,i,o),pe(e,o)||H.type(t,e,n,s,i)}a(s)};const ar={string:ss,method:ts,number:rs,boolean:Zo,regexp:is,integer:es,float:Yo,array:Go,object:ns,enum:Xo,pattern:as,date:Qo,url:un,hex:un,email:un,required:os,any:Ko};var cr=function(){function r(t){Ht(this,r),C(this,"rules",null),C(this,"_messages",bn),this.define(t)}return Ut(r,[{key:"define",value:function(e){var a=this;if(!e)throw new Error("Cannot configure a schema with no rules");if(xe(e)!=="object"||Array.isArray(e))throw new Error("Rules must be an object");this.rules={},Object.keys(e).forEach(function(n){var i=e[n];a.rules[n]=Array.isArray(i)?i:[i]})}},{key:"messages",value:function(e){return e&&(this._messages=Qn(yn(),e)),this._messages}},{key:"validate",value:function(e){var a=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:function(){},o=e,s=n,l=i;if(typeof s=="function"&&(l=s,s={}),!this.rules||Object.keys(this.rules).length===0)return l&&l(null,o),Promise.resolve(o);function c(m){var p=[],d={};function b(w){if(Array.isArray(w)){var P;p=(P=p).concat.apply(P,j(w))}else p.push(w)}for(var g=0;g<m.length;g++)b(m[g]);p.length?(d=Cn(p),l(p,d)):l(null,o)}if(s.messages){var u=this.messages();u===bn&&(u=yn()),Qn(u,s.messages),s.messages=u}else s.messages=this.messages();var v={},h=s.keys||Object.keys(this.rules);h.forEach(function(m){var p=a.rules[m],d=o[m];p.forEach(function(b){var g=b;typeof g.transform=="function"&&(o===e&&(o=S({},o)),d=o[m]=g.transform(d),d!=null&&(g.type=g.type||(Array.isArray(d)?"array":xe(d)))),typeof g=="function"?g={validator:g}:g=S({},g),g.validator=a.getValidationMethod(g),g.validator&&(g.field=m,g.fullField=g.fullField||m,g.type=a.getType(g),v[m]=v[m]||[],v[m].push({rule:g,value:d,source:o,field:m}))})});var y={};return Lo(v,s,function(m,p){var d=m.rule,b=(d.type==="object"||d.type==="array")&&(xe(d.fields)==="object"||xe(d.defaultField)==="object");b=b&&(d.required||!d.required&&m.value),d.field=m.field;function g(R,x){return S(S({},x),{},{fullField:"".concat(d.fullField,".").concat(R),fullFields:d.fullFields?[].concat(j(d.fullFields),[R]):[R]})}function w(){var R=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],x=Array.isArray(R)?R:[R];!s.suppressWarning&&x.length&&r.warning("async-validator:",x),x.length&&d.message!==void 0&&(x=[].concat(d.message));var O=x.map(Zn(d,o));if(s.first&&O.length)return y[d.field]=1,p(O);if(!b)p(O);else{if(d.required&&!m.value)return d.message!==void 0?O=[].concat(d.message).map(Zn(d,o)):s.error&&(O=[s.error(d,je(s.messages.required,d.field))]),p(O);var $={};d.defaultField&&Object.keys(m.value).map(function(_){$[_]=d.defaultField}),$=S(S({},$),m.rule.fields);var V={};Object.keys($).forEach(function(_){var k=$[_],Z=Array.isArray(k)?k:[k];V[_]=Z.map(g.bind(null,_))});var L=new r(V);L.messages(s.messages),m.rule.options&&(m.rule.options.messages=s.messages,m.rule.options.error=s.error),L.validate(m.value,m.rule.options||s,function(_){var k=[];O&&O.length&&k.push.apply(k,j(O)),_&&_.length&&k.push.apply(k,j(_)),p(k.length?k:null)})}}var P;if(d.asyncValidator)P=d.asyncValidator(d,m.value,w,m.source,s);else if(d.validator){try{P=d.validator(d,m.value,w,m.source,s)}catch(R){var E,F;(E=(F=console).error)===null||E===void 0||E.call(F,R),s.suppressValidatorError||setTimeout(function(){throw R},0),w(R.message)}P===!0?w():P===!1?w(typeof d.message=="function"?d.message(d.fullField||d.field):d.message||"".concat(d.fullField||d.field," fails")):P instanceof Array?w(P):P instanceof Error&&w(P.message)}P&&P.then&&P.then(function(){return w()},function(R){return w(R)})},function(m){c(m)},o)}},{key:"getType",value:function(e){if(e.type===void 0&&e.pattern instanceof RegExp&&(e.type="pattern"),typeof e.validator!="function"&&e.type&&!ar.hasOwnProperty(e.type))throw new Error(je("Unknown rule type %s",e.type));return e.type||"string"}},{key:"getValidationMethod",value:function(e){if(typeof e.validator=="function")return e.validator;var a=Object.keys(e),n=a.indexOf("message");return n!==-1&&a.splice(n,1),a.length===1&&a[0]==="required"?ar.required:ar[this.getType(e)]||void 0}}]),r}();C(cr,"register",function(t,e){if(typeof e!="function")throw new Error("Cannot register a validator by type, validator is not a function");ar[t]=e});C(cr,"warning",ko);C(cr,"messages",bn);C(cr,"validators",ar);var ze="'${name}' is not a valid ${type}",_a={default:"Validation error on field '${name}'",required:"'${name}' is required",enum:"'${name}' must be one of [${enum}]",whitespace:"'${name}' cannot be empty",date:{format:"'${name}' is invalid for format date",parse:"'${name}' could not be parsed as date",invalid:"'${name}' is invalid date"},types:{string:ze,method:ze,array:ze,object:ze,number:ze,date:ze,boolean:ze,integer:ze,float:ze,regexp:ze,email:ze,url:ze,hex:ze},string:{len:"'${name}' must be exactly ${len} characters",min:"'${name}' must be at least ${min} characters",max:"'${name}' cannot be longer than ${max} characters",range:"'${name}' must be between ${min} and ${max} characters"},number:{len:"'${name}' must equal ${len}",min:"'${name}' cannot be less than ${min}",max:"'${name}' cannot be greater than ${max}",range:"'${name}' must be between ${min} and ${max}"},array:{len:"'${name}' must be exactly ${len} in length",min:"'${name}' cannot be less than ${min} in length",max:"'${name}' cannot be greater than ${max} in length",range:"'${name}' must be between ${min} and ${max} in length"},pattern:{mismatch:"'${name}' does not match pattern ${pattern}"}},Xn=cr;function ls(r,t){return r.replace(/\\?\$\{\w+\}/g,function(e){if(e.startsWith("\\"))return e.slice(1);var a=e.slice(2,-1);return t[a]})}var Yn="CODE_LOGIC_ERROR";function Fn(r,t,e,a,n){return En.apply(this,arguments)}function En(){return En=lr(at().mark(function r(t,e,a,n,i){var o,s,l,c,u,v,h,y,m;return at().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:return o=S({},a),delete o.ruleIndex,Xn.warning=function(){},o.validator&&(s=o.validator,o.validator=function(){try{return s.apply(void 0,arguments)}catch(b){return console.error(b),Promise.reject(Yn)}}),l=null,o&&o.type==="array"&&o.defaultField&&(l=o.defaultField,delete o.defaultField),c=new Xn(C({},t,[o])),u=rr(_a,n.validateMessages),c.messages(u),v=[],d.prev=10,d.next=13,Promise.resolve(c.validate(C({},t,e),S({},n)));case 13:d.next=18;break;case 15:d.prev=15,d.t0=d.catch(10),d.t0.errors&&(v=d.t0.errors.map(function(b,g){var w=b.message,P=w===Yn?u.default:w;return f.isValidElement(P)?f.cloneElement(P,{key:"error_".concat(g)}):P}));case 18:if(!(!v.length&&l)){d.next=23;break}return d.next=21,Promise.all(e.map(function(b,g){return Fn("".concat(t,".").concat(g),b,l,n,i)}));case 21:return h=d.sent,d.abrupt("return",h.reduce(function(b,g){return[].concat(j(b),j(g))},[]));case 23:return y=S(S({},a),{},{name:t,enum:(a.enum||[]).join(", ")},i),m=v.map(function(b){return typeof b=="string"?ls(b,y):b}),d.abrupt("return",m);case 26:case"end":return d.stop()}},r,null,[[10,15]])})),En.apply(this,arguments)}function us(r,t,e,a,n,i){var o=r.join("."),s=e.map(function(u,v){var h=u.validator,y=S(S({},u),{},{ruleIndex:v});return h&&(y.validator=function(m,p,d){var b=!1,g=function(){for(var E=arguments.length,F=new Array(E),R=0;R<E;R++)F[R]=arguments[R];Promise.resolve().then(function(){gt(!b,"Your validator function has already return a promise. `callback` will be ignored."),b||d.apply(void 0,F)})},w=h(m,p,g);b=w&&typeof w.then=="function"&&typeof w.catch=="function",gt(b,"`callback` is deprecated. Please return a promise instead."),b&&w.then(function(){d()}).catch(function(P){d(P||" ")})}),y}).sort(function(u,v){var h=u.warningOnly,y=u.ruleIndex,m=v.warningOnly,p=v.ruleIndex;return!!h==!!m?y-p:h?1:-1}),l;if(n===!0)l=new Promise(function(){var u=lr(at().mark(function v(h,y){var m,p,d;return at().wrap(function(g){for(;;)switch(g.prev=g.next){case 0:m=0;case 1:if(!(m<s.length)){g.next=12;break}return p=s[m],g.next=5,Fn(o,t,p,a,i);case 5:if(d=g.sent,!d.length){g.next=9;break}return y([{errors:d,rule:p}]),g.abrupt("return");case 9:m+=1,g.next=1;break;case 12:h([]);case 13:case"end":return g.stop()}},v)}));return function(v,h){return u.apply(this,arguments)}}());else{var c=s.map(function(u){return Fn(o,t,u,a,i).then(function(v){return{errors:v,rule:u}})});l=(n?fs(c):cs(c)).then(function(u){return Promise.reject(u)})}return l.catch(function(u){return u}),l}function cs(r){return Pn.apply(this,arguments)}function Pn(){return Pn=lr(at().mark(function r(t){return at().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.abrupt("return",Promise.all(t).then(function(n){var i,o=(i=[]).concat.apply(i,j(n));return o}));case 1:case"end":return a.stop()}},r)})),Pn.apply(this,arguments)}function fs(r){return On.apply(this,arguments)}function On(){return On=lr(at().mark(function r(t){var e;return at().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return e=0,n.abrupt("return",new Promise(function(i){t.forEach(function(o){o.then(function(s){s.errors.length&&i([s]),e+=1,e===t.length&&i([])})})}));case 2:case"end":return n.stop()}},r)})),On.apply(this,arguments)}function ce(r){return pn(r)}function ea(r,t){var e={};return t.forEach(function(a){var n=Ot(r,a);e=mt(e,a,n)}),e}function jt(r,t){var e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return r&&r.some(function(a){return Na(t,a,e)})}function Na(r,t){var e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return!r||!t||!e&&r.length!==t.length?!1:t.every(function(a,n){return r[n]===a})}function ds(r,t){if(r===t)return!0;if(!r&&t||r&&!t||!r||!t||xe(r)!=="object"||xe(t)!=="object")return!1;var e=Object.keys(r),a=Object.keys(t),n=new Set([].concat(e,a));return j(n).every(function(i){var o=r[i],s=t[i];return typeof o=="function"&&typeof s=="function"?!0:o===s})}function vs(r){var t=arguments.length<=1?void 0:arguments[1];return t&&t.target&&xe(t.target)==="object"&&r in t.target?t.target[r]:t}function ta(r,t,e){var a=r.length;if(t<0||t>=a||e<0||e>=a)return r;var n=r[t],i=t-e;return i>0?[].concat(j(r.slice(0,e)),[n],j(r.slice(e,t)),j(r.slice(t+1,a))):i<0?[].concat(j(r.slice(0,t)),j(r.slice(t+1,e+1)),[n],j(r.slice(e+1,a))):r}var ms=["name"],Ze=[];function cn(r,t,e,a,n,i){return typeof r=="function"?r(t,e,"source"in i?{source:i.source}:{}):a!==n}var Nn=function(r){$n(e,r);var t=xn(e);function e(a){var n;if(Ht(this,e),n=t.call(this,a),C(K(n),"state",{resetCount:0}),C(K(n),"cancelRegisterFunc",null),C(K(n),"mounted",!1),C(K(n),"touched",!1),C(K(n),"dirty",!1),C(K(n),"validatePromise",void 0),C(K(n),"prevValidating",void 0),C(K(n),"errors",Ze),C(K(n),"warnings",Ze),C(K(n),"cancelRegister",function(){var l=n.props,c=l.preserve,u=l.isListField,v=l.name;n.cancelRegisterFunc&&n.cancelRegisterFunc(u,c,ce(v)),n.cancelRegisterFunc=null}),C(K(n),"getNamePath",function(){var l=n.props,c=l.name,u=l.fieldContext,v=u.prefixName,h=v===void 0?[]:v;return c!==void 0?[].concat(j(h),j(c)):[]}),C(K(n),"getRules",function(){var l=n.props,c=l.rules,u=c===void 0?[]:c,v=l.fieldContext;return u.map(function(h){return typeof h=="function"?h(v):h})}),C(K(n),"refresh",function(){n.mounted&&n.setState(function(l){var c=l.resetCount;return{resetCount:c+1}})}),C(K(n),"metaCache",null),C(K(n),"triggerMetaEvent",function(l){var c=n.props.onMetaChange;if(c){var u=S(S({},n.getMeta()),{},{destroy:l});An(n.metaCache,u)||c(u),n.metaCache=u}else n.metaCache=null}),C(K(n),"onStoreChange",function(l,c,u){var v=n.props,h=v.shouldUpdate,y=v.dependencies,m=y===void 0?[]:y,p=v.onReset,d=u.store,b=n.getNamePath(),g=n.getValue(l),w=n.getValue(d),P=c&&jt(c,b);switch(u.type==="valueUpdate"&&u.source==="external"&&!An(g,w)&&(n.touched=!0,n.dirty=!0,n.validatePromise=null,n.errors=Ze,n.warnings=Ze,n.triggerMetaEvent()),u.type){case"reset":if(!c||P){n.touched=!1,n.dirty=!1,n.validatePromise=void 0,n.errors=Ze,n.warnings=Ze,n.triggerMetaEvent(),p==null||p(),n.refresh();return}break;case"remove":{if(h&&cn(h,l,d,g,w,u)){n.reRender();return}break}case"setField":{var E=u.data;if(P){"touched"in E&&(n.touched=E.touched),"validating"in E&&!("originRCField"in E)&&(n.validatePromise=E.validating?Promise.resolve([]):null),"errors"in E&&(n.errors=E.errors||Ze),"warnings"in E&&(n.warnings=E.warnings||Ze),n.dirty=!0,n.triggerMetaEvent(),n.reRender();return}else if("value"in E&&jt(c,b,!0)){n.reRender();return}if(h&&!b.length&&cn(h,l,d,g,w,u)){n.reRender();return}break}case"dependenciesUpdate":{var F=m.map(ce);if(F.some(function(R){return jt(u.relatedFields,R)})){n.reRender();return}break}default:if(P||(!m.length||b.length||h)&&cn(h,l,d,g,w,u)){n.reRender();return}break}h===!0&&n.reRender()}),C(K(n),"validateRules",function(l){var c=n.getNamePath(),u=n.getValue(),v=l||{},h=v.triggerName,y=v.validateOnly,m=y===void 0?!1:y,p=Promise.resolve().then(lr(at().mark(function d(){var b,g,w,P,E,F,R;return at().wrap(function(O){for(;;)switch(O.prev=O.next){case 0:if(n.mounted){O.next=2;break}return O.abrupt("return",[]);case 2:if(b=n.props,g=b.validateFirst,w=g===void 0?!1:g,P=b.messageVariables,E=b.validateDebounce,F=n.getRules(),h&&(F=F.filter(function($){return $}).filter(function($){var V=$.validateTrigger;if(!V)return!0;var L=pn(V);return L.includes(h)})),!(E&&h)){O.next=10;break}return O.next=8,new Promise(function($){setTimeout($,E)});case 8:if(n.validatePromise===p){O.next=10;break}return O.abrupt("return",[]);case 10:return R=us(c,u,F,l,w,P),R.catch(function($){return $}).then(function(){var $=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Ze;if(n.validatePromise===p){var V;n.validatePromise=null;var L=[],_=[];(V=$.forEach)===null||V===void 0||V.call($,function(k){var Z=k.rule.warningOnly,z=k.errors,U=z===void 0?Ze:z;Z?_.push.apply(_,j(U)):L.push.apply(L,j(U))}),n.errors=L,n.warnings=_,n.triggerMetaEvent(),n.reRender()}}),O.abrupt("return",R);case 13:case"end":return O.stop()}},d)})));return m||(n.validatePromise=p,n.dirty=!0,n.errors=Ze,n.warnings=Ze,n.triggerMetaEvent(),n.reRender()),p}),C(K(n),"isFieldValidating",function(){return!!n.validatePromise}),C(K(n),"isFieldTouched",function(){return n.touched}),C(K(n),"isFieldDirty",function(){if(n.dirty||n.props.initialValue!==void 0)return!0;var l=n.props.fieldContext,c=l.getInternalHooks(Rt),u=c.getInitialValue;return u(n.getNamePath())!==void 0}),C(K(n),"getErrors",function(){return n.errors}),C(K(n),"getWarnings",function(){return n.warnings}),C(K(n),"isListField",function(){return n.props.isListField}),C(K(n),"isList",function(){return n.props.isList}),C(K(n),"isPreserve",function(){return n.props.preserve}),C(K(n),"getMeta",function(){n.prevValidating=n.isFieldValidating();var l={touched:n.isFieldTouched(),validating:n.prevValidating,errors:n.errors,warnings:n.warnings,name:n.getNamePath(),validated:n.validatePromise===null};return l}),C(K(n),"getOnlyChild",function(l){if(typeof l=="function"){var c=n.getMeta();return S(S({},n.getOnlyChild(l(n.getControlled(),c,n.props.fieldContext))),{},{isFunction:!0})}var u=wa(l);return u.length!==1||!f.isValidElement(u[0])?{child:u,isFunction:!1}:{child:u[0],isFunction:!1}}),C(K(n),"getValue",function(l){var c=n.props.fieldContext.getFieldsValue,u=n.getNamePath();return Ot(l||c(!0),u)}),C(K(n),"getControlled",function(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},c=n.props,u=c.name,v=c.trigger,h=c.validateTrigger,y=c.getValueFromEvent,m=c.normalize,p=c.valuePropName,d=c.getValueProps,b=c.fieldContext,g=h!==void 0?h:b.validateTrigger,w=n.getNamePath(),P=b.getInternalHooks,E=b.getFieldsValue,F=P(Rt),R=F.dispatch,x=n.getValue(),O=d||function(k){return C({},p,k)},$=l[v],V=u!==void 0?O(x):{},L=S(S({},l),V);L[v]=function(){n.touched=!0,n.dirty=!0,n.triggerMetaEvent();for(var k,Z=arguments.length,z=new Array(Z),U=0;U<Z;U++)z[U]=arguments[U];y?k=y.apply(void 0,z):k=vs.apply(void 0,[p].concat(z)),m&&(k=m(k,x,E(!0))),k!==x&&R({type:"updateValue",namePath:w,value:k}),$&&$.apply(void 0,z)};var _=pn(g||[]);return _.forEach(function(k){var Z=L[k];L[k]=function(){Z&&Z.apply(void 0,arguments);var z=n.props.rules;z&&z.length&&R({type:"validateField",namePath:w,triggerName:k})}}),L}),a.fieldContext){var i=a.fieldContext.getInternalHooks,o=i(Rt),s=o.initEntityValue;s(K(n))}return n}return Ut(e,[{key:"componentDidMount",value:function(){var n=this.props,i=n.shouldUpdate,o=n.fieldContext;if(this.mounted=!0,o){var s=o.getInternalHooks,l=s(Rt),c=l.registerField;this.cancelRegisterFunc=c(this)}i===!0&&this.reRender()}},{key:"componentWillUnmount",value:function(){this.cancelRegister(),this.triggerMetaEvent(!0),this.mounted=!1}},{key:"reRender",value:function(){this.mounted&&this.forceUpdate()}},{key:"render",value:function(){var n=this.state.resetCount,i=this.props.children,o=this.getOnlyChild(i),s=o.child,l=o.isFunction,c;return l?c=s:f.isValidElement(s)?c=f.cloneElement(s,this.getControlled(s.props)):(gt(!s,"`children` of Field is not validate ReactElement."),c=s),f.createElement(f.Fragment,{key:n},c)}}]),e}(f.Component);C(Nn,"contextType",qt);C(Nn,"defaultProps",{trigger:"onChange",valuePropName:"value"});function ka(r){var t,e=r.name,a=nt(r,ms),n=f.useContext(qt),i=f.useContext(Ar),o=e!==void 0?ce(e):void 0,s=(t=a.isListField)!==null&&t!==void 0?t:!!i,l="keep";return s||(l="_".concat((o||[]).join("_"))),f.createElement(Nn,Ne({key:l,name:o,isListField:s},a,{fieldContext:n}))}function hs(r){var t=r.name,e=r.initialValue,a=r.children,n=r.rules,i=r.validateTrigger,o=r.isListField,s=f.useContext(qt),l=f.useContext(Ar),c=f.useRef({keys:[],id:0}),u=c.current,v=f.useMemo(function(){var p=ce(s.prefixName)||[];return[].concat(j(p),j(ce(t)))},[s.prefixName,t]),h=f.useMemo(function(){return S(S({},s),{},{prefixName:v})},[s,v]),y=f.useMemo(function(){return{getKey:function(d){var b=v.length,g=d[b];return[u.keys[g],d.slice(b+1)]}}},[v]);if(typeof a!="function")return gt(!1,"Form.List only accepts function as children."),null;var m=function(d,b,g){var w=g.source;return w==="internal"?!1:d!==b};return f.createElement(Ar.Provider,{value:y},f.createElement(qt.Provider,{value:h},f.createElement(ka,{name:[],shouldUpdate:m,rules:n,validateTrigger:i,initialValue:e,isList:!0,isListField:o??!!l},function(p,d){var b=p.value,g=b===void 0?[]:b,w=p.onChange,P=s.getFieldValue,E=function(){var O=P(v||[]);return O||[]},F={add:function(O,$){var V=E();$>=0&&$<=V.length?(u.keys=[].concat(j(u.keys.slice(0,$)),[u.id],j(u.keys.slice($))),w([].concat(j(V.slice(0,$)),[O],j(V.slice($))))):(u.keys=[].concat(j(u.keys),[u.id]),w([].concat(j(V),[O]))),u.id+=1},remove:function(O){var $=E(),V=new Set(Array.isArray(O)?O:[O]);V.size<=0||(u.keys=u.keys.filter(function(L,_){return!V.has(_)}),w($.filter(function(L,_){return!V.has(_)})))},move:function(O,$){if(O!==$){var V=E();O<0||O>=V.length||$<0||$>=V.length||(u.keys=ta(u.keys,O,$),w(ta(V,O,$)))}}},R=g||[];return Array.isArray(R)||(R=[]),a(R.map(function(x,O){var $=u.keys[O];return $===void 0&&(u.keys[O]=u.id,$=u.keys[O],u.id+=1),{name:O,key:$,isListField:!0}}),F,d)})))}function gs(r){var t=!1,e=r.length,a=[];return r.length?new Promise(function(n,i){r.forEach(function(o,s){o.catch(function(l){return t=!0,l}).then(function(l){e-=1,a[s]=l,!(e>0)&&(t&&i(a),n(a))})})}):Promise.resolve([])}var Ia="__@field_split__";function fn(r){return r.map(function(t){return"".concat(xe(t),":").concat(t)}).join(Ia)}var Tt=function(){function r(){Ht(this,r),C(this,"kvs",new Map)}return Ut(r,[{key:"set",value:function(e,a){this.kvs.set(fn(e),a)}},{key:"get",value:function(e){return this.kvs.get(fn(e))}},{key:"update",value:function(e,a){var n=this.get(e),i=a(n);i?this.set(e,i):this.delete(e)}},{key:"delete",value:function(e){this.kvs.delete(fn(e))}},{key:"map",value:function(e){return j(this.kvs.entries()).map(function(a){var n=q(a,2),i=n[0],o=n[1],s=i.split(Ia);return e({key:s.map(function(l){var c=l.match(/^([^:]*):(.*)$/),u=q(c,3),v=u[1],h=u[2];return v==="number"?Number(h):h}),value:o})})}},{key:"toJSON",value:function(){var e={};return this.map(function(a){var n=a.key,i=a.value;return e[n.join(".")]=i,null}),e}}]),r}(),ps=["name"],ys=Ut(function r(t){var e=this;Ht(this,r),C(this,"formHooked",!1),C(this,"forceRootUpdate",void 0),C(this,"subscribable",!0),C(this,"store",{}),C(this,"fieldEntities",[]),C(this,"initialValues",{}),C(this,"callbacks",{}),C(this,"validateMessages",null),C(this,"preserve",null),C(this,"lastValidatePromise",null),C(this,"getForm",function(){return{getFieldValue:e.getFieldValue,getFieldsValue:e.getFieldsValue,getFieldError:e.getFieldError,getFieldWarning:e.getFieldWarning,getFieldsError:e.getFieldsError,isFieldsTouched:e.isFieldsTouched,isFieldTouched:e.isFieldTouched,isFieldValidating:e.isFieldValidating,isFieldsValidating:e.isFieldsValidating,resetFields:e.resetFields,setFields:e.setFields,setFieldValue:e.setFieldValue,setFieldsValue:e.setFieldsValue,validateFields:e.validateFields,submit:e.submit,_init:!0,getInternalHooks:e.getInternalHooks}}),C(this,"getInternalHooks",function(a){return a===Rt?(e.formHooked=!0,{dispatch:e.dispatch,initEntityValue:e.initEntityValue,registerField:e.registerField,useSubscribe:e.useSubscribe,setInitialValues:e.setInitialValues,destroyForm:e.destroyForm,setCallbacks:e.setCallbacks,setValidateMessages:e.setValidateMessages,getFields:e.getFields,setPreserve:e.setPreserve,getInitialValue:e.getInitialValue,registerWatch:e.registerWatch}):(gt(!1,"`getInternalHooks` is internal usage. Should not call directly."),null)}),C(this,"useSubscribe",function(a){e.subscribable=a}),C(this,"prevWithoutPreserves",null),C(this,"setInitialValues",function(a,n){if(e.initialValues=a||{},n){var i,o=rr(a,e.store);(i=e.prevWithoutPreserves)===null||i===void 0||i.map(function(s){var l=s.key;o=mt(o,l,Ot(a,l))}),e.prevWithoutPreserves=null,e.updateStore(o)}}),C(this,"destroyForm",function(a){if(a)e.updateStore({});else{var n=new Tt;e.getFieldEntities(!0).forEach(function(i){e.isMergedPreserve(i.isPreserve())||n.set(i.getNamePath(),!0)}),e.prevWithoutPreserves=n}}),C(this,"getInitialValue",function(a){var n=Ot(e.initialValues,a);return a.length?rr(n):n}),C(this,"setCallbacks",function(a){e.callbacks=a}),C(this,"setValidateMessages",function(a){e.validateMessages=a}),C(this,"setPreserve",function(a){e.preserve=a}),C(this,"watchList",[]),C(this,"registerWatch",function(a){return e.watchList.push(a),function(){e.watchList=e.watchList.filter(function(n){return n!==a})}}),C(this,"notifyWatch",function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];if(e.watchList.length){var n=e.getFieldsValue(),i=e.getFieldsValue(!0);e.watchList.forEach(function(o){o(n,i,a)})}}),C(this,"timeoutId",null),C(this,"warningUnhooked",function(){}),C(this,"updateStore",function(a){e.store=a}),C(this,"getFieldEntities",function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;return a?e.fieldEntities.filter(function(n){return n.getNamePath().length}):e.fieldEntities}),C(this,"getFieldsMap",function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,n=new Tt;return e.getFieldEntities(a).forEach(function(i){var o=i.getNamePath();n.set(o,i)}),n}),C(this,"getFieldEntitiesForNamePathList",function(a){if(!a)return e.getFieldEntities(!0);var n=e.getFieldsMap(!0);return a.map(function(i){var o=ce(i);return n.get(o)||{INVALIDATE_NAME_PATH:ce(i)}})}),C(this,"getFieldsValue",function(a,n){e.warningUnhooked();var i,o,s;if(a===!0||Array.isArray(a)?(i=a,o=n):a&&xe(a)==="object"&&(s=a.strict,o=a.filter),i===!0&&!o)return e.store;var l=e.getFieldEntitiesForNamePathList(Array.isArray(i)?i:null),c=[];return l.forEach(function(u){var v,h,y="INVALIDATE_NAME_PATH"in u?u.INVALIDATE_NAME_PATH:u.getNamePath();if(s){var m,p;if((m=(p=u).isList)!==null&&m!==void 0&&m.call(p))return}else if(!i&&(v=(h=u).isListField)!==null&&v!==void 0&&v.call(h))return;if(!o)c.push(y);else{var d="getMeta"in u?u.getMeta():null;o(d)&&c.push(y)}}),ea(e.store,c.map(ce))}),C(this,"getFieldValue",function(a){e.warningUnhooked();var n=ce(a);return Ot(e.store,n)}),C(this,"getFieldsError",function(a){e.warningUnhooked();var n=e.getFieldEntitiesForNamePathList(a);return n.map(function(i,o){return i&&!("INVALIDATE_NAME_PATH"in i)?{name:i.getNamePath(),errors:i.getErrors(),warnings:i.getWarnings()}:{name:ce(a[o]),errors:[],warnings:[]}})}),C(this,"getFieldError",function(a){e.warningUnhooked();var n=ce(a),i=e.getFieldsError([n])[0];return i.errors}),C(this,"getFieldWarning",function(a){e.warningUnhooked();var n=ce(a),i=e.getFieldsError([n])[0];return i.warnings}),C(this,"isFieldsTouched",function(){e.warningUnhooked();for(var a=arguments.length,n=new Array(a),i=0;i<a;i++)n[i]=arguments[i];var o=n[0],s=n[1],l,c=!1;n.length===0?l=null:n.length===1?Array.isArray(o)?(l=o.map(ce),c=!1):(l=null,c=o):(l=o.map(ce),c=s);var u=e.getFieldEntities(!0),v=function(d){return d.isFieldTouched()};if(!l)return c?u.every(function(p){return v(p)||p.isList()}):u.some(v);var h=new Tt;l.forEach(function(p){h.set(p,[])}),u.forEach(function(p){var d=p.getNamePath();l.forEach(function(b){b.every(function(g,w){return d[w]===g})&&h.update(b,function(g){return[].concat(j(g),[p])})})});var y=function(d){return d.some(v)},m=h.map(function(p){var d=p.value;return d});return c?m.every(y):m.some(y)}),C(this,"isFieldTouched",function(a){return e.warningUnhooked(),e.isFieldsTouched([a])}),C(this,"isFieldsValidating",function(a){e.warningUnhooked();var n=e.getFieldEntities();if(!a)return n.some(function(o){return o.isFieldValidating()});var i=a.map(ce);return n.some(function(o){var s=o.getNamePath();return jt(i,s)&&o.isFieldValidating()})}),C(this,"isFieldValidating",function(a){return e.warningUnhooked(),e.isFieldsValidating([a])}),C(this,"resetWithFieldInitialValue",function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=new Tt,i=e.getFieldEntities(!0);i.forEach(function(l){var c=l.props.initialValue,u=l.getNamePath();if(c!==void 0){var v=n.get(u)||new Set;v.add({entity:l,value:c}),n.set(u,v)}});var o=function(c){c.forEach(function(u){var v=u.props.initialValue;if(v!==void 0){var h=u.getNamePath(),y=e.getInitialValue(h);if(y!==void 0)gt(!1,"Form already set 'initialValues' with path '".concat(h.join("."),"'. Field can not overwrite it."));else{var m=n.get(h);if(m&&m.size>1)gt(!1,"Multiple Field with path '".concat(h.join("."),"' set 'initialValue'. Can not decide which one to pick."));else if(m){var p=e.getFieldValue(h),d=u.isListField();!d&&(!a.skipExist||p===void 0)&&e.updateStore(mt(e.store,h,j(m)[0].value))}}}})},s;a.entities?s=a.entities:a.namePathList?(s=[],a.namePathList.forEach(function(l){var c=n.get(l);if(c){var u;(u=s).push.apply(u,j(j(c).map(function(v){return v.entity})))}})):s=i,o(s)}),C(this,"resetFields",function(a){e.warningUnhooked();var n=e.store;if(!a){e.updateStore(rr(e.initialValues)),e.resetWithFieldInitialValue(),e.notifyObservers(n,null,{type:"reset"}),e.notifyWatch();return}var i=a.map(ce);i.forEach(function(o){var s=e.getInitialValue(o);e.updateStore(mt(e.store,o,s))}),e.resetWithFieldInitialValue({namePathList:i}),e.notifyObservers(n,i,{type:"reset"}),e.notifyWatch(i)}),C(this,"setFields",function(a){e.warningUnhooked();var n=e.store,i=[];a.forEach(function(o){var s=o.name,l=nt(o,ps),c=ce(s);i.push(c),"value"in l&&e.updateStore(mt(e.store,c,l.value)),e.notifyObservers(n,[c],{type:"setField",data:o})}),e.notifyWatch(i)}),C(this,"getFields",function(){var a=e.getFieldEntities(!0),n=a.map(function(i){var o=i.getNamePath(),s=i.getMeta(),l=S(S({},s),{},{name:o,value:e.getFieldValue(o)});return Object.defineProperty(l,"originRCField",{value:!0}),l});return n}),C(this,"initEntityValue",function(a){var n=a.props.initialValue;if(n!==void 0){var i=a.getNamePath(),o=Ot(e.store,i);o===void 0&&e.updateStore(mt(e.store,i,n))}}),C(this,"isMergedPreserve",function(a){var n=a!==void 0?a:e.preserve;return n??!0}),C(this,"registerField",function(a){e.fieldEntities.push(a);var n=a.getNamePath();if(e.notifyWatch([n]),a.props.initialValue!==void 0){var i=e.store;e.resetWithFieldInitialValue({entities:[a],skipExist:!0}),e.notifyObservers(i,[a.getNamePath()],{type:"valueUpdate",source:"internal"})}return function(o,s){var l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[];if(e.fieldEntities=e.fieldEntities.filter(function(v){return v!==a}),!e.isMergedPreserve(s)&&(!o||l.length>1)){var c=o?void 0:e.getInitialValue(n);if(n.length&&e.getFieldValue(n)!==c&&e.fieldEntities.every(function(v){return!Na(v.getNamePath(),n)})){var u=e.store;e.updateStore(mt(u,n,c,!0)),e.notifyObservers(u,[n],{type:"remove"}),e.triggerDependenciesUpdate(u,n)}}e.notifyWatch([n])}}),C(this,"dispatch",function(a){switch(a.type){case"updateValue":{var n=a.namePath,i=a.value;e.updateValue(n,i);break}case"validateField":{var o=a.namePath,s=a.triggerName;e.validateFields([o],{triggerName:s});break}}}),C(this,"notifyObservers",function(a,n,i){if(e.subscribable){var o=S(S({},i),{},{store:e.getFieldsValue(!0)});e.getFieldEntities().forEach(function(s){var l=s.onStoreChange;l(a,n,o)})}else e.forceRootUpdate()}),C(this,"triggerDependenciesUpdate",function(a,n){var i=e.getDependencyChildrenFields(n);return i.length&&e.validateFields(i),e.notifyObservers(a,i,{type:"dependenciesUpdate",relatedFields:[n].concat(j(i))}),i}),C(this,"updateValue",function(a,n){var i=ce(a),o=e.store;e.updateStore(mt(e.store,i,n)),e.notifyObservers(o,[i],{type:"valueUpdate",source:"internal"}),e.notifyWatch([i]);var s=e.triggerDependenciesUpdate(o,i),l=e.callbacks.onValuesChange;if(l){var c=ea(e.store,[i]);l(c,e.getFieldsValue())}e.triggerOnFieldsChange([i].concat(j(s)))}),C(this,"setFieldsValue",function(a){e.warningUnhooked();var n=e.store;if(a){var i=rr(e.store,a);e.updateStore(i)}e.notifyObservers(n,null,{type:"valueUpdate",source:"external"}),e.notifyWatch()}),C(this,"setFieldValue",function(a,n){e.setFields([{name:a,value:n,errors:[],warnings:[]}])}),C(this,"getDependencyChildrenFields",function(a){var n=new Set,i=[],o=new Tt;e.getFieldEntities().forEach(function(l){var c=l.props.dependencies;(c||[]).forEach(function(u){var v=ce(u);o.update(v,function(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:new Set;return h.add(l),h})})});var s=function l(c){var u=o.get(c)||new Set;u.forEach(function(v){if(!n.has(v)){n.add(v);var h=v.getNamePath();v.isFieldDirty()&&h.length&&(i.push(h),l(h))}})};return s(a),i}),C(this,"triggerOnFieldsChange",function(a,n){var i=e.callbacks.onFieldsChange;if(i){var o=e.getFields();if(n){var s=new Tt;n.forEach(function(c){var u=c.name,v=c.errors;s.set(u,v)}),o.forEach(function(c){c.errors=s.get(c.name)||c.errors})}var l=o.filter(function(c){var u=c.name;return jt(a,u)});l.length&&i(l,o)}}),C(this,"validateFields",function(a,n){e.warningUnhooked();var i,o;Array.isArray(a)||typeof a=="string"||typeof n=="string"?(i=a,o=n):o=a;var s=!!i,l=s?i.map(ce):[],c=[],u=String(Date.now()),v=new Set,h=o||{},y=h.recursive,m=h.dirty;e.getFieldEntities(!0).forEach(function(g){if(s||l.push(g.getNamePath()),!(!g.props.rules||!g.props.rules.length)&&!(m&&!g.isFieldDirty())){var w=g.getNamePath();if(v.add(w.join(u)),!s||jt(l,w,y)){var P=g.validateRules(S({validateMessages:S(S({},_a),e.validateMessages)},o));c.push(P.then(function(){return{name:w,errors:[],warnings:[]}}).catch(function(E){var F,R=[],x=[];return(F=E.forEach)===null||F===void 0||F.call(E,function(O){var $=O.rule.warningOnly,V=O.errors;$?x.push.apply(x,j(V)):R.push.apply(R,j(V))}),R.length?Promise.reject({name:w,errors:R,warnings:x}):{name:w,errors:R,warnings:x}}))}}});var p=gs(c);e.lastValidatePromise=p,p.catch(function(g){return g}).then(function(g){var w=g.map(function(P){var E=P.name;return E});e.notifyObservers(e.store,w,{type:"validateFinish"}),e.triggerOnFieldsChange(w,g)});var d=p.then(function(){return e.lastValidatePromise===p?Promise.resolve(e.getFieldsValue(l)):Promise.reject([])}).catch(function(g){var w=g.filter(function(P){return P&&P.errors.length});return Promise.reject({values:e.getFieldsValue(l),errorFields:w,outOfDate:e.lastValidatePromise!==p})});d.catch(function(g){return g});var b=l.filter(function(g){return v.has(g.join(u))});return e.triggerOnFieldsChange(b),d}),C(this,"submit",function(){e.warningUnhooked(),e.validateFields().then(function(a){var n=e.callbacks.onFinish;if(n)try{n(a)}catch(i){console.error(i)}}).catch(function(a){var n=e.callbacks.onFinishFailed;n&&n(a)})}),this.forceRootUpdate=t});function Ta(r){var t=f.useRef(),e=f.useState({}),a=q(e,2),n=a[1];if(!t.current)if(r)t.current=r;else{var i=function(){n({})},o=new ys(i);t.current=o.getForm()}return[t.current]}var Rn=f.createContext({triggerFormChange:function(){},triggerFormFinish:function(){},registerForm:function(){},unregisterForm:function(){}}),Aa=function(t){var e=t.validateMessages,a=t.onFormChange,n=t.onFormFinish,i=t.children,o=f.useContext(Rn),s=f.useRef({});return f.createElement(Rn.Provider,{value:S(S({},o),{},{validateMessages:S(S({},o.validateMessages),e),triggerFormChange:function(c,u){a&&a(c,{changedFields:u,forms:s.current}),o.triggerFormChange(c,u)},triggerFormFinish:function(c,u){n&&n(c,{values:u,forms:s.current}),o.triggerFormFinish(c,u)},registerForm:function(c,u){c&&(s.current=S(S({},s.current),{},C({},c,u))),o.registerForm(c,u)},unregisterForm:function(c){var u=S({},s.current);delete u[c],s.current=u,o.unregisterForm(c)}})},i)},bs=["name","initialValues","fields","form","preserve","children","component","validateMessages","validateTrigger","onValuesChange","onFieldsChange","onFinish","onFinishFailed","clearOnDestroy"],ws=function(t,e){var a=t.name,n=t.initialValues,i=t.fields,o=t.form,s=t.preserve,l=t.children,c=t.component,u=c===void 0?"form":c,v=t.validateMessages,h=t.validateTrigger,y=h===void 0?"onChange":h,m=t.onValuesChange,p=t.onFieldsChange,d=t.onFinish,b=t.onFinishFailed,g=t.clearOnDestroy,w=nt(t,bs),P=f.useRef(null),E=f.useContext(Rn),F=Ta(o),R=q(F,1),x=R[0],O=x.getInternalHooks(Rt),$=O.useSubscribe,V=O.setInitialValues,L=O.setCallbacks,_=O.setValidateMessages,k=O.setPreserve,Z=O.destroyForm;f.useImperativeHandle(e,function(){return S(S({},x),{},{nativeElement:P.current})}),f.useEffect(function(){return E.registerForm(a,x),function(){E.unregisterForm(a)}},[E,x,a]),_(S(S({},E.validateMessages),v)),L({onValuesChange:m,onFieldsChange:function(T){if(E.triggerFormChange(a,T),p){for(var B=arguments.length,be=new Array(B>1?B-1:0),we=1;we<B;we++)be[we-1]=arguments[we];p.apply(void 0,[T].concat(be))}},onFinish:function(T){E.triggerFormFinish(a,T),d&&d(T)},onFinishFailed:b}),k(s);var z=f.useRef(null);V(n,!z.current),z.current||(z.current=!0),f.useEffect(function(){return function(){return Z(g)}},[]);var U,ee=typeof l=="function";if(ee){var te=x.getFieldsValue(!0);U=l(te,x)}else U=l;$(!ee);var W=f.useRef();f.useEffect(function(){ds(W.current||[],i||[])||x.setFields(i||[]),W.current=i},[i,x]);var Re=f.useMemo(function(){return S(S({},x),{},{validateTrigger:y})},[x,y]),ue=f.createElement(Ar.Provider,{value:null},f.createElement(qt.Provider,{value:Re},U));return u===!1?ue:f.createElement(u,Ne({},w,{ref:P,onSubmit:function(T){T.preventDefault(),T.stopPropagation(),x.submit()},onReset:function(T){var B;T.preventDefault(),x.resetFields(),(B=w.onReset)===null||B===void 0||B.call(w,T)}}),ue)};function ra(r){try{return JSON.stringify(r)}catch{return Math.random()}}function Cs(){for(var r=arguments.length,t=new Array(r),e=0;e<r;e++)t[e]=arguments[e];var a=t[0],n=t[1],i=n===void 0?{}:n,o=Mo(i)?{form:i}:i,s=o.form,l=f.useState(),c=q(l,2),u=c[0],v=c[1],h=f.useMemo(function(){return ra(u)},[u]),y=f.useRef(h);y.current=h;var m=f.useContext(qt),p=s||m,d=p&&p._init,b=ce(a),g=f.useRef(b);return g.current=b,f.useEffect(function(){if(d){var w=p.getFieldsValue,P=p.getInternalHooks,E=P(Rt),F=E.registerWatch,R=function(V,L){var _=o.preserve?L:V;return typeof a=="function"?a(_):Ot(_,g.current)},x=F(function($,V){var L=R($,V),_=ra(L);y.current!==_&&(y.current=_,v(L))}),O=R(w(),w(!0));return u!==O&&v(O),x}},[d]),u}var Fs=f.forwardRef(ws),fr=Fs;fr.FormProvider=Aa;fr.Field=ka;fr.List=hs;fr.useForm=Ta;fr.useWatch=Cs;const _l=f.createContext({labelAlign:"right",vertical:!1,itemRef:()=>{}}),Nl=f.createContext(null),kl=r=>{const t=ui(r,["prefixCls"]);return f.createElement(Aa,Object.assign({},t))},Il=f.createContext({prefixCls:""}),na=f.createContext({}),Es=r=>{let{children:t,status:e,override:a}=r;const n=f.useContext(na),i=f.useMemo(()=>{const o=Object.assign({},n);return a&&delete o.isFormItemInput,e&&(delete o.status,delete o.hasFeedback,delete o.feedbackIcon),o},[e,a,n]);return f.createElement(na.Provider,{value:i},t)},Tl=f.createContext(void 0),Ps=r=>{const{space:t,form:e,children:a}=r;if(a==null)return null;let n=a;return e&&(n=Nr.createElement(Es,{override:!0,status:!0},n)),t&&(n=Nr.createElement(ci,null,n)),n};function Os(r){return t=>f.createElement(Qa,{theme:{token:{motion:!1,zIndexPopupBase:0}}},f.createElement(r,Object.assign({},t)))}const Al=(r,t,e,a,n)=>Os(o=>{const{prefixCls:s,style:l}=o,c=f.useRef(null),[u,v]=f.useState(0),[h,y]=f.useState(0),[m,p]=Sa(!1,{value:o.open}),{getPrefixCls:d}=f.useContext(ga),b=d(a||"select",s);f.useEffect(()=>{if(p(!0),typeof ResizeObserver<"u"){const P=new ResizeObserver(F=>{const R=F[0].target;v(R.offsetHeight+8),y(R.offsetWidth)}),E=setInterval(()=>{var F;const R=n?`.${n(b)}`:`.${b}-dropdown`,x=(F=c.current)===null||F===void 0?void 0:F.querySelector(R);x&&(clearInterval(E),P.observe(x))},10);return()=>{clearInterval(E),P.disconnect()}}},[]);let g=Object.assign(Object.assign({},o),{style:Object.assign(Object.assign({},l),{margin:0}),open:m,visible:m,getPopupContainer:()=>c.current});t&&Object.assign(g,{[t]:{overflow:{adjustX:!1,adjustY:!1}}});const w={paddingBottom:u,position:"relative",minWidth:h};return f.createElement("div",{ref:c,style:w},f.createElement(r,Object.assign({},g)))}),Rs=function(){if(typeof navigator>"u"||typeof window>"u")return!1;var r=navigator.userAgent||navigator.vendor||window.opera;return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(r)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i.test(r==null?void 0:r.substr(0,4))};var Ss=["prefixCls","invalidate","item","renderItem","responsive","responsiveDisabled","registerSize","itemKey","className","style","children","display","order","component"],At=void 0;function $s(r,t){var e=r.prefixCls,a=r.invalidate,n=r.item,i=r.renderItem,o=r.responsive,s=r.responsiveDisabled,l=r.registerSize,c=r.itemKey,u=r.className,v=r.style,h=r.children,y=r.display,m=r.order,p=r.component,d=p===void 0?"div":p,b=nt(r,Ss),g=o&&!y;function w(x){l(c,x)}f.useEffect(function(){return function(){w(null)}},[]);var P=i&&n!==At?i(n,{index:m}):h,E;a||(E={opacity:g?0:1,height:g?0:At,overflowY:g?"hidden":At,order:o?m:At,pointerEvents:g?"none":At,position:g?"absolute":At});var F={};g&&(F["aria-hidden"]=!0);var R=f.createElement(d,Ne({className:Oe(!a&&e,u),style:S(S({},E),v)},F,b,{ref:t}),P);return o&&(R=f.createElement(ur,{onResize:function(O){var $=O.offsetWidth;w($)},disabled:s},R)),R}var ir=f.forwardRef($s);ir.displayName="Item";function xs(r){if(typeof MessageChannel>"u")Ja(r);else{var t=new MessageChannel;t.port1.onmessage=function(){return r()},t.port2.postMessage(void 0)}}function Ms(){var r=f.useRef(null),t=function(a){r.current||(r.current=[],xs(function(){ma.unstable_batchedUpdates(function(){r.current.forEach(function(n){n()}),r.current=null})})),r.current.push(a)};return t}function er(r,t){var e=f.useState(t),a=q(e,2),n=a[0],i=a[1],o=rt(function(s){r(function(){i(s)})});return[n,o]}var Lr=Nr.createContext(null),Vs=["component"],_s=["className"],Ns=["className"],ks=function(t,e){var a=f.useContext(Lr);if(!a){var n=t.component,i=n===void 0?"div":n,o=nt(t,Vs);return f.createElement(i,Ne({},o,{ref:e}))}var s=a.className,l=nt(a,_s),c=t.className,u=nt(t,Ns);return f.createElement(Lr.Provider,{value:null},f.createElement(ir,Ne({ref:e,className:Oe(s,c)},l,u)))},La=f.forwardRef(ks);La.displayName="RawItem";var Is=["prefixCls","data","renderItem","renderRawItem","itemKey","itemWidth","ssr","style","className","maxCount","renderRest","renderRawRest","suffix","component","itemComponent","onVisibleChange"],Da="responsive",za="invalidate";function Ts(r){return"+ ".concat(r.length," ...")}function As(r,t){var e=r.prefixCls,a=e===void 0?"rc-overflow":e,n=r.data,i=n===void 0?[]:n,o=r.renderItem,s=r.renderRawItem,l=r.itemKey,c=r.itemWidth,u=c===void 0?10:c,v=r.ssr,h=r.style,y=r.className,m=r.maxCount,p=r.renderRest,d=r.renderRawRest,b=r.suffix,g=r.component,w=g===void 0?"div":g,P=r.itemComponent,E=r.onVisibleChange,F=nt(r,Is),R=v==="full",x=Ms(),O=er(x,null),$=q(O,2),V=$[0],L=$[1],_=V||0,k=er(x,new Map),Z=q(k,2),z=Z[0],U=Z[1],ee=er(x,0),te=q(ee,2),W=te[0],Re=te[1],ue=er(x,0),Q=q(ue,2),T=Q[0],B=Q[1],be=er(x,0),we=q(be,2),Ce=we[0],ve=we[1],We=f.useState(null),qe=q(We,2),fe=qe[0],Se=qe[1],de=f.useState(null),me=q(de,2),He=me[0],Me=me[1],ie=f.useMemo(function(){return He===null&&R?Number.MAX_SAFE_INTEGER:He||0},[He,V]),Fe=f.useState(!1),Ue=q(Fe,2),he=Ue[0],pt=Ue[1],G="".concat(a,"-item"),Je=Math.max(W,T),ke=m===Da,Y=i.length&&ke,Ie=m===za,Te=Y||typeof m=="number"&&i.length>m,Ae=f.useMemo(function(){var A=i;return Y?V===null&&R?A=i:A=i.slice(0,Math.min(i.length,_/u)):typeof m=="number"&&(A=i.slice(0,m)),A},[i,u,V,m,Y]),it=f.useMemo(function(){return Y?i.slice(ie+1):i.slice(Ae.length)},[i,Ae,Y,ie]),re=f.useCallback(function(A,N){var D;return typeof l=="function"?l(A):(D=l&&(A==null?void 0:A[l]))!==null&&D!==void 0?D:N},[l]),ne=f.useCallback(o||function(A){return A},[o]);function oe(A,N,D){He===A&&(N===void 0||N===fe)||(Me(A),D||(pt(A<i.length-1),E==null||E(A)),N!==void 0&&Se(N))}function Ve(A,N){L(N.clientWidth)}function St(A,N){U(function(D){var se=new Map(D);return N===null?se.delete(A):se.set(A,N),se})}function ot(A,N){B(N),Re(T)}function _e(A,N){ve(N)}function Be(A){return z.get(re(Ae[A],A))}$e(function(){if(_&&typeof Je=="number"&&Ae){var A=Ce,N=Ae.length,D=N-1;if(!N){oe(0,null);return}for(var se=0;se<N;se+=1){var De=Be(se);if(R&&(De=De||0),De===void 0){oe(se-1,void 0,!0);break}if(A+=De,D===0&&A<=_||se===D-1&&A+Be(D)<=_){oe(D,null);break}else if(A+Je>_){oe(se-1,A-De-Ce+T);break}}b&&Be(0)+Ce>_&&Se(null)}},[_,z,T,Ce,re,Ae]);var $t=he&&!!it.length,yt={};fe!==null&&Y&&(yt={position:"absolute",left:fe,top:0});var st={prefixCls:G,responsive:Y,component:P,invalidate:Ie},Bt=s?function(A,N){var D=re(A,N);return f.createElement(Lr.Provider,{key:D,value:S(S({},st),{},{order:N,item:A,itemKey:D,registerSize:St,display:N<=ie})},s(A,N))}:function(A,N){var D=re(A,N);return f.createElement(ir,Ne({},st,{order:N,key:D,item:A,renderItem:ne,itemKey:D,registerSize:St,display:N<=ie}))},Le={order:$t?ie:Number.MAX_SAFE_INTEGER,className:"".concat(G,"-rest"),registerSize:ot,display:$t},Ee=p||Ts,bt=d?f.createElement(Lr.Provider,{value:S(S({},st),Le)},d(it)):f.createElement(ir,Ne({},st,Le),typeof Ee=="function"?Ee(it):Ee),ye=f.createElement(w,Ne({className:Oe(!Ie&&a,y),style:h,ref:t},F),Ae.map(Bt),Te?bt:null,b&&f.createElement(ir,Ne({},st,{responsive:ke,responsiveDisabled:!Y,order:ie,className:"".concat(G,"-suffix"),registerSize:_e,display:!0,style:yt}),b));return ke?f.createElement(ur,{onResize:Ve,disabled:!Y},ye):ye}var zr=f.forwardRef(As);zr.displayName="Overflow";zr.Item=La;zr.RESPONSIVE=Da;zr.INVALIDATE=za;function Ls(r){var t=r.prefixCls,e=r.align,a=r.arrow,n=r.arrowPos,i=a||{},o=i.className,s=i.content,l=n.x,c=l===void 0?0:l,u=n.y,v=u===void 0?0:u,h=f.useRef();if(!e||!e.points)return null;var y={position:"absolute"};if(e.autoArrow!==!1){var m=e.points[0],p=e.points[1],d=m[0],b=m[1],g=p[0],w=p[1];d===g||!["t","b"].includes(d)?y.top=v:d==="t"?y.top=0:y.bottom=0,b===w||!["l","r"].includes(b)?y.left=c:b==="l"?y.left=0:y.right=0}return f.createElement("div",{ref:h,className:Oe("".concat(t,"-arrow"),o),style:y},s)}function Ds(r){var t=r.prefixCls,e=r.open,a=r.zIndex,n=r.mask,i=r.motion;return n?f.createElement(pa,Ne({},i,{motionAppear:!0,visible:e,removeOnLeave:!0}),function(o){var s=o.className;return f.createElement("div",{style:{zIndex:a},className:Oe("".concat(t,"-mask"),s)})}):null}var zs=f.memo(function(r){var t=r.children;return t},function(r,t){return t.cache}),js=f.forwardRef(function(r,t){var e=r.popup,a=r.className,n=r.prefixCls,i=r.style,o=r.target,s=r.onVisibleChanged,l=r.open,c=r.keepDom,u=r.fresh,v=r.onClick,h=r.mask,y=r.arrow,m=r.arrowPos,p=r.align,d=r.motion,b=r.maskMotion,g=r.forceRender,w=r.getPopupContainer,P=r.autoDestroy,E=r.portal,F=r.zIndex,R=r.onMouseEnter,x=r.onMouseLeave,O=r.onPointerEnter,$=r.onPointerDownCapture,V=r.ready,L=r.offsetX,_=r.offsetY,k=r.offsetR,Z=r.offsetB,z=r.onAlign,U=r.onPrepare,ee=r.stretch,te=r.targetWidth,W=r.targetHeight,Re=typeof e=="function"?e():e,ue=l||c,Q=(w==null?void 0:w.length)>0,T=f.useState(!w||!Q),B=q(T,2),be=B[0],we=B[1];if($e(function(){!be&&Q&&o&&we(!0)},[be,Q,o]),!be)return null;var Ce="auto",ve={left:"-1000vw",top:"-1000vh",right:Ce,bottom:Ce};if(V||!l){var We,qe=p.points,fe=p.dynamicInset||((We=p._experimental)===null||We===void 0?void 0:We.dynamicInset),Se=fe&&qe[0][1]==="r",de=fe&&qe[0][0]==="b";Se?(ve.right=k,ve.left=Ce):(ve.left=L,ve.right=Ce),de?(ve.bottom=Z,ve.top=Ce):(ve.top=_,ve.bottom=Ce)}var me={};return ee&&(ee.includes("height")&&W?me.height=W:ee.includes("minHeight")&&W&&(me.minHeight=W),ee.includes("width")&&te?me.width=te:ee.includes("minWidth")&&te&&(me.minWidth=te)),l||(me.pointerEvents="none"),f.createElement(E,{open:g||ue,getContainer:w&&function(){return w(o)},autoDestroy:P},f.createElement(Ds,{prefixCls:n,open:l,zIndex:F,mask:h,motion:b}),f.createElement(ur,{onResize:z,disabled:!l},function(He){return f.createElement(pa,Ne({motionAppear:!0,motionEnter:!0,motionLeave:!0,removeOnLeave:!1,forceRender:g,leavedClassName:"".concat(n,"-hidden")},d,{onAppearPrepare:U,onEnterPrepare:U,visible:l,onVisibleChanged:function(ie){var Fe;d==null||(Fe=d.onVisibleChanged)===null||Fe===void 0||Fe.call(d,ie),s(ie)}}),function(Me,ie){var Fe=Me.className,Ue=Me.style,he=Oe(n,Fe,a);return f.createElement("div",{ref:Xa(He,t,ie),className:he,style:S(S(S(S({"--arrow-x":"".concat(m.x||0,"px"),"--arrow-y":"".concat(m.y||0,"px")},ve),me),Ue),{},{boxSizing:"border-box",zIndex:F},i),onMouseEnter:R,onMouseLeave:x,onPointerEnter:O,onClick:v,onPointerDownCapture:$},y&&f.createElement(Ls,{prefixCls:n,arrow:y,arrowPos:m,align:p}),f.createElement(zs,{cache:!l&&!u},Re))})}))}),Ws=f.forwardRef(function(r,t){var e=r.children,a=r.getTriggerDOMNode,n=Mn(e),i=f.useCallback(function(s){Ya(t,a?a(s):s)},[a]),o=Vn(i,da(e));return n?f.cloneElement(e,{ref:o}):e}),aa=f.createContext(null);function ia(r){return r?Array.isArray(r)?r:[r]:[]}function qs(r,t,e,a){return f.useMemo(function(){var n=ia(e??t),i=ia(a??t),o=new Set(n),s=new Set(i);return r&&(o.has("hover")&&(o.delete("hover"),o.add("click")),s.has("hover")&&(s.delete("hover"),s.add("click"))),[o,s]},[r,t,e,a])}function Hs(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],e=arguments.length>2?arguments[2]:void 0;return e?r[0]===t[0]:r[0]===t[0]&&r[1]===t[1]}function Us(r,t,e,a){for(var n=e.points,i=Object.keys(r),o=0;o<i.length;o+=1){var s,l=i[o];if(Hs((s=r[l])===null||s===void 0?void 0:s.points,n,a))return"".concat(t,"-placement-").concat(l)}return""}function oa(r,t,e,a){return t||(e?{motionName:"".concat(r,"-").concat(e)}:a?{motionName:a}:null)}function dr(r){return r.ownerDocument.defaultView}function Sn(r){for(var t=[],e=r==null?void 0:r.parentElement,a=["hidden","scroll","clip","auto"];e;){var n=dr(e).getComputedStyle(e),i=n.overflowX,o=n.overflowY,s=n.overflow;[i,o,s].some(function(l){return a.includes(l)})&&t.push(e),e=e.parentElement}return t}function sr(r){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;return Number.isNaN(r)?t:r}function tr(r){return sr(parseFloat(r),0)}function sa(r,t){var e=S({},r);return(t||[]).forEach(function(a){if(!(a instanceof HTMLBodyElement||a instanceof HTMLHtmlElement)){var n=dr(a).getComputedStyle(a),i=n.overflow,o=n.overflowClipMargin,s=n.borderTopWidth,l=n.borderBottomWidth,c=n.borderLeftWidth,u=n.borderRightWidth,v=a.getBoundingClientRect(),h=a.offsetHeight,y=a.clientHeight,m=a.offsetWidth,p=a.clientWidth,d=tr(s),b=tr(l),g=tr(c),w=tr(u),P=sr(Math.round(v.width/m*1e3)/1e3),E=sr(Math.round(v.height/h*1e3)/1e3),F=(m-p-g-w)*P,R=(h-y-d-b)*E,x=d*E,O=b*E,$=g*P,V=w*P,L=0,_=0;if(i==="clip"){var k=tr(o);L=k*P,_=k*E}var Z=v.x+$-L,z=v.y+x-_,U=Z+v.width+2*L-$-V-F,ee=z+v.height+2*_-x-O-R;e.left=Math.max(e.left,Z),e.top=Math.max(e.top,z),e.right=Math.min(e.right,U),e.bottom=Math.min(e.bottom,ee)}}),e}function la(r){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,e="".concat(t),a=e.match(/^(.*)\%$/);return a?r*(parseFloat(a[1])/100):parseFloat(e)}function ua(r,t){var e=t||[],a=q(e,2),n=a[0],i=a[1];return[la(r.width,n),la(r.height,i)]}function ca(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return[r[0],r[1]]}function Lt(r,t){var e=t[0],a=t[1],n,i;return e==="t"?i=r.y:e==="b"?i=r.y+r.height:i=r.y+r.height/2,a==="l"?n=r.x:a==="r"?n=r.x+r.width:n=r.x+r.width/2,{x:n,y:i}}function vt(r,t){var e={t:"b",b:"t",l:"r",r:"l"};return r.map(function(a,n){return n===t?e[a]||"c":a}).join("")}function Bs(r,t,e,a,n,i,o){var s=f.useState({ready:!1,offsetX:0,offsetY:0,offsetR:0,offsetB:0,arrowX:0,arrowY:0,scaleX:1,scaleY:1,align:n[a]||{}}),l=q(s,2),c=l[0],u=l[1],v=f.useRef(0),h=f.useMemo(function(){return t?Sn(t):[]},[t]),y=f.useRef({}),m=function(){y.current={}};r||m();var p=rt(function(){if(t&&e&&r){let Ge=function(kt,ft){var dt=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Je,Yt=T.x+kt,Sr=T.y+ft,en=Yt+Se,tn=Sr+fe,rn=Math.max(Yt,dt.left),M=Math.max(Sr,dt.top),I=Math.min(en,dt.right),ae=Math.min(tn,dt.bottom);return Math.max(0,(I-rn)*(ae-M))},Rr=function(){wt=T.y+D,Ct=wt+fe,lt=T.x+N,Mt=lt+Se};var g,w,P,E,F=t,R=F.ownerDocument,x=dr(F),O=x.getComputedStyle(F),$=O.width,V=O.height,L=O.position,_=F.style.left,k=F.style.top,Z=F.style.right,z=F.style.bottom,U=F.style.overflow,ee=S(S({},n[a]),i),te=R.createElement("div");(g=F.parentElement)===null||g===void 0||g.appendChild(te),te.style.left="".concat(F.offsetLeft,"px"),te.style.top="".concat(F.offsetTop,"px"),te.style.position=L,te.style.height="".concat(F.offsetHeight,"px"),te.style.width="".concat(F.offsetWidth,"px"),F.style.left="0",F.style.top="0",F.style.right="auto",F.style.bottom="auto",F.style.overflow="hidden";var W;if(Array.isArray(e))W={x:e[0],y:e[1],width:0,height:0};else{var Re,ue,Q=e.getBoundingClientRect();Q.x=(Re=Q.x)!==null&&Re!==void 0?Re:Q.left,Q.y=(ue=Q.y)!==null&&ue!==void 0?ue:Q.top,W={x:Q.x,y:Q.y,width:Q.width,height:Q.height}}var T=F.getBoundingClientRect();T.x=(w=T.x)!==null&&w!==void 0?w:T.left,T.y=(P=T.y)!==null&&P!==void 0?P:T.top;var B=R.documentElement,be=B.clientWidth,we=B.clientHeight,Ce=B.scrollWidth,ve=B.scrollHeight,We=B.scrollTop,qe=B.scrollLeft,fe=T.height,Se=T.width,de=W.height,me=W.width,He={left:0,top:0,right:be,bottom:we},Me={left:-qe,top:-We,right:Ce-qe,bottom:ve-We},ie=ee.htmlRegion,Fe="visible",Ue="visibleFirst";ie!=="scroll"&&ie!==Ue&&(ie=Fe);var he=ie===Ue,pt=sa(Me,h),G=sa(He,h),Je=ie===Fe?G:pt,ke=he?G:Je;F.style.left="auto",F.style.top="auto",F.style.right="0",F.style.bottom="0";var Y=F.getBoundingClientRect();F.style.left=_,F.style.top=k,F.style.right=Z,F.style.bottom=z,F.style.overflow=U,(E=F.parentElement)===null||E===void 0||E.removeChild(te);var Ie=sr(Math.round(Se/parseFloat($)*1e3)/1e3),Te=sr(Math.round(fe/parseFloat(V)*1e3)/1e3);if(Ie===0||Te===0||vn(e)&&!fi(e))return;var Ae=ee.offset,it=ee.targetOffset,re=ua(T,Ae),ne=q(re,2),oe=ne[0],Ve=ne[1],St=ua(W,it),ot=q(St,2),_e=ot[0],Be=ot[1];W.x-=_e,W.y-=Be;var $t=ee.points||[],yt=q($t,2),st=yt[0],Bt=yt[1],Le=ca(Bt),Ee=ca(st),bt=Lt(W,Le),ye=Lt(T,Ee),A=S({},ee),N=bt.x-ye.x+oe,D=bt.y-ye.y+Ve,se=Ge(N,D),De=Ge(N,D,G),Kt=Lt(W,["t","l"]),Ke=Lt(T,["t","l"]),vr=Lt(W,["b","r"]),Gt=Lt(T,["b","r"]),ct=ee.overflow||{},mr=ct.adjustX,jr=ct.adjustY,Zt=ct.shiftX,xt=ct.shiftY,Qt=function(ft){return typeof ft=="boolean"?ft:ft>=0},wt,Ct,lt,Mt;Rr();var Ft=Qt(jr),hr=Ee[0]===Le[0];if(Ft&&Ee[0]==="t"&&(Ct>ke.bottom||y.current.bt)){var Pe=D;hr?Pe-=fe-de:Pe=Kt.y-Gt.y-Ve;var gr=Ge(N,Pe),Wr=Ge(N,Pe,G);gr>se||gr===se&&(!he||Wr>=De)?(y.current.bt=!0,D=Pe,Ve=-Ve,A.points=[vt(Ee,0),vt(Le,0)]):y.current.bt=!1}if(Ft&&Ee[0]==="b"&&(wt<ke.top||y.current.tb)){var Et=D;hr?Et+=fe-de:Et=vr.y-Ke.y-Ve;var pr=Ge(N,Et),qr=Ge(N,Et,G);pr>se||pr===se&&(!he||qr>=De)?(y.current.tb=!0,D=Et,Ve=-Ve,A.points=[vt(Ee,0),vt(Le,0)]):y.current.tb=!1}var yr=Qt(mr),br=Ee[1]===Le[1];if(yr&&Ee[1]==="l"&&(Mt>ke.right||y.current.rl)){var Pt=N;br?Pt-=Se-me:Pt=Kt.x-Gt.x-oe;var wr=Ge(Pt,D),Vt=Ge(Pt,D,G);wr>se||wr===se&&(!he||Vt>=De)?(y.current.rl=!0,N=Pt,oe=-oe,A.points=[vt(Ee,1),vt(Le,1)]):y.current.rl=!1}if(yr&&Ee[1]==="r"&&(lt<ke.left||y.current.lr)){var ut=N;br?ut+=Se-me:ut=vr.x-Ke.x-oe;var Cr=Ge(ut,D),Fr=Ge(ut,D,G);Cr>se||Cr===se&&(!he||Fr>=De)?(y.current.lr=!0,N=ut,oe=-oe,A.points=[vt(Ee,1),vt(Le,1)]):y.current.lr=!1}Rr();var Ye=Zt===!0?0:Zt;typeof Ye=="number"&&(lt<G.left&&(N-=lt-G.left-oe,W.x+me<G.left+Ye&&(N+=W.x-G.left+me-Ye)),Mt>G.right&&(N-=Mt-G.right-oe,W.x>G.right-Ye&&(N+=W.x-G.right+Ye)));var Xe=xt===!0?0:xt;typeof Xe=="number"&&(wt<G.top&&(D-=wt-G.top-Ve,W.y+de<G.top+Xe&&(D+=W.y-G.top+de-Xe)),Ct>G.bottom&&(D-=Ct-G.bottom-Ve,W.y>G.bottom-Xe&&(D+=W.y-G.bottom+Xe)));var _t=T.x+N,Nt=_t+Se,et=T.y+D,Hr=et+fe,Er=W.x,Ur=Er+me,Jt=W.y,Br=Jt+de,Kr=Math.max(_t,Er),Gr=Math.min(Nt,Ur),Pr=(Kr+Gr)/2,Zr=Pr-_t,Qr=Math.max(et,Jt),Or=Math.min(Hr,Br),Jr=(Qr+Or)/2,Xr=Jr-et;o==null||o(t,A);var Xt=Y.right-T.x-(N+T.width),tt=Y.bottom-T.y-(D+T.height);Ie===1&&(N=Math.round(N),Xt=Math.round(Xt)),Te===1&&(D=Math.round(D),tt=Math.round(tt));var Yr={ready:!0,offsetX:N/Ie,offsetY:D/Te,offsetR:Xt/Ie,offsetB:tt/Te,arrowX:Zr/Ie,arrowY:Xr/Te,scaleX:Ie,scaleY:Te,align:A};u(Yr)}}),d=function(){v.current+=1;var w=v.current;Promise.resolve().then(function(){v.current===w&&p()})},b=function(){u(function(w){return S(S({},w),{},{ready:!1})})};return $e(b,[a]),$e(function(){r||b()},[r]),[c.ready,c.offsetX,c.offsetY,c.offsetR,c.offsetB,c.arrowX,c.arrowY,c.scaleX,c.scaleY,c.align,d]}function Ks(r,t,e,a,n){$e(function(){if(r&&t&&e){let v=function(){a(),n()};var i=t,o=e,s=Sn(i),l=Sn(o),c=dr(o),u=new Set([c].concat(j(s),j(l)));return u.forEach(function(h){h.addEventListener("scroll",v,{passive:!0})}),c.addEventListener("resize",v,{passive:!0}),a(),function(){u.forEach(function(h){h.removeEventListener("scroll",v),c.removeEventListener("resize",v)})}}},[r,t,e])}function Gs(r,t,e,a,n,i,o,s){var l=f.useRef(r);l.current=r;var c=f.useRef(!1);f.useEffect(function(){if(t&&a&&(!n||i)){var v=function(){c.current=!1},h=function(d){var b;l.current&&!o(((b=d.composedPath)===null||b===void 0||(b=b.call(d))===null||b===void 0?void 0:b[0])||d.target)&&!c.current&&s(!1)},y=dr(a);y.addEventListener("pointerdown",v,!0),y.addEventListener("mousedown",h,!0),y.addEventListener("contextmenu",h,!0);var m=mn(e);return m&&(m.addEventListener("mousedown",h,!0),m.addEventListener("contextmenu",h,!0)),function(){y.removeEventListener("pointerdown",v,!0),y.removeEventListener("mousedown",h,!0),y.removeEventListener("contextmenu",h,!0),m&&(m.removeEventListener("mousedown",h,!0),m.removeEventListener("contextmenu",h,!0))}}},[t,e,a,n,i]);function u(){c.current=!0}return u}var Zs=["prefixCls","children","action","showAction","hideAction","popupVisible","defaultPopupVisible","onPopupVisibleChange","afterPopupVisibleChange","mouseEnterDelay","mouseLeaveDelay","focusDelay","blurDelay","mask","maskClosable","getPopupContainer","forceRender","autoDestroy","destroyPopupOnHide","popup","popupClassName","popupStyle","popupPlacement","builtinPlacements","popupAlign","zIndex","stretch","getPopupClassNameFromAlign","fresh","alignPoint","onPopupClick","onPopupAlign","arrow","popupMotion","maskMotion","popupTransitionName","popupAnimation","maskTransitionName","maskAnimation","className","getTriggerDOMNode"];function Qs(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:xa,t=f.forwardRef(function(e,a){var n=e.prefixCls,i=n===void 0?"rc-trigger-popup":n,o=e.children,s=e.action,l=s===void 0?"hover":s,c=e.showAction,u=e.hideAction,v=e.popupVisible,h=e.defaultPopupVisible,y=e.onPopupVisibleChange,m=e.afterPopupVisibleChange,p=e.mouseEnterDelay,d=e.mouseLeaveDelay,b=d===void 0?.1:d,g=e.focusDelay,w=e.blurDelay,P=e.mask,E=e.maskClosable,F=E===void 0?!0:E,R=e.getPopupContainer,x=e.forceRender,O=e.autoDestroy,$=e.destroyPopupOnHide,V=e.popup,L=e.popupClassName,_=e.popupStyle,k=e.popupPlacement,Z=e.builtinPlacements,z=Z===void 0?{}:Z,U=e.popupAlign,ee=e.zIndex,te=e.stretch,W=e.getPopupClassNameFromAlign,Re=e.fresh,ue=e.alignPoint,Q=e.onPopupClick,T=e.onPopupAlign,B=e.arrow,be=e.popupMotion,we=e.maskMotion,Ce=e.popupTransitionName,ve=e.popupAnimation,We=e.maskTransitionName,qe=e.maskAnimation,fe=e.className,Se=e.getTriggerDOMNode,de=nt(e,Zs),me=O||$||!1,He=f.useState(!1),Me=q(He,2),ie=Me[0],Fe=Me[1];$e(function(){Fe(Rs())},[]);var Ue=f.useRef({}),he=f.useContext(aa),pt=f.useMemo(function(){return{registerSubPopup:function(I,ae){Ue.current[I]=ae,he==null||he.registerSubPopup(I,ae)}}},[he]),G=Ma(),Je=f.useState(null),ke=q(Je,2),Y=ke[0],Ie=ke[1],Te=f.useRef(null),Ae=rt(function(M){Te.current=M,vn(M)&&Y!==M&&Ie(M),he==null||he.registerSubPopup(G,M)}),it=f.useState(null),re=q(it,2),ne=re[0],oe=re[1],Ve=f.useRef(null),St=rt(function(M){vn(M)&&ne!==M&&(oe(M),Ve.current=M)}),ot=f.Children.only(o),_e=(ot==null?void 0:ot.props)||{},Be={},$t=rt(function(M){var I,ae,ge=ne;return(ge==null?void 0:ge.contains(M))||((I=mn(ge))===null||I===void 0?void 0:I.host)===M||M===ge||(Y==null?void 0:Y.contains(M))||((ae=mn(Y))===null||ae===void 0?void 0:ae.host)===M||M===Y||Object.values(Ue.current).some(function(le){return(le==null?void 0:le.contains(M))||M===le})}),yt=oa(i,be,ve,Ce),st=oa(i,we,qe,We),Bt=f.useState(h||!1),Le=q(Bt,2),Ee=Le[0],bt=Le[1],ye=v??Ee,A=rt(function(M){v===void 0&&bt(M)});$e(function(){bt(v||!1)},[v]);var N=f.useRef(ye);N.current=ye;var D=f.useRef([]);D.current=[];var se=rt(function(M){var I;A(M),((I=D.current[D.current.length-1])!==null&&I!==void 0?I:ye)!==M&&(D.current.push(M),y==null||y(M))}),De=f.useRef(),Kt=function(){clearTimeout(De.current)},Ke=function(I){var ae=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;Kt(),ae===0?se(I):De.current=setTimeout(function(){se(I)},ae*1e3)};f.useEffect(function(){return Kt},[]);var vr=f.useState(!1),Gt=q(vr,2),ct=Gt[0],mr=Gt[1];$e(function(M){(!M||ye)&&mr(!0)},[ye]);var jr=f.useState(null),Zt=q(jr,2),xt=Zt[0],Qt=Zt[1],wt=f.useState(null),Ct=q(wt,2),lt=Ct[0],Mt=Ct[1],Ft=function(I){Mt([I.clientX,I.clientY])},hr=Bs(ye,Y,ue&&lt!==null?lt:ne,k,z,U,T),Pe=q(hr,11),gr=Pe[0],Wr=Pe[1],Et=Pe[2],pr=Pe[3],qr=Pe[4],yr=Pe[5],br=Pe[6],Pt=Pe[7],wr=Pe[8],Vt=Pe[9],ut=Pe[10],Cr=qs(ie,l,c,u),Fr=q(Cr,2),Ye=Fr[0],Xe=Fr[1],_t=Ye.has("click"),Nt=Xe.has("click")||Xe.has("contextMenu"),et=rt(function(){ct||ut()}),Hr=function(){N.current&&ue&&Nt&&Ke(!1)};Ks(ye,ne,Y,et,Hr),$e(function(){et()},[lt,k]),$e(function(){ye&&!(z!=null&&z[k])&&et()},[JSON.stringify(U)]);var Er=f.useMemo(function(){var M=Us(z,i,Vt,ue);return Oe(M,W==null?void 0:W(Vt))},[Vt,W,z,i,ue]);f.useImperativeHandle(a,function(){return{nativeElement:Ve.current,popupElement:Te.current,forceAlign:et}});var Ur=f.useState(0),Jt=q(Ur,2),Br=Jt[0],Kr=Jt[1],Gr=f.useState(0),Pr=q(Gr,2),Zr=Pr[0],Qr=Pr[1],Or=function(){if(te&&ne){var I=ne.getBoundingClientRect();Kr(I.width),Qr(I.height)}},Jr=function(){Or(),et()},Xr=function(I){mr(!1),ut(),m==null||m(I)},Xt=function(){return new Promise(function(I){Or(),Qt(function(){return I})})};$e(function(){xt&&(ut(),xt(),Qt(null))},[xt]);function tt(M,I,ae,ge){Be[M]=function(le){var $r;ge==null||ge(le),Ke(I,ae);for(var nn=arguments.length,kn=new Array(nn>1?nn-1:0),xr=1;xr<nn;xr++)kn[xr-1]=arguments[xr];($r=_e[M])===null||$r===void 0||$r.call.apply($r,[_e,le].concat(kn))}}(_t||Nt)&&(Be.onClick=function(M){var I;N.current&&Nt?Ke(!1):!N.current&&_t&&(Ft(M),Ke(!0));for(var ae=arguments.length,ge=new Array(ae>1?ae-1:0),le=1;le<ae;le++)ge[le-1]=arguments[le];(I=_e.onClick)===null||I===void 0||I.call.apply(I,[_e,M].concat(ge))});var Yr=Gs(ye,Nt,ne,Y,P,F,$t,Ke),Ge=Ye.has("hover"),Rr=Xe.has("hover"),kt,ft;Ge&&(tt("onMouseEnter",!0,p,function(M){Ft(M)}),tt("onPointerEnter",!0,p,function(M){Ft(M)}),kt=function(I){(ye||ct)&&Y!==null&&Y!==void 0&&Y.contains(I.target)&&Ke(!0,p)},ue&&(Be.onMouseMove=function(M){var I;(I=_e.onMouseMove)===null||I===void 0||I.call(_e,M)})),Rr&&(tt("onMouseLeave",!1,b),tt("onPointerLeave",!1,b),ft=function(){Ke(!1,b)}),Ye.has("focus")&&tt("onFocus",!0,g),Xe.has("focus")&&tt("onBlur",!1,w),Ye.has("contextMenu")&&(Be.onContextMenu=function(M){var I;N.current&&Xe.has("contextMenu")?Ke(!1):(Ft(M),Ke(!0)),M.preventDefault();for(var ae=arguments.length,ge=new Array(ae>1?ae-1:0),le=1;le<ae;le++)ge[le-1]=arguments[le];(I=_e.onContextMenu)===null||I===void 0||I.call.apply(I,[_e,M].concat(ge))}),fe&&(Be.className=Oe(_e.className,fe));var dt=S(S({},_e),Be),Yt={},Sr=["onContextMenu","onClick","onMouseDown","onTouchStart","onMouseEnter","onMouseLeave","onFocus","onBlur"];Sr.forEach(function(M){de[M]&&(Yt[M]=function(){for(var I,ae=arguments.length,ge=new Array(ae),le=0;le<ae;le++)ge[le]=arguments[le];(I=dt[M])===null||I===void 0||I.call.apply(I,[dt].concat(ge)),de[M].apply(de,ge)})});var en=f.cloneElement(ot,S(S({},dt),Yt)),tn={x:yr,y:br},rn=B?S({},B!==!0?B:{}):null;return f.createElement(f.Fragment,null,f.createElement(ur,{disabled:!ye,ref:St,onResize:Jr},f.createElement(Ws,{getTriggerDOMNode:Se},en)),f.createElement(aa.Provider,{value:pt},f.createElement(js,{portal:r,ref:Ae,prefixCls:i,popup:V,className:Oe(L,Er),style:_,target:ne,onMouseEnter:kt,onMouseLeave:ft,onPointerEnter:kt,zIndex:ee,open:ye,keepDom:ct,fresh:Re,onClick:Q,onPointerDownCapture:Yr,mask:P,motion:yt,maskMotion:st,onVisibleChanged:Xr,onPrepare:Xt,forceRender:x,autoDestroy:me,getPopupContainer:R,align:Vt,arrow:rn,arrowPos:tn,ready:gr,offsetX:Wr,offsetY:Et,offsetR:pr,offsetB:qr,onAlign:et,stretch:te,targetWidth:Br/Pt,targetHeight:Zr/wr})))});return t}const Js=Qs(xa);var Xs={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"},Ys=function(t,e){return f.createElement(ei,Ne({},t,{ref:e,icon:Xs}))},Ll=f.forwardRef(Ys);const el=(r,t)=>{typeof(r==null?void 0:r.addEventListener)<"u"?r.addEventListener("change",t):typeof(r==null?void 0:r.addListener)<"u"&&r.addListener(t)},tl=(r,t)=>{typeof(r==null?void 0:r.removeEventListener)<"u"?r.removeEventListener("change",t):typeof(r==null?void 0:r.removeListener)<"u"&&r.removeListener(t)},rl=["xxl","xl","lg","md","sm","xs"],nl=r=>({xs:`(max-width: ${r.screenXSMax}px)`,sm:`(min-width: ${r.screenSM}px)`,md:`(min-width: ${r.screenMD}px)`,lg:`(min-width: ${r.screenLG}px)`,xl:`(min-width: ${r.screenXL}px)`,xxl:`(min-width: ${r.screenXXL}px)`}),al=r=>{const t=r,e=[].concat(rl).reverse();return e.forEach((a,n)=>{const i=a.toUpperCase(),o=`screen${i}Min`,s=`screen${i}`;if(!(t[o]<=t[s]))throw new Error(`${o}<=${s} fails : !(${t[o]}<=${t[s]})`);if(n<e.length-1){const l=`screen${i}Max`;if(!(t[s]<=t[l]))throw new Error(`${s}<=${l} fails : !(${t[s]}<=${t[l]})`);const u=`screen${e[n+1].toUpperCase()}Min`;if(!(t[l]<=t[u]))throw new Error(`${l}<=${u} fails : !(${t[l]}<=${t[u]})`)}}),r},il=()=>{const[,r]=ya(),t=nl(al(r));return Nr.useMemo(()=>{const e=new Map;let a=-1,n={};return{responsiveMap:t,matchHandlers:{},dispatch(i){return n=i,e.forEach(o=>o(n)),e.size>=1},subscribe(i){return e.size||this.register(),a+=1,e.set(a,i),i(n),a},unsubscribe(i){e.delete(i),e.size||this.unregister()},register(){Object.entries(t).forEach(i=>{let[o,s]=i;const l=u=>{let{matches:v}=u;this.dispatch(Object.assign(Object.assign({},n),{[o]:v}))},c=window.matchMedia(s);el(c,l),this.matchHandlers[s]={mql:c,listener:l},l(c)})},unregister(){Object.values(t).forEach(i=>{const o=this.matchHandlers[i];tl(o==null?void 0:o.mql,o==null?void 0:o.listener)}),e.clear()}}},[r])};function ol(){const[,r]=f.useReducer(t=>t+1,0);return r}function Dl(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const e=f.useRef(t),a=ol(),n=il();return $e(()=>{const i=n.subscribe(o=>{e.current=o,r&&a()});return()=>n.unsubscribe(i)},[]),e.current}function ja(r){var t=r.children,e=r.prefixCls,a=r.id,n=r.overlayInnerStyle,i=r.bodyClassName,o=r.className,s=r.style;return f.createElement("div",{className:Oe("".concat(e,"-content"),o),style:s},f.createElement("div",{className:Oe("".concat(e,"-inner"),i),id:a,role:"tooltip",style:n},typeof t=="function"?t():t))}var Dt={shiftX:64,adjustY:1},zt={adjustX:1,shiftY:!0},Qe=[0,0],sl={left:{points:["cr","cl"],overflow:zt,offset:[-4,0],targetOffset:Qe},right:{points:["cl","cr"],overflow:zt,offset:[4,0],targetOffset:Qe},top:{points:["bc","tc"],overflow:Dt,offset:[0,-4],targetOffset:Qe},bottom:{points:["tc","bc"],overflow:Dt,offset:[0,4],targetOffset:Qe},topLeft:{points:["bl","tl"],overflow:Dt,offset:[0,-4],targetOffset:Qe},leftTop:{points:["tr","tl"],overflow:zt,offset:[-4,0],targetOffset:Qe},topRight:{points:["br","tr"],overflow:Dt,offset:[0,-4],targetOffset:Qe},rightTop:{points:["tl","tr"],overflow:zt,offset:[4,0],targetOffset:Qe},bottomRight:{points:["tr","br"],overflow:Dt,offset:[0,4],targetOffset:Qe},rightBottom:{points:["bl","br"],overflow:zt,offset:[4,0],targetOffset:Qe},bottomLeft:{points:["tl","bl"],overflow:Dt,offset:[0,4],targetOffset:Qe},leftBottom:{points:["br","bl"],overflow:zt,offset:[-4,0],targetOffset:Qe}},ll=["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","children","onVisibleChange","afterVisibleChange","transitionName","animation","motion","placement","align","destroyTooltipOnHide","defaultVisible","getTooltipContainer","overlayInnerStyle","arrowContent","overlay","id","showArrow","classNames","styles"],ul=function(t,e){var a=t.overlayClassName,n=t.trigger,i=n===void 0?["hover"]:n,o=t.mouseEnterDelay,s=o===void 0?0:o,l=t.mouseLeaveDelay,c=l===void 0?.1:l,u=t.overlayStyle,v=t.prefixCls,h=v===void 0?"rc-tooltip":v,y=t.children,m=t.onVisibleChange,p=t.afterVisibleChange,d=t.transitionName,b=t.animation,g=t.motion,w=t.placement,P=w===void 0?"right":w,E=t.align,F=E===void 0?{}:E,R=t.destroyTooltipOnHide,x=R===void 0?!1:R,O=t.defaultVisible,$=t.getTooltipContainer,V=t.overlayInnerStyle;t.arrowContent;var L=t.overlay,_=t.id,k=t.showArrow,Z=k===void 0?!0:k,z=t.classNames,U=t.styles,ee=nt(t,ll),te=Ma(_),W=f.useRef(null);f.useImperativeHandle(e,function(){return W.current});var Re=S({},ee);"visible"in t&&(Re.popupVisible=t.visible);var ue=function(){return f.createElement(ja,{key:"content",prefixCls:h,id:te,bodyClassName:z==null?void 0:z.body,overlayInnerStyle:S(S({},V),U==null?void 0:U.body)},L)},Q=function(){var B=f.Children.only(y),be=(B==null?void 0:B.props)||{},we=S(S({},be),{},{"aria-describedby":L?te:null});return f.cloneElement(y,we)};return f.createElement(Js,Ne({popupClassName:Oe(a,z==null?void 0:z.root),prefixCls:h,popup:ue,action:i,builtinPlacements:sl,popupPlacement:P,ref:W,popupAlign:F,getPopupContainer:$,onPopupVisibleChange:m,afterPopupVisibleChange:p,popupTransitionName:d,popupAnimation:b,popupMotion:g,defaultPopupVisible:O,autoDestroy:x,mouseLeaveDelay:c,popupStyle:S(S({},u),U==null?void 0:U.root),mouseEnterDelay:s,arrow:Z},Re),Q())};const cl=f.forwardRef(ul);function fl(r){const{sizePopupArrow:t,borderRadiusXS:e,borderRadiusOuter:a}=r,n=t/2,i=0,o=n,s=a*1/Math.sqrt(2),l=n-a*(1-1/Math.sqrt(2)),c=n-e*(1/Math.sqrt(2)),u=a*(Math.sqrt(2)-1)+e*(1/Math.sqrt(2)),v=2*n-c,h=u,y=2*n-s,m=l,p=2*n-i,d=o,b=n*Math.sqrt(2)+a*(Math.sqrt(2)-2),g=a*(Math.sqrt(2)-1),w=`polygon(${g}px 100%, 50% ${g}px, ${2*n-g}px 100%, ${g}px 100%)`,P=`path('M ${i} ${o} A ${a} ${a} 0 0 0 ${s} ${l} L ${c} ${u} A ${e} ${e} 0 0 1 ${v} ${h} L ${y} ${m} A ${a} ${a} 0 0 0 ${p} ${d} Z')`;return{arrowShadowWidth:b,arrowPath:P,arrowPolygon:w}}const dl=(r,t,e)=>{const{sizePopupArrow:a,arrowPolygon:n,arrowPath:i,arrowShadowWidth:o,borderRadiusXS:s,calc:l}=r;return{pointerEvents:"none",width:a,height:a,overflow:"hidden","&::before":{position:"absolute",bottom:0,insetInlineStart:0,width:a,height:l(a).div(2).equal(),background:t,clipPath:{_multi_value_:!0,value:[n,i]},content:'""'},"&::after":{content:'""',position:"absolute",width:o,height:o,bottom:0,insetInline:0,margin:"auto",borderRadius:{_skip_check_:!0,value:`0 0 ${or(s)} 0`},transform:"translateY(50%) rotate(-135deg)",boxShadow:e,zIndex:0,background:"transparent"}}},Wa=8;function qa(r){const{contentRadius:t,limitVerticalRadius:e}=r,a=t>12?t+2:12;return{arrowOffsetHorizontal:a,arrowOffsetVertical:e?Wa:a}}function Vr(r,t){return r?t:{}}function vl(r,t,e){const{componentCls:a,boxShadowPopoverArrow:n,arrowOffsetVertical:i,arrowOffsetHorizontal:o}=r,{arrowDistance:s=0,arrowPlacement:l={left:!0,right:!0,top:!0,bottom:!0}}=e||{};return{[a]:Object.assign(Object.assign(Object.assign(Object.assign({[`${a}-arrow`]:[Object.assign(Object.assign({position:"absolute",zIndex:1,display:"block"},dl(r,t,n)),{"&:before":{background:t}})]},Vr(!!l.top,{[[`&-placement-top > ${a}-arrow`,`&-placement-topLeft > ${a}-arrow`,`&-placement-topRight > ${a}-arrow`].join(",")]:{bottom:s,transform:"translateY(100%) rotate(180deg)"},[`&-placement-top > ${a}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},"&-placement-topLeft":{"--arrow-offset-horizontal":o,[`> ${a}-arrow`]:{left:{_skip_check_:!0,value:o}}},"&-placement-topRight":{"--arrow-offset-horizontal":`calc(100% - ${or(o)})`,[`> ${a}-arrow`]:{right:{_skip_check_:!0,value:o}}}})),Vr(!!l.bottom,{[[`&-placement-bottom > ${a}-arrow`,`&-placement-bottomLeft > ${a}-arrow`,`&-placement-bottomRight > ${a}-arrow`].join(",")]:{top:s,transform:"translateY(-100%)"},[`&-placement-bottom > ${a}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(-100%)"},"&-placement-bottomLeft":{"--arrow-offset-horizontal":o,[`> ${a}-arrow`]:{left:{_skip_check_:!0,value:o}}},"&-placement-bottomRight":{"--arrow-offset-horizontal":`calc(100% - ${or(o)})`,[`> ${a}-arrow`]:{right:{_skip_check_:!0,value:o}}}})),Vr(!!l.left,{[[`&-placement-left > ${a}-arrow`,`&-placement-leftTop > ${a}-arrow`,`&-placement-leftBottom > ${a}-arrow`].join(",")]:{right:{_skip_check_:!0,value:s},transform:"translateX(100%) rotate(90deg)"},[`&-placement-left > ${a}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(100%) rotate(90deg)"},[`&-placement-leftTop > ${a}-arrow`]:{top:i},[`&-placement-leftBottom > ${a}-arrow`]:{bottom:i}})),Vr(!!l.right,{[[`&-placement-right > ${a}-arrow`,`&-placement-rightTop > ${a}-arrow`,`&-placement-rightBottom > ${a}-arrow`].join(",")]:{left:{_skip_check_:!0,value:s},transform:"translateX(-100%) rotate(-90deg)"},[`&-placement-right > ${a}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(-100%) rotate(-90deg)"},[`&-placement-rightTop > ${a}-arrow`]:{top:i},[`&-placement-rightBottom > ${a}-arrow`]:{bottom:i}}))}}function ml(r,t,e,a){if(a===!1)return{adjustX:!1,adjustY:!1};const n=a&&typeof a=="object"?a:{},i={};switch(r){case"top":case"bottom":i.shiftX=t.arrowOffsetHorizontal*2+e,i.shiftY=!0,i.adjustY=!0;break;case"left":case"right":i.shiftY=t.arrowOffsetVertical*2+e,i.shiftX=!0,i.adjustX=!0;break}const o=Object.assign(Object.assign({},i),n);return o.shiftX||(o.adjustX=!0),o.shiftY||(o.adjustY=!0),o}const fa={left:{points:["cr","cl"]},right:{points:["cl","cr"]},top:{points:["bc","tc"]},bottom:{points:["tc","bc"]},topLeft:{points:["bl","tl"]},leftTop:{points:["tr","tl"]},topRight:{points:["br","tr"]},rightTop:{points:["tl","tr"]},bottomRight:{points:["tr","br"]},rightBottom:{points:["bl","br"]},bottomLeft:{points:["tl","bl"]},leftBottom:{points:["br","bl"]}},hl={topLeft:{points:["bl","tc"]},leftTop:{points:["tr","cl"]},topRight:{points:["br","tc"]},rightTop:{points:["tl","cr"]},bottomRight:{points:["tr","bc"]},rightBottom:{points:["bl","cr"]},bottomLeft:{points:["tl","bc"]},leftBottom:{points:["br","cl"]}},gl=new Set(["topLeft","topRight","bottomLeft","bottomRight","leftTop","leftBottom","rightTop","rightBottom"]);function pl(r){const{arrowWidth:t,autoAdjustOverflow:e,arrowPointAtCenter:a,offset:n,borderRadius:i,visibleFirst:o}=r,s=t/2,l={};return Object.keys(fa).forEach(c=>{const u=a&&hl[c]||fa[c],v=Object.assign(Object.assign({},u),{offset:[0,0],dynamicInset:!0});switch(l[c]=v,gl.has(c)&&(v.autoArrow=!1),c){case"top":case"topLeft":case"topRight":v.offset[1]=-s-n;break;case"bottom":case"bottomLeft":case"bottomRight":v.offset[1]=s+n;break;case"left":case"leftTop":case"leftBottom":v.offset[0]=-s-n;break;case"right":case"rightTop":case"rightBottom":v.offset[0]=s+n;break}const h=qa({contentRadius:i,limitVerticalRadius:!0});if(a)switch(c){case"topLeft":case"bottomLeft":v.offset[0]=-h.arrowOffsetHorizontal-s;break;case"topRight":case"bottomRight":v.offset[0]=h.arrowOffsetHorizontal+s;break;case"leftTop":case"rightTop":v.offset[1]=-h.arrowOffsetHorizontal*2+s;break;case"leftBottom":case"rightBottom":v.offset[1]=h.arrowOffsetHorizontal*2-s;break}v.overflow=ml(c,h,t,e),o&&(v.htmlRegion="visibleFirst")}),l}const yl=r=>{const{calc:t,componentCls:e,tooltipMaxWidth:a,tooltipColor:n,tooltipBg:i,tooltipBorderRadius:o,zIndexPopup:s,controlHeight:l,boxShadowSecondary:c,paddingSM:u,paddingXS:v,arrowOffsetHorizontal:h,sizePopupArrow:y}=r,m=t(o).add(y).add(h).equal(),p=t(o).mul(2).add(y).equal();return[{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},ri(r)),{position:"absolute",zIndex:s,display:"block",width:"max-content",maxWidth:a,visibility:"visible","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"&-hidden":{display:"none"},"--antd-arrow-background-color":i,[`${e}-inner`]:{minWidth:p,minHeight:l,padding:`${or(r.calc(u).div(2).equal())} ${or(v)}`,color:n,textAlign:"start",textDecoration:"none",wordWrap:"break-word",backgroundColor:i,borderRadius:o,boxShadow:c,boxSizing:"border-box"},[["&-placement-topLeft","&-placement-topRight","&-placement-bottomLeft","&-placement-bottomRight"].join(",")]:{minWidth:m},[["&-placement-left","&-placement-leftTop","&-placement-leftBottom","&-placement-right","&-placement-rightTop","&-placement-rightBottom"].join(",")]:{[`${e}-inner`]:{borderRadius:r.min(o,Wa)}},[`${e}-content`]:{position:"relative"}}),Di(r,(d,b)=>{let{darkColor:g}=b;return{[`&${e}-${d}`]:{[`${e}-inner`]:{backgroundColor:g},[`${e}-arrow`]:{"--antd-arrow-background-color":g}}}})),{"&-rtl":{direction:"rtl"}})},vl(r,"var(--antd-arrow-background-color)"),{[`${e}-pure`]:{position:"relative",maxWidth:"none",margin:r.sizePopupArrow}}]},bl=r=>Object.assign(Object.assign({zIndexPopup:r.zIndexPopupBase+70},qa({contentRadius:r.borderRadius,limitVerticalRadius:!0})),fl(ba(r,{borderRadiusOuter:Math.min(r.borderRadiusOuter,4)}))),Ha=function(r){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return ti("Tooltip",a=>{const{borderRadius:n,colorTextLightSolid:i,colorBgSpotlight:o}=a,s=ba(a,{tooltipMaxWidth:250,tooltipColor:i,tooltipBorderRadius:n,tooltipBg:o});return[yl(s),wo(a,"zoom-big-fast")]},bl,{resetStyle:!1,injectStyle:t})(r)},wl=kr.map(r=>`${r}-inverse`);function Cl(r){return(arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0)?[].concat(j(wl),j(kr)).includes(r):kr.includes(r)}function Ua(r,t){const e=Cl(t),a=Oe({[`${r}-${t}`]:t&&e}),n={},i={};return t&&!e&&(n.background=t,i["--antd-arrow-background-color"]=t),{className:a,overlayStyle:n,arrowStyle:i}}const Fl=r=>{const{prefixCls:t,className:e,placement:a="top",title:n,color:i,overlayInnerStyle:o}=r,{getPrefixCls:s}=f.useContext(ga),l=s("tooltip",t),[c,u,v]=Ha(l),h=Ua(l,i),y=h.arrowStyle,m=Object.assign(Object.assign({},o),h.overlayStyle),p=Oe(u,v,l,`${l}-pure`,`${l}-placement-${a}`,e,h.className);return c(f.createElement("div",{className:p,style:y},f.createElement("div",{className:`${l}-arrow`}),f.createElement(ja,Object.assign({},r,{className:u,prefixCls:l,overlayInnerStyle:m}),n)))};var El=function(r,t){var e={};for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&t.indexOf(a)<0&&(e[a]=r[a]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,a=Object.getOwnPropertySymbols(r);n<a.length;n++)t.indexOf(a[n])<0&&Object.prototype.propertyIsEnumerable.call(r,a[n])&&(e[a[n]]=r[a[n]]);return e};const Pl=f.forwardRef((r,t)=>{var e,a;const{prefixCls:n,openClassName:i,getTooltipContainer:o,color:s,overlayInnerStyle:l,children:c,afterOpenChange:u,afterVisibleChange:v,destroyTooltipOnHide:h,destroyOnHidden:y,arrow:m=!0,title:p,overlay:d,builtinPlacements:b,arrowPointAtCenter:g=!1,autoAdjustOverflow:w=!0,motion:P,getPopupContainer:E,placement:F="top",mouseEnterDelay:R=.1,mouseLeaveDelay:x=.1,overlayStyle:O,rootClassName:$,overlayClassName:V,styles:L,classNames:_}=r,k=El(r,["prefixCls","openClassName","getTooltipContainer","color","overlayInnerStyle","children","afterOpenChange","afterVisibleChange","destroyTooltipOnHide","destroyOnHidden","arrow","title","overlay","builtinPlacements","arrowPointAtCenter","autoAdjustOverflow","motion","getPopupContainer","placement","mouseEnterDelay","mouseLeaveDelay","overlayStyle","rootClassName","overlayClassName","styles","classNames"]),Z=!!m,[,z]=ya(),{getPopupContainer:U,getPrefixCls:ee,direction:te,className:W,style:Re,classNames:ue,styles:Q}=ni("tooltip"),T=ai(),B=f.useRef(null),be=()=>{var re;(re=B.current)===null||re===void 0||re.forceAlign()};f.useImperativeHandle(t,()=>{var re,ne;return{forceAlign:be,forcePopupAlign:()=>{T.deprecated(!1,"forcePopupAlign","forceAlign"),be()},nativeElement:(re=B.current)===null||re===void 0?void 0:re.nativeElement,popupElement:(ne=B.current)===null||ne===void 0?void 0:ne.popupElement}});const[we,Ce]=Sa(!1,{value:(e=r.open)!==null&&e!==void 0?e:r.visible,defaultValue:(a=r.defaultOpen)!==null&&a!==void 0?a:r.defaultVisible}),ve=!p&&!d&&p!==0,We=re=>{var ne,oe;Ce(ve?!1:re),ve||((ne=r.onOpenChange)===null||ne===void 0||ne.call(r,re),(oe=r.onVisibleChange)===null||oe===void 0||oe.call(r,re))},qe=f.useMemo(()=>{var re,ne;let oe=g;return typeof m=="object"&&(oe=(ne=(re=m.pointAtCenter)!==null&&re!==void 0?re:m.arrowPointAtCenter)!==null&&ne!==void 0?ne:g),b||pl({arrowPointAtCenter:oe,autoAdjustOverflow:w,arrowWidth:Z?z.sizePopupArrow:0,borderRadius:z.borderRadius,offset:z.marginXXS,visibleFirst:!0})},[g,m,b,z]),fe=f.useMemo(()=>p===0?p:d||p||"",[d,p]),Se=f.createElement(Ps,{space:!0},typeof fe=="function"?fe():fe),de=ee("tooltip",n),me=ee(),He=r["data-popover-inject"];let Me=we;!("open"in r)&&!("visible"in r)&&ve&&(Me=!1);const ie=f.isValidElement(c)&&!ii(c)?c:f.createElement("span",null,c),Fe=ie.props,Ue=!Fe.className||typeof Fe.className=="string"?Oe(Fe.className,i||`${de}-open`):Fe.className,[he,pt,G]=Ha(de,!He),Je=Ua(de,s),ke=Je.arrowStyle,Y=Oe(V,{[`${de}-rtl`]:te==="rtl"},Je.className,$,pt,G,W,ue.root,_==null?void 0:_.root),Ie=Oe(ue.body,_==null?void 0:_.body),[Te,Ae]=oi("Tooltip",k.zIndex),it=f.createElement(cl,Object.assign({},k,{zIndex:Te,showArrow:Z,placement:F,mouseEnterDelay:R,mouseLeaveDelay:x,prefixCls:de,classNames:{root:Y,body:Ie},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},ke),Q.root),Re),O),L==null?void 0:L.root),body:Object.assign(Object.assign(Object.assign(Object.assign({},Q.body),l),L==null?void 0:L.body),Je.overlayStyle)},getTooltipContainer:E||o||U,ref:B,builtinPlacements:qe,overlay:Se,visible:Me,onVisibleChange:We,afterVisibleChange:u??v,arrowContent:f.createElement("span",{className:`${de}-arrow-content`}),motion:{motionName:ji(me,"zoom-big-fast",r.transitionName),motionDeadline:1e3},destroyTooltipOnHide:y??!!h}),Me?si(ie,{className:Ue}):ie);return he(f.createElement(li.Provider,{value:Ae},it))}),Ol=Pl;Ol._InternalPanelDoNotUseOrYouWillBeFired=Fl;export{Ta as A,kl as B,Ps as C,_l as D,fr as E,zr as F,qt as G,hs as H,Cs as I,Di as J,Cl as K,Ar as L,el as M,Nl as N,tl as O,xa as P,vl as Q,ur as R,qa as S,Ol as T,pl as U,Tl as V,ka as W,ja as X,Dl as a,ji as b,wo as c,Ma as d,ol as e,_n as f,xl as g,Js as h,$l as i,Rs as j,Vl as k,Ml as l,eo as m,to as n,Yi as o,Ll as p,na as q,Al as r,ro as s,fl as t,Sa as u,dl as v,Os as w,rl as x,Il as y,lo as z};
