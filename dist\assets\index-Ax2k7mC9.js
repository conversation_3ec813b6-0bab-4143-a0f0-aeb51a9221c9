import{j as e,r as x,L as t}from"./index-3mE9H3a0.js";import{I as p}from"./index-C-I6oq0X.js";import{B as a,R as g}from"./index-Cd-Wothc.js";import{I as u}from"./iconbutton-DFqB2BBL.js";import{P as r}from"./propertycard-DcHEKJZy.js";import"./react-stripe.esm-CaXK0k-R.js";import{I as j,C as v}from"./interactionbox-B9SrINfg.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";import"./index-BUUUOhAK.js";import"./DeleteOutlined-DCDN0YWx.js";const b=({user:s,time:i,question:n,body:c,likes:o,comments:m,shares:f,reactions:d,onClick:h})=>e.jsxs("div",{className:"post-card p-3 rounded border bg-white",onClick:h,children:[e.jsxs("div",{className:"d-flex justify-content-between align-items-start mb-2",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("img",{src:s.avatar,alt:"avatar",className:"rounded-circle",width:40,height:40}),e.jsxs("div",{className:"ms-2",children:[e.jsx("h6",{className:"mb-0",children:s.name}),e.jsx("small",{className:"text-muted",children:i})]})]}),e.jsx("div",{children:"⋮"})]}),e.jsx("strong",{children:n}),e.jsx("p",{className:"text-muted mt-2 mb-3",children:c}),e.jsx(j,{reactions:d,comments:m,likes:o}),e.jsx(v,{avatar:s.avatar,type:"text",className:"form-control w-100 ",placeholder:"Add your comments"})]}),l=[{title:"Modern 2-Bedroom |",location:"Downtown, Dubai",price:"$3,500,000",detail:"Stunning view with spacious layout. ",bath_tub:2,bed_room:2,square_feet:"220 Sq. Yd"},{title:"Luxury Villa |",location:"Palm Jumeirah",price:"$8,900,000",detail:"Exclusive beachfront property. ",bath_tub:4,bed_room:5,square_feet:"500 Sq. Yd"},{title:"Modern 2-Bedroom |",location:"Downtown, Dubai",price:"$3,500,000",detail:"Stunning view with spacious layout. ",bath_tub:2,bed_room:2,square_feet:"220 Sq. Yd"},{title:"Luxury Villa |",location:"Palm Jumeirah",price:"$8,900,000",detail:"Exclusive beachfront property. ",bath_tub:4,bed_room:5,square_feet:"500 Sq. Yd"}],N=[{id:1,user:{name:"Anonymous 1",avatar:"/assets/img/avatar-1.png"},time:"12 hours ago",question:"Do buildings have windows?",body:"I'm like really really confused. like do buildings have windows been trying to figure out for sooo long...",likes:256,comments:10,shares:10,reactions:["/assets/img/avatar-1.png","/assets/img/avatar-1.png","/assets/img/avatar-1.png","/assets/img/avatar-1.png","/assets/img/avatar-1.png"]},{id:2,user:{name:"User 2",avatar:"/assets/img/avatar-1.png"},time:"1 day ago",question:"Why is the sky blue?",body:"I've always wondered this. Anyone knows the science behind it?",likes:120,comments:5,shares:3,reactions:["/assets/img/avatar1-.png","/assets/img/avatar-1.png","/assets/img/avatar-1.png"]},{id:3,user:{name:"User 2",avatar:"/assets/img/avatar-1.png"},time:"1 day ago",question:"Why is the sky blue?",body:"I've always wondered this. Anyone knows the science behind it?",likes:120,comments:5,shares:3,reactions:["/assets/img/avatar1-.png","/assets/img/avatar-1.png","/assets/img/avatar-1.png"]},{id:4,user:{name:"User 2",avatar:"/assets/img/avatar-1.png"},time:"1 day ago",question:"Why is the sky blue?",body:"I've always wondered this. Anyone knows the science behind it?",likes:120,comments:5,shares:3,reactions:["/assets/img/avatar1-.png","/assets/img/avatar-1.png","/assets/img/avatar-1.png"]}],y=()=>e.jsx(p,{children:e.jsxs("div",{className:"container-fluid mt-4",children:[e.jsx("div",{className:"row justify-content-center",children:e.jsx("div",{className:"col-12 col-md-10 col-lg-10",children:e.jsxs("div",{className:"inputs-area",children:[e.jsx("div",{className:"row justify-content-center",children:e.jsx("div",{className:"col-12 col-md-8 col-lg-8 col-xl-9",children:e.jsxs("div",{className:"sreach",children:[e.jsx(a,{name:"name",placeholder:"e.g John Doe",label:"",className:"search-input",required:!0,suffix:e.jsx(u,{title:"Find Property",icon:e.jsx(g,{})})}),e.jsx("div",{})]})})}),e.jsx("div",{className:"bg-blue",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col",children:e.jsx(a,{name:"name",placeholder:"Location",label:"",className:"search-input",required:!0,type:"select",prefix:e.jsx("img",{src:"/assets/img/location-icon.png"})})}),e.jsx("div",{className:"col",children:e.jsx(a,{name:"name",placeholder:"Property Type",label:"",className:"search-input",required:!0,type:"select",prefix:e.jsx("img",{src:"/assets/img/property-icon.png"})})}),e.jsx("div",{className:"col",children:e.jsx(a,{name:"name",placeholder:"Pricing Range",label:"",className:"search-input",required:!0,type:"select",prefix:e.jsx("img",{src:"/assets/img/price-icon.png"})})}),e.jsx("div",{className:"col",children:e.jsx(a,{name:"name",placeholder:"Property Size",label:"",className:"search-input",required:!0,type:"select",prefix:e.jsx("img",{src:"/assets/img/box-icon.png"})})}),e.jsx("div",{className:"col",children:e.jsx(a,{name:"name",placeholder:"Build Year",label:"",className:"search-input",required:!0,type:"select",prefix:e.jsx("img",{src:"/assets/img/calender-icon.png"})})})]})})]})})}),e.jsx("div",{className:"row mt-5",children:e.jsx("div",{className:"col-12 w-100",children:e.jsx("img",{src:"/assets/img/banner-img.png",alt:"",className:"img-fluid w-100"})})}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"d-flex align-items-center justify-content-between",children:[e.jsx("div",{children:e.jsx("h4",{className:"mt-4 mb-3",children:"Recent Posts"})}),e.jsx("div",{children:e.jsx(t,{to:"/sphare-it",className:"font-18",children:"See More"})})]})}),l.map((s,i)=>e.jsx("div",{className:"col-12 col-sm-6 col-md-4 col-lg-3",children:e.jsx(r,{...s})},i))]}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"d-flex align-items-center justify-content-between",children:[e.jsx("div",{children:e.jsx("h4",{className:"mt-4 mb-3",children:"Recent Properties"})}),e.jsx("div",{children:e.jsx(t,{to:"/listing",className:"font-18",children:"See More"})})]})}),l.map((s,i)=>e.jsx("div",{className:"col-12 col-sm-6 col-md-4 col-lg-3",children:e.jsx(r,{...s})},i))]}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12",children:e.jsx("h4",{className:"mt-4 mb-3",children:"Recent Questions"})}),N.map(s=>e.jsx("div",{className:"col-12 col-md-6 col-lg-4 col-xl-3 mb-4",children:e.jsx(b,{...s})},s.id))]}),e.jsx("div",{className:"row mt-4 mb-5",children:e.jsx("div",{className:"col-12 w-100",children:e.jsx("img",{src:"/assets/img/home-img.png",alt:"",className:"img-fluid w-100"})})})]})}),A=x.memo(y);export{A as default};
