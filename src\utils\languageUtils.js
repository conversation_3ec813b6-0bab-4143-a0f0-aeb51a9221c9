/**
 * Utility functions for handling language data transformations
 * Languages are handled using IDs for API communication and names for display
 */

/**
 * Parse languages from various formats to array of IDs for form
 * @param {string|array|object} languages - Languages data in various formats
 * @param {array} availableLanguages - Available languages from startup data for ID mapping
 * @returns {array} - Array of language IDs
 */
export const parseLanguagesForForm = (languages, availableLanguages = []) => {
  if (!languages) return [];

  // If it's already an array of IDs, return as is
  if (
    Array.isArray(languages) &&
    languages.length > 0 &&
    typeof languages[0] === "number"
  ) {
    return languages;
  }

  // If it's an array of objects with id field
  if (Array.isArray(languages) && languages.length > 0 && languages[0].id) {
    return languages.map((lang) => lang.id);
  }

  // If it's an array of names or mixed data, convert to IDs
  if (Array.isArray(languages)) {
    return languages.map((lang) => {
      if (typeof lang === "object" && lang.name) {
        // Find ID by name
        const foundLang = availableLanguages.find(
          (al) => al.name === lang.name
        );
        return foundLang ? foundLang.id : lang.name;
      }
      if (typeof lang === "object" && lang.id) {
        return lang.id;
      }
      // If it's a name string, find the ID
      const foundLang = availableLanguages.find((al) => al.name === lang);
      return foundLang ? foundLang.id : lang;
    });
  }

  // If it's a string (JSON or comma-separated)
  if (typeof languages === "string") {
    try {
      const parsed = JSON.parse(languages);
      return parseLanguagesForForm(parsed, availableLanguages); // Recursive call for parsed data
    } catch (e) {
      // If not JSON, treat as comma-separated string
      return languages.split(",").map((langName) => {
        const foundLang = availableLanguages.find(
          (al) => al.name === langName.trim()
        );
        return foundLang ? foundLang.id : langName.trim();
      });
    }
  }

  // If it's a single object with id
  if (typeof languages === "object" && languages.id) {
    return [languages.id];
  }

  // If it's a single object with name
  if (typeof languages === "object" && languages.name) {
    const foundLang = availableLanguages.find(
      (al) => al.name === languages.name
    );
    return foundLang ? [foundLang.id] : [languages.name];
  }

  return [];
};

/**
 * Transform languages for API submission
 * @param {array} languageIds - Array of language IDs
 * @returns {object} - Object with indexed language keys for API
 */
export const transformLanguagesForAPI = (languageIds) => {
  if (!Array.isArray(languageIds)) return {};

  const transformedData = {};
  languageIds.forEach((langId, index) => {
    // Use the ID directly for API submission
    transformedData[`languages[${index}]`] = langId;
  });

  return transformedData;
};

/**
 * Get language options for select dropdown from startup data
 * @param {array} languagesData - Languages data from startup API
 * @returns {array} - Array of options for select component
 */
export const getLanguageOptions = (languagesData) => {
  if (!languagesData || !Array.isArray(languagesData)) return [];

  return languagesData.map((lang) => ({
    value: lang.id, // Always use ID as value
    label: lang.name, // Display name as label
  }));
};

/**
 * Convert language IDs to display format using available languages
 * @param {array} languageIds - Array of language IDs
 * @param {array} availableLanguages - Available languages from startup data
 * @returns {string} - Comma-separated string of language names
 */
export const formatLanguagesForDisplay = (
  languageIds,
  availableLanguages = []
) => {
  if (!Array.isArray(languageIds)) return "";

  return languageIds
    .map((langId) => {
      // Find the language name by ID
      const foundLang = availableLanguages.find((al) => al.id === langId);
      return foundLang ? foundLang.name : langId;
    })
    .join(", ");
};

/**
 * Get language names from IDs
 * @param {array} languageIds - Array of language IDs
 * @param {array} availableLanguages - Available languages from startup data
 * @returns {array} - Array of language names
 */
export const getLanguageNames = (languageIds, availableLanguages = []) => {
  if (!Array.isArray(languageIds)) return [];

  return languageIds.map((langId) => {
    const foundLang = availableLanguages.find((al) => al.id === langId);
    return foundLang ? foundLang.name : langId;
  });
};

/**
 * Get language objects from IDs
 * @param {array} languageIds - Array of language IDs
 * @param {array} availableLanguages - Available languages from startup data
 * @returns {array} - Array of language objects with id and name
 */
export const getLanguageObjects = (languageIds, availableLanguages = []) => {
  if (!Array.isArray(languageIds)) return [];

  return languageIds.map((langId) => {
    const foundLang = availableLanguages.find((al) => al.id === langId);
    return foundLang
      ? { id: foundLang.id, name: foundLang.name }
      : { id: langId, name: langId };
  });
};

export default {
  parseLanguagesForForm,
  transformLanguagesForAPI,
  getLanguageOptions,
  formatLanguagesForDisplay,
  getLanguageNames,
  getLanguageObjects,
};
