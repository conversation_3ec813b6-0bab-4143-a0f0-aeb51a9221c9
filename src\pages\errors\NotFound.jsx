import React from 'react';
import { Result, Button } from 'antd';
import { useNavigate } from 'react-router-dom';

/**
 * 404 Not Found page component using Ant Design
 */
const NotFound = () => {
  const navigate = useNavigate();

  const handleBackHome = () => {
    // Check if user exists to determine where to redirect
    if (window.user && Object.keys(window.user).length > 0) {
      navigate('/home');
    } else {
      navigate('/login');
    }
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
      }}
    >
      <Result
        status="404"
        title="404"
        subTitle="Sorry, the page you visited does not exist."
        extra={
          <div style={{ display: 'flex', gap: '12px', justifyContent: 'center' }}>
            <Button type="primary" onClick={handleBackHome}>
              Back Home
            </Button>
            <Button onClick={handleGoBack}>
              Go Back
            </Button>
          </div>
        }
      />
    </div>
  );
};

export default NotFound;