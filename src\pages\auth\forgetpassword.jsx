import React, { memo } from "react";
import AuthLayout from "@/components/shared/layout/authlayout";
import BaseInput from "@/components/shared/inputs/index";
import FlatButton from "@/components/shared/button/flatbutton";
import { Form } from "antd";
import { useNavigate } from "react-router-dom";
import { combineRules, validations } from "@/config/rules";
import { useMutation } from "@/hooks/reactQuery";

const ForgetPassword = () => {
  const navigate = useNavigate();
  const { mutate, isPending } = useMutation("forgotPassword", {
    useFormData: false,
    onSuccess: (data) => {
      if (data) navigate("/login");
    },
  });
  const onFinish = (values) => {
    mutate(values);
  };

  return (
    <div className="forgot-area">
      <div className="text-center sign-up-logo">
        <img src="../assets/img/logo.png" alt="Auth Logo" />
      </div>
      <AuthLayout
        showSidebar={true}
        src="/assets/img/forgot-img.png"
        pageType="forgot"
      >
        <div className="row">
          <div className="col-12">
            <h1 className="font-36 color-black mb-3">Forgot Your Password?</h1>
            <p>Enter the email address associated with your account.</p>
          </div>
        </div>
        <Form
          name="ForgetPassword"
          layout="vertical"
          onFinish={onFinish}
          initialValues={{
            remember: true,
          }}
          autoComplete="off"
        >
          <div className="row">
            <div className="col-12 col-md-12 col-lg-10">
              <BaseInput
                name="email"
                placeholder="Email Address"
                label="Email"
                rules={combineRules(
                  "email",
                  validations.required,
                  validations.email
                )}
              />
            </div>
            <div className="col-12 col-md-12 col-lg-10">
              <FlatButton
                title={isPending ? "Submiting..." : "Submit"}
                className="mx-auto mt-4 signin-btn signup-btn w-100 mt-5"
                htmlType="submit"
                loading={isPending}
                disabled={isPending}
              />
              <p className="signup-text">
                We will email you a link to reset your password
              </p>
            </div>
          </div>

          <div></div>
        </Form>
      </AuthLayout>
    </div>
  );
};

export default memo(ForgetPassword);
