import React, { useState } from "react";

import CommentBox from "@/components/shared/box/commentbox";
import InteractionBox from "@/components/shared/box/interactionbox";
import ImageGallery from "./imagegallery";
import UserAvatar from "@/components/shared/avatar/useravatar";
import MyComment from "@/components/shared/box/mycomment";
import CustomDropdown from "@/components/shared/dropdown";
import CustomModal from "@/components/shared/modal";
import BaseInput from "@/components/shared/inputs";
import FlatButton from "@/components/shared/button/flatbutton";

const SparePost = ({
  user,
  body,
  likes,
  repost,
  comments,
  reactions,
  images,
  user_comment,
  type = "shareit"
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  return (
    <>
      <div className="post-card p-3 rounded border bg-white mb-5">
        {/* Header */}
        <div className="d-flex justify-content-between align-items-start mb-2">
          <div className="d-flex align-items-center">
            <UserAvatar
              avatar={user.avatar}
              name={user.name}
              time={user.time}
            />
          </div>
          <div>
            <CustomDropdown
              title="⋮"
              overlayClassName="my-drop"
              icon={false}
              items={[
                { key: "1", label: <p>Copy Link</p> },
                { key: "2", label: <p onClick={showModal}>Report Post</p> },
              ]}
            />
          </div>
        </div>

        <p className="text-muted mt-2 mb-3">{body}</p>

        {/* ImageGallery */}
      {type === "shareit" && <ImageGallery images={images} />}
        {/* Interaction Stats */}
        <InteractionBox
          reactions={reactions}
          comments={comments}
          repost={repost}
          likes={likes}
          isSpareCard={true}
        />

        {/* Reactions Avatars */}
        {user_comment?.map((comment, idx) => (
          <MyComment
            key={idx}
            isReply={comment.isReply}
            avatar={
              <UserAvatar
                avatar={comment.avatar}
                name={comment.name}
                time={comment.time}
              />
            }
            mycomment={comment.mycomment}
            commentlink={comment.commentlink}
            likes={comment.likes}
            shares={comment.shares}
          />
        ))}
        {/* Comment Box */}
        <CommentBox
          avatar={user.avatar}
          type="text"
          className="form-control w-100 "
          placeholder="Add your comments"
        />
      </div>
      <CustomModal
        title="Report"
        closable={{ "aria-label": "Custom Close Button" }}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        className="report-modal"
        footer={false}
        width="700px"
      >
        <p>Please enter below any additional details you would want to share with us</p>
        <BaseInput placeholder="Submit a reason for reporting this post" rows={6} type="textarea" />
        <FlatButton title="Submit" className="modal-submit" onClick={handleOk} />
      </CustomModal>
    </>
  );
};

export default SparePost;
