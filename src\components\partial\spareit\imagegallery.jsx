import React from "react";

const ImageGallery = ({ images }) => (
  <div className="">
    <div className="row mb-4">
      {images.map((img, idx) => {
        if (idx === 0) {
          return (
            <div key={idx} className="col-4">
              <img
                src={img}
                alt={`gallery-${idx}`}
                className="rounded-md object-cover w-100"
              />
            </div>
          );
        }
        if (idx === 1) {
          return (
            <div key={idx} className="col-8">
              <img
                src={img}
                alt={`gallery-${idx}`}
                className="rounded-md object-cover w-100"
              />
            </div>
          );
        }
        // Second row (images 2 to 6) evenly distributed
        return (
          <div key={idx} className="col">
            <div className="gallery-img">
              <img
                src={img}
                alt={`gallery-${idx}`}
                className="rounded-md object-cover w-100"
              />
            </div>
          </div>
        );
      })}
    </div>
  </div>
);

export default ImageGallery;
