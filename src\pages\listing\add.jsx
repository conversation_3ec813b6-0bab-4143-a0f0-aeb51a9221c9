import React, { useState, useEffect } from "react";
import InnerLayout from "@/components/shared/layout/innerlayout";
import BaseInput from "@/components/shared/inputs";
import { Form, Radio, Switch, Select, DatePicker } from "antd";
import DraggerUpload from "@/components/shared/inputs/draggerupload";
import FlatButton from "@/components/shared/button/flatbutton";
import { useNavigate, useParams } from "react-router-dom";
import useStartupData from "@/hooks/reactQuery/useStartupData";
import useLocationData from "@/hooks/useLocationData";
import { useMutation, useQuery } from "@/hooks/reactQuery";
import { combineRules, validations } from "@/config/rules";
import PhoneInputField from "@/components/shared/inputs/phonenumber";
import useSweetAlert from "@/hooks/useSweetAlert";
import { useQueryClient } from "@tanstack/react-query";
import dayjs from "dayjs";

const AddListing = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id } = useParams();
  const { showAlert } = useSweetAlert();
  const queryClient = useQueryClient();

  // State management
  const [basementValue, setBasementValue] = useState(false);
  const [hoaValue, setHoaValue] = useState(false);
  const [uploadedImages, setUploadedImages] = useState([]);
  const [removedImageIds, setRemovedImageIds] = useState([]);
  const [originalImages, setOriginalImages] = useState([]);

  // Get startup data for dropdowns
  const { data: startupResponse, loading } = useStartupData();
  const startupData = startupResponse?.data;

  const { data: propertyResponse, isLoading: propertyLoading } = useQuery(
    "properties",
    {
      slug: id, // Pass user ID as URL slug
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
      enabled: !!id, // Only fetch if user ID is available
    }
  );

  const propertyData = propertyResponse?.data;

  // Location data hook for state/city logic
  const {
    selectedState,
    stateOptions,
    cityOptions,
    statesLoading,
    citiesLoading,
    handleStateChange,
    updateSelectedState,
  } = useLocationData();

  // Add Property mutation
  const { mutate: addProperty, isPending: isAdding } = useMutation(
    "addProperty",
    {
      useFormData: false,

      onSuccess: async (data) => {
        // Invalidate all properties-related queries with more aggressive approach
        await queryClient.invalidateQueries({
          queryKey: ["properties"],
          exact: false,
        });
        await queryClient.invalidateQueries({
          queryKey: ["getProperty"],
          exact: false,
        });

        // Invalidate getUser query to update user's property count/data
        await queryClient.invalidateQueries({
          queryKey: ["getUser"],
          exact: false,
        });

        // Also remove all getProperty queries from cache
        queryClient.removeQueries({
          queryKey: ["getProperty"],
          exact: false,
        });

        // If we have the new property ID, invalidate its specific query
        const newPropertyId = data?.data?.id || data?.id;
        if (newPropertyId) {
          console.log(
            "Invalidating specific property query for ID:",
            newPropertyId
          );
          // Invalidate both string and number versions of the ID
          await queryClient.invalidateQueries({
            queryKey: ["getProperty", newPropertyId.toString()],
          });
          await queryClient.invalidateQueries({
            queryKey: ["getProperty", newPropertyId],
          });
        }

        // Force refetch of properties queries
        await queryClient.refetchQueries({ queryKey: ["properties"] });

        // Trigger storage event to notify detail pages
        localStorage.setItem(`property_updated_${id}`, Date.now().toString());
        localStorage.removeItem(`property_updated_${id}`); // Remove immediately to trigger event

        if (data) {
          navigate("/listing");
        }
      },
    }
  );

  // Update Property mutation
  const { mutate: updateProperty, isPending: isUpdating } = useMutation(
    "updateProperty",
    {
      useFormData: false,
      onSuccess: async (data) => {
        // Invalidate all properties-related queries with more aggressive approach
        await queryClient.invalidateQueries({
          queryKey: ["properties"],
          exact: false,
        });
        await queryClient.invalidateQueries({
          queryKey: ["getProperty"],
          exact: false,
        });

        // Invalidate getUser query to update user's property count/data
        await queryClient.invalidateQueries({
          queryKey: ["getUser"],
          exact: false,
        });

        // Also remove all getProperty queries from cache
        queryClient.removeQueries({
          queryKey: ["getProperty"],
          exact: false,
        });

        // Invalidate the specific property query that was just updated
        if (id) {
          console.log("Invalidating specific property query for ID:", id);

          // Remove the query from cache completely to force fresh fetch
          queryClient.removeQueries({
            queryKey: ["getProperty", id.toString()],
          });
          queryClient.removeQueries({
            queryKey: ["getProperty", id],
          });

          // Invalidate both string and number versions of the ID
          await queryClient.invalidateQueries({
            queryKey: ["getProperty", id.toString()],
            exact: false, // This will invalidate all queries that start with this key
          });
          await queryClient.invalidateQueries({
            queryKey: ["getProperty", id],
            exact: false,
          });

          // Force refetch if the query is currently active (someone is viewing the property)
          await queryClient.refetchQueries({
            queryKey: ["getProperty", id.toString()],
            type: "active",
          });
          await queryClient.refetchQueries({
            queryKey: ["getProperty", id],
            type: "active",
          });
        }

        // Force refetch of properties queries
        await queryClient.refetchQueries({ queryKey: ["properties"] });

        if (data) {
          navigate("/listing");
        }
      },
    }
  );

  const isSaving = isAdding || isUpdating;

  const onStateChange = (value) => {
    handleStateChange(value, form);
  };

  // Static options
  const compensationTypeOptions = [
    { value: "$", label: "$" },
    { value: "%", label: "%" },
  ];

  const sizeTypeOptions = [
    { value: "sqft", label: "Sq.Ft" },
    { value: "sq.m", label: "Sq.M" },
    { value: "sq.yd", label: "Sq.Yd" },
    { value: "acres", label: "Acres" },
  ];

  // Generate options for bed and bath (1-10)
  const bedBathOptions = Array.from({ length: 10 }, (_, i) => ({
    value: i + 1,
    label: i + 1,
  }));

  // Generate options for garage (1-10) with formatted labels
  const garageOptions = Array.from({ length: 10 }, (_, i) => ({
    value: i + 1,
    label: `${i + 1} Car${i + 1 > 1 ? "s" : ""}`,
  }));

  // Form submission handler
  const onFinish = (values) => {
    const formData = {
      ...values,
      // Ensure HOA and Basement are proper boolean values
      basement: values.basement === true || values.basement === "true",
      hoa: values.hoa === true || values.hoa === "true",
    };

    // Remove any 'images' field that might be captured by form
    delete formData.images;

    // Convert year_built to number if it exists
    if (formData.year_built) {
      formData.year_built = parseInt(formData.year_built.format("YYYY"), 10);
    }

    // Add images as array: images: [1, 2, 3]
    if (uploadedImages && uploadedImages.length > 0) {
      const imageIds = uploadedImages
        .map((image) => {
          // Extract only the ID from the image object
          return image.id || image.uploadedId;
        })
        .filter(Boolean); // Remove any undefined/null values

      if (imageIds.length > 0) {
        formData.images = imageIds;
      }
    }

    // Add removed image IDs for update operations as JSON array
    if (id && removedImageIds.length > 0) {
      formData.remove_image_ids = removedImageIds;
    }

    // Remove hoa_price and hoa_type if hoa is false
    if (!formData.hoa) {
      delete formData.hoa_price;
      delete formData.hoa_type;
    }

    console.log("Form data:", formData);

    if (id) {
      updateProperty({ slug: id, data: formData });
    } else {
      addProperty(formData);
    }
  };

  // Handle image upload - store images in array format without API call
  const handleImageUpload = (images, removedImage = null) => {
    // Separate new File objects from existing image data
    const newFiles = images.filter((img) => img instanceof File);
    const existingImages = images.filter((img) => !(img instanceof File));

    // Combine both types
    setUploadedImages([...existingImages, ...newFiles]);

    // Track removed image IDs for edit mode
    if (removedImage && removedImage.id && id) {
      setRemovedImageIds((prev) => [...prev, removedImage.id]);
    }
  };

  // Effect to populate form when editing
  useEffect(() => {
    if (propertyData && id) {
      // Set form values
      const formValues = {
        ...propertyData,
        year_built: propertyData.year_built
          ? dayjs().year(propertyData.year_built)
          : null,
      };

      // Set state values
      setBasementValue(propertyData.basement || false);
      setHoaValue(propertyData.hoa || false);
      setUploadedImages(propertyData.images || []);
      setOriginalImages(propertyData.images || []);
      setRemovedImageIds([]); // Reset removed images when loading property data

      // Handle state selection for location (without clearing city)
      if (propertyData.state) {
        updateSelectedState(propertyData.state);
      }

      // Set form values after state is updated
      setTimeout(() => {
        form.setFieldsValue(formValues);
      }, 100);
    }
  }, [propertyData, id, form, updateSelectedState]);
  return (
    <InnerLayout>
      <div className="container-fluid">
        <div className="row mt-5">
          <div className="col-12">
            <h2>{id ? "Edit" : "Add"} Listing</h2>
          </div>
          <div className="col-12">
            <Form
              form={form}
              name="propertyForm"
              layout="vertical"
              onFinish={onFinish}
              initialValues={true}
              scrollToFirstError={true}
              autoComplete="off"
              className="add-listing"
            >
              <div className="row gx-5">
                <div className="col-12">
                  <p className="font-18 mt-4">Property Details</p>
                </div>
                <div className="col-12 col-sm-6 col-md-6 col-lg-4">
                  <BaseInput
                    name="type_id"
                    placeholder="Select Property Type"
                    label="Property Type"
                    type="select"
                    rules={combineRules("Property Type", validations.required)}
                    options={startupData?.propertyTypes?.map((type) => ({
                      value: type.id,
                      label: type.name,
                    }))}
                    loading={loading}
                  />
                </div>
                <div className="col-12 col-sm-6 col-md-6 col-lg-4">
                  <BaseInput
                    name="home_style_id"
                    placeholder="Select Home Style"
                    label="Home Style"
                    type="select"
                    rules={combineRules("Home Style", validations.required)}
                    options={startupData?.homeStyles?.map((type) => ({
                      value: type.id,
                      label: type.name,
                    }))}
                    loading={loading}
                  />
                </div>
                <div className="col-12 col-sm-6 col-md-6 col-lg-4">
                  <PhoneInputField
                    name="mobile_no"
                    placeholder="(XXX) XXX-XXXX"
                    label="Phone Number"
                    rules={combineRules(
                      "mobile-number",
                      validations.required,
                      validations.phone
                    )}
                  />
                </div>
                <div className="col-12 col-sm-6 col-md-6 col-lg-4">
                  <BaseInput
                    name="mls_id"
                    placeholder="Enter MLS ID"
                    label="MLS ID (optional)"
                  />
                </div>

                <div className="col-12 col-sm-6 col-md-6 col-lg-4">
                  <BaseInput
                    name="address"
                    placeholder="xyz street"
                    label="Address"
                    rules={combineRules("Address", validations.required)}
                  />
                </div>

                <div className="col-12 col-sm-6 col-md-6 col-lg-2">
                  <BaseInput
                    name="state"
                    placeholder="Select State"
                    options={stateOptions}
                    loading={statesLoading}
                    handlechange={onStateChange}
                    showSearch={true}
                    label="State"
                    type="select"
                    rules={combineRules("State", validations.required)}
                    filterOption={(input, option) =>
                      option?.label?.toLowerCase().includes(input.toLowerCase())
                    }
                  />
                </div>

                <div className="col-12 col-sm-6 col-md-6 col-lg-2">
                  <BaseInput
                    name="city"
                    placeholder="Select City"
                    options={cityOptions}
                    loading={citiesLoading}
                    disabled={!selectedState}
                    showSearch={true}
                    rules={combineRules("City", validations.required)}
                    label="City"
                    type="select"
                    filterOption={(input, option) =>
                      option?.label?.toLowerCase().includes(input.toLowerCase())
                    }
                  />
                </div>
                <div className="col-12 col-sm-6 col-md-6 col-lg-4">
                  <BaseInput
                    name="zip"
                    placeholder="Enter Zip Code"
                    label="Zip Code"
                    rules={combineRules("Zip Code", validations.required)}
                  />
                </div>
                <div className="col-12 col-sm-6 col-md-6 col-lg-4">
                  <BaseInput
                    name="price"
                    placeholder="Enter Price ($)"
                    label="Price"
                    type="number"
                    rules={combineRules(
                      "Price",
                      validations.required,
                      validations.greaterThanOne
                    )}
                  />
                </div>
                <div className="col-12 col-sm-6 col-md-6 col-lg-4">
                  <div className="row">
                    <div className="col-8">
                      <BaseInput
                        name="buy_side_compensation"
                        placeholder="Enter Compensation"
                        label="Buy-Side Compensation"
                        rules={combineRules(
                          "Buy Side Compensation",
                          validations.required,
                          validations.greaterThanOne
                        )}
                        type="number"
                      />
                    </div>
                    <div className="col-4">
                      <BaseInput
                        name="buy_side_compensation_type"
                        placeholder="Enter Compensation"
                        label="Type"
                        options={compensationTypeOptions}
                        rules={combineRules(
                          "Buy Side Compensation Type",
                          validations.required
                        )}
                        type="select"
                      />
                    </div>
                  </div>
                </div>

                <div className="col-12 col-sm-6 col-md-6 col-lg-4">
                  <div className="row">
                    <div className="col-8">
                      <BaseInput
                        name="size"
                        placeholder="Enter Property Size"
                        label="Property Size"
                        type="number"
                        rules={combineRules(
                          "Property Size",
                          validations.required,
                          validations.greaterThanOne
                        )}
                      />
                    </div>
                    <div className="col-4">
                      <BaseInput
                        placeholder="Unit"
                        options={sizeTypeOptions}
                        name="size_type"
                        label="Size Type"
                        rules={combineRules(
                          "Property Size Type",
                          validations.required
                        )}
                        type="select"
                      />
                    </div>
                  </div>
                </div>

                <div className="col-12 col-sm-6 col-md-6 col-lg-4">
                  <div className="row">
                    <div className="col-8">
                      <BaseInput
                        name="lot_size"
                        placeholder="Enter Lot Size"
                        label="Lot Size"
                        type="number"
                        rules={combineRules(
                          "Lot Size",
                          validations.required,
                          validations.greaterThanOne
                        )}
                      />
                    </div>
                    <div className="col-4">
                      <BaseInput
                        placeholder="Select Type"
                        label="Size Type"
                        options={sizeTypeOptions}
                        rules={combineRules(
                          "Lot Size Type",
                          validations.required
                        )}
                        name="lot_type"
                        type="select"
                      />
                    </div>
                  </div>
                </div>

                <div className="col-12 col-sm-6 col-md-6 col-lg-4">
                  <Form.Item
                    name="year_built"
                    label="Year Built"
                    rules={combineRules("Year Built", validations.required)}
                  >
                    <DatePicker
                      picker="year"
                      placeholder="Select Year"
                      style={{ width: "100%" }}
                    />
                  </Form.Item>
                </div>

                <div className="col-12 col-sm-6 col-md-6 col-lg-4">
                  <BaseInput
                    name="bed"
                    placeholder="No. of Bed"
                    label="Bed"
                    type="select"
                    options={bedBathOptions}
                    rules={combineRules("Bed", validations.required)}
                  />
                </div>

                <div className="col-12 col-sm-6 col-md-6 col-lg-4">
                  <BaseInput
                    name="bath"
                    placeholder="No. of Bath"
                    label="Bath"
                    type="select"
                    options={bedBathOptions}
                    rules={combineRules("Bath", validations.required)}
                  />
                </div>

                <div className="col-12 col-sm-6 col-md-6 col-lg-4">
                  <BaseInput
                    name="garage"
                    placeholder="No. of Cars"
                    label="Garage"
                    type="select"
                    options={garageOptions}
                    rules={combineRules("Garage", validations.required)}
                  />
                </div>

                <div className="col-12 col-sm-6 col-md-6 col-lg-3">
                  <BaseInput
                    type="radio"
                    name="hoa"
                    label="HOA"
                    options={[
                      { value: true, label: "Yes" },
                      { value: false, label: "No" },
                    ]}
                    rules={combineRules("HOA", validations.required)}
                    handlechange={(value) => {
                      setHoaValue(value);
                      // Clear HOA fields when "No" is selected
                      if (!value) {
                        form.setFieldsValue({
                          hoa_price: undefined,
                          hoa_type: undefined,
                        });
                      }
                    }}
                  />
                </div>
                <div className="col-12 col-sm-6 col-md-6 col-lg-3">
                  <BaseInput
                    type="radio"
                    name="basement"
                    label="Basement"
                    options={[
                      { value: true, label: "Yes" },
                      { value: false, label: "No" },
                    ]}
                    rules={combineRules("basement", validations.required)}
                  />
                </div>

                {/* HOA Price and Type fields - only show when HOA is Yes */}
                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, currentValues) =>
                    prevValues.hoa !== currentValues.hoa
                  }
                >
                  {({ getFieldValue }) => {
                    const hoaSelected = getFieldValue("hoa");
                    return hoaSelected === true ? (
                      <div className="col-12 col-sm-6 col-md-6">
                        <div className="row">
                          <div className="col-8">
                            <BaseInput
                              name="hoa_price"
                              placeholder="Enter Price ($)"
                              label="HOA Price"
                              type="number"
                              rules={combineRules(
                                "HOA Price",
                                validations.required,
                                validations.greaterThanOne
                              )}
                            />
                          </div>
                          <div className="col-4">
                            <BaseInput
                              placeholder="Select Type"
                              label="Type"
                              options={[
                                {
                                  value: "month",
                                  label: "Month",
                                },
                                {
                                  value: "year",
                                  label: "Year",
                                },
                              ]}
                              name="hoa_type"
                              type="select"
                              rules={combineRules(
                                "HOA Type",
                                validations.required
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    ) : null;
                  }}
                </Form.Item>
              </div>

              <div className="row">
                <div className="col-12">
                  <p className="font-18 mt-4">Property Description</p>
                </div>
                <div className="col-12">
                  <BaseInput
                    name="description"
                    placeholder="Enter detailed property description..."
                    label="Description"
                    type="textarea"
                    rows={5}
                    rules={combineRules("description", validations.required)}
                  />
                </div>
              </div>

              <div className="row">
                <div className="col-12">
                  <p className="font-18 mt-4">Property Images</p>
                </div>
                <div className="col-12 mt-4 mb-5">
                  <div className="form-item-label">
                    <label>Upload Images</label>
                  </div>
                  <DraggerUpload
                    onChange={handleImageUpload}
                    multiple={false}
                    maxCount={15}
                    value={uploadedImages}
                    type="property_image"
                  />
                </div>
              </div>

              <div className="row">
                <div className="col-12 mt-3 mb-3 text-end">
                  <FlatButton
                    title="Cancel"
                    className="gray-btn"
                    onClick={() => navigate(-1)}
                    disabled={isSaving}
                  />
                  <FlatButton
                    title={isSaving ? "Saving..." : id ? "Update" : "Save"}
                    className="blue-btn ms-3"
                    htmlType="submit"
                    disabled={isSaving}
                  />
                </div>
              </div>
            </Form>
          </div>
        </div>
      </div>
    </InnerLayout>
  );
};

export default AddListing;
