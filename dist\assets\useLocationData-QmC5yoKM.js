import{R as s}from"./index-3mE9H3a0.js";import{a as f}from"./useQuery-Bzo2W4ue.js";function h(){return f("startupData",{staleTime:1/0,gcTime:1/0,refetchOnMount:!1,refetchOnWindowFocus:!1,refetchOnReconnect:!1,retry:!1,select:a=>a,notifyOnChangeProps:["data","error","isLoading"],enabled:!0})}const M=(a="")=>{const[o,l]=s.useState(a),{data:r,isLoading:m}=h(),t=r==null?void 0:r.data,d=s.useMemo(()=>{if(!o||!(t!=null&&t.states))return"";const n=t.states.find(e=>e.name===o);return(n==null?void 0:n.isoCode)||""},[o,t==null?void 0:t.states]),{data:c,isLoading:C}=f("cities",{params:{stateCode:d},enabled:!!d,staleTime:5*60*1e3,gcTime:10*60*1e3,select:n=>Array.isArray(n==null?void 0:n.data)?n:[]}),i=c==null?void 0:c.data,y=s.useMemo(()=>t!=null&&t.states?t.states.filter(e=>e.countryCode==="US"&&!e.isoCode.includes("UM")).map(e=>({value:e.name,label:e.name,isoCode:e.isoCode})):[],[t==null?void 0:t.states]),S=s.useMemo(()=>!i||!Array.isArray(i)?[]:i.map(e=>{const u=e.name||e.city_name||e.label||e;return{value:u,label:u,id:e.id}}),[i]),g=s.useCallback((n,e=null)=>{l(n),e&&e.setFieldsValue({city:void 0})},[]),b=s.useCallback(n=>{l(n)},[]);return{selectedState:o,stateOptions:y,cityOptions:S,statesLoading:m,citiesLoading:C,handleStateChange:g,updateSelectedState:b}};export{M as a,h as u};
