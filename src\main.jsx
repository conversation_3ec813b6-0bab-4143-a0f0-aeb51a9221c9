// CRITICAL: Import Ant Design React 19 compatibility setup FIRST
import "@/config/antd-react19-setup";

import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { QueryClientProvider } from "@tanstack/react-query";
import "bootstrap/dist/css/bootstrap.min.css";
import "bootstrap/dist/js/bootstrap.bundle.min.js";
import "@/index.css";
import App from "@/route";
import queryClient from "@/services/queryClient";
import bootstrap from "@/bootstrap";

// Initialize bootstrap to set up window.constants
(async () => {
  await bootstrap();

  createRoot(document.getElementById("root")).render(
    <QueryClientProvider client={queryClient}>
      <App />
    </QueryClientProvider>
  );
})();
