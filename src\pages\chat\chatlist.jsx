import React from "react";
import ChatUserList from "@/components/shared/list/chatuserlist";


const ChatList = ({ users }) => {
  return (
    <>
      {users.map((user, index) => (
        <ChatUserList
          key={index}
          name={user.name}
          message={user.message}
          isTyping={user.isTyping}
          msgTime={user.msgTime}
          msgStatus={user.msgStatus}
          onlineStatus={user.onlineStatus}
          avatar={user.avatar}
        />
      ))}
    </>
  );
};

export default ChatList;
