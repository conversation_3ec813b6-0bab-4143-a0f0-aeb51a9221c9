import { loadStripe } from "@stripe/stripe-js";
import { notification } from "antd";
import constants from "@/config/constants";

const stripePromise = loadStripe(constants.stripe_publish_key);

export const updateUserSubscriptionStatus = (
  subscriptionData = {}
) => {
  if (window.user) {
    window.user.subscription_status = subscriptionData.status || "inactive";
    window.user.subscription_id = subscriptionData.id || null;
    window.user.subscription_current_period_end =
      subscriptionData.current_period_end || null;
    window.user.subscription_cancel_at_period_end =
      subscriptionData.cancel_at_period_end || false;

    if (window.helper && window.helper.setStorageData) {
      window.helper.setStorageData("session", window.user);
    }
  }
};

export const checkStripeSubscriptionStatus = async (
  token = null
) => {
  try {
    const response = await window.apiClient.request("getCurrentSubscription", {
      token: token,
    });

    if (response && response.data) {
      return response.data;
    }
    return null;
  } catch (err) {
    notification.error({
      message: "Error",
      description: "Failed to check subscription status",
      duration: 4,
    });
    return null;
  }
};

export const showSubscriptionStatusNotification = (subscription) => {
  if (!subscription) return;

  const isActiveSubscription = ["active", "trialing"].includes(
    subscription.status
  );

  updateUserSubscriptionStatus(subscription);

  if (isActiveSubscription) {
    notification.success({
      message: "Subscription Active",
      description: `Your subscription is ${subscription.status}`,
      duration: 4,
    });
  } else {
    notification.warning({
      message: "Subscription Status",
      description: `Your subscription is ${subscription.status}`,
      duration: 4,
    });
  }
};

export const checkAndShowSubscriptionStatus = async (
  token = null
) => {
  const subscription = await checkStripeSubscriptionStatus(token);
  if (subscription) {
    showSubscriptionStatusNotification(subscription);
  }
  return subscription;
};

export const updateUrlWithSubscriptionParams = (
  clientSecret,
  subscriptionId
) => {
  const url = new URL(window.location);
  url.searchParams.set("subscription_id", subscriptionId);
  url.searchParams.set("client_secret", clientSecret);
  window.history.replaceState({}, "", url);
};

export const getSubscriptionParamsFromUrl = () => {
  const urlParams = new URLSearchParams(window.location.search);
  return {
    subscriptionId: urlParams.get("subscription_id"),
    clientSecret: urlParams.get("client_secret"),
  };
};

export const clearSubscriptionParamsFromUrl = () => {
  const url = new URL(window.location);
  url.searchParams.delete("subscription_id");
  url.searchParams.delete("client_secret");
  window.history.replaceState({}, "", url);
};