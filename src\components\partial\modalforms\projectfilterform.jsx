import { Form } from 'antd'
import React from 'react'
import BaseInput from '../../shared/inputs'

const ProjectFilterForm = () => {
  return (
    <Form
      name="login"
      layout="vertical"
      onFinish={() => setIsFilterModalOpen(false)}
      initialValues={{
        remember: true,
      }}
      autoComplete="off"
    >
      <BaseInput
        name="date"
        placeholder=""
        label="Date"
        type="date"
        />
      <BaseInput
        name="due_date"
        placeholder=""
        label="Due Date"
        type="date"
      />
      <BaseInput
        name="priority"
        type="select"
        placeholder=""
        options={[{ value: 'Low' }, { value: 'Medium' }, { value: 'High' }]}
        label="Priority"
      />

    </Form>
  )
}

export default ProjectFilterForm
