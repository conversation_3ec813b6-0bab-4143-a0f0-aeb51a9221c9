const secretKey = "app.resarchHound753";
import _ from "lodash";
import Swal from "sweetalert2";
import { notification, message } from "antd";
import { dateHelper } from "./dateHelper";

const deriveKey = async (password, salt) => {
  const encoder = new TextEncoder();
  const keyMaterial = await crypto.subtle.importKey(
    "raw",
    encoder.encode(password),
    { name: "PBKDF2" },
    false,
    ["deriveKey"]
  );

  return crypto.subtle.deriveKey(
    {
      name: "PBKDF2",
      salt: salt,
      iterations: 100000,
      hash: "SHA-256",
    },
    keyMaterial,
    { name: "AES-GCM", length: 256 },
    false,
    ["encrypt", "decrypt"]
  );
};

const encryptData = async (data, password) => {
  const encoder = new TextEncoder();
  const salt = crypto.getRandomValues(new Uint8Array(16));
  const iv = crypto.getRandomValues(new Uint8Array(12));
  const key = await derive<PERSON>ey(password, salt);

  const encrypted = await crypto.subtle.encrypt(
    { name: "AES-GCM", iv: iv },
    key,
    encoder.encode(data)
  );

  const combined = new Uint8Array(
    salt.length + iv.length + encrypted.byteLength
  );
  combined.set(salt, 0);
  combined.set(iv, salt.length);
  combined.set(new Uint8Array(encrypted), salt.length + iv.length);

  return btoa(String.fromCharCode(...combined));
};

const decryptData = async (encryptedData, password) => {
  try {
    const combined = new Uint8Array(
      atob(encryptedData)
        .split("")
        .map((char) => char.charCodeAt(0))
    );

    const salt = combined.slice(0, 16);
    const iv = combined.slice(16, 28);
    const encrypted = combined.slice(28);

    const key = await deriveKey(password, salt);

    const decrypted = await crypto.subtle.decrypt(
      { name: "AES-GCM", iv: iv },
      key,
      encrypted
    );

    const decoder = new TextDecoder();
    return decoder.decode(decrypted);
  } catch (error) {
    return null;
  }
};

const Helper = {
  devLog: function (message, params = "") {
    if (
      process.env.NODE_ENV === "development" ||
      process.env.NODE_ENV === "dev"
    ) {
    }
    return;
  },
  truncateText: function (text, maxLength) {
    if (text.length <= maxLength) {
      return text;
    }
    return text.slice(0, maxLength) + "...";
  },
  getStorageData: async function (key) {
    let data = localStorage.getItem(key);
    if (_.isEmpty(data)) {
      return {};
    }

    try {
      const decryptedString = await decryptData(data, secretKey);
      if (decryptedString) {
        const decryptedData = JSON.parse(decryptedString);
        return _.isEmpty(decryptedData) ? {} : decryptedData;
      }
      return {};
    } catch (error) {
      return {};
    }
  },
  setStorageData: async function (key, value) {
    try {
      const ciphertext = await encryptData(JSON.stringify(value), secretKey);
      localStorage.setItem(key, ciphertext);
    } catch (error) {
    }
  },
  removeStorageData: function () {
    localStorage.clear();
    window.user = {};
    window.location.replace("/login");
  },

  loadScript(src) {
    var tag = document.createElement("script");
    tag.async = false;
    tag.src = src;
    var body = document.getElementsByTagName("body")[0];
    body.appendChild(tag);
  },

  overlay(is_show = false) {
    if (is_show === true) {
      if (document.getElementById("overlay"))
        document.getElementById("overlay").style.display = "block";
    } else {
      if (document.getElementById("overlay"))
        document.getElementById("overlay").style.display = "none";
    }
  },

  dateFormat(given_date) {
    return moment(given_date).format("YYYY-MM-DD hh:mm:ss");
  },
  dateFormattoYMD(given_date) {
    return moment(given_date).format("YYYY-MM-DD");
  },
  dateFormattoYMDSlashes(given_date) {
    return moment(given_date).format("L");
  },

  dayFromdate(given_date) {
    const date = moment(given_date);
    return date.format("dddd");
  },
  formatPhoneNumber(number) {
    if (!number || typeof number !== "string") {
      return "";
    }

    const countryCode = number.slice(0, 2);
    const areaCode = number.slice(2, 5);
    const firstPart = number.slice(5, 8);
    const secondPart = number.slice(8);

    const formattedNumber = `+${countryCode} (${areaCode}) ${firstPart}-${secondPart}`;

    return formattedNumber;
  },

  randomid(length) {
    let result = [];
    let characters =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let charactersLength = characters.length;

    const randomValues = crypto.getRandomValues(new Uint8Array(length));

    for (let i = 0; i < length; i++) {
      result.push(characters.charAt(randomValues[i] % charactersLength));
    }
    return result.join("");
  },
  encryptCryptoString: async function (data) {
    try {
      const secret = window.constants?.crypto_secret || secretKey;
      const ciphertext = await encryptData(data, secret);
      return ciphertext;
    } catch (error) {
      return data;
    }
  },
  timeDiff(sTime, eTime) {
    const startTime = moment(sTime, "HH:mm:ss");
    const endTime = moment(eTime, "HH:mm:ss");
    time_diff = moment.duration(endTime.diff(startTime));
    time_diff =
      time_diff > 0 ? time_diff : moment.duration(startTime.diff(endTime));
    return time_diff.hours() + ":" + time_diff.minutes();
  },
  getUid() {
    const randomValues = crypto.getRandomValues(new Uint32Array(1));
    const number = 100000000 + (randomValues[0] % 900000000);
    return number;
  },
  sendNotification: (
    type = "success",
    message = "Success",
    description = "..."
  ) => {
    notification[type]({ message, description });
  },
  successMessage: (data) => {
    return message.info(data);
  },
  sweetAlert: async function (
    type = "success",
    title = "Success",
    msg = "success",
    callback = () => {}
  ) {
    return Swal.fire({
      title: title,
      text: msg,
      icon: type,
      confirmButtonText: "OK",
    }).then(callback);
  },
  generateRanges: (step = 50, max = 1000) => {
    const ranges = [];
    for (let i = 0; i < max; i += step) {
      const range = `${i}-${i + step}`;
      ranges.push({ key: range, value: range });
    }
    return ranges;
  },
  formatPhoneNumber: (phoneNumber) => {
    const cleaned = phoneNumber.replace(/[^\d+]/g, "");

    const countryCode = cleaned.substring(0, 2);
    const areaCode = cleaned.substring(2, 5);
    const firstPart = cleaned.substring(5, 8);
    const secondPart = cleaned.substring(8, 11);

    return `${countryCode} ${areaCode} ${firstPart} ${secondPart}`;
  },

  getLabel: function (type, value) {
    const mappings = {
      professional_type: {
        broker: "Real Estate Broker",
        lender: "Lender/ mtg Broker",
        commercial: "Commercial Agent",
      },

      boolean: {
        true: "Yes",
        false: "No",
        1: "Yes",
        0: "No",
      },

      multi_state_license: {
        true: "Yes",
        false: "No",
        1: "Yes",
        0: "No",
      },

      status: {
        active: "Active",
        inactive: "Inactive",
        pending: "Pending",
        approved: "Approved",
        rejected: "Rejected",
      },

      user_role: {
        admin: "Administrator",
        user: "User",
        moderator: "Moderator",
        agent: "Agent",
        broker: "Broker",
      },
    };

    const typeMapping = mappings[type];

    if (!typeMapping) {
      return value;
    }

    const stringValue = String(value);

    return typeMapping[stringValue] || value;
  },

  date: dateHelper,
};

export default Helper;