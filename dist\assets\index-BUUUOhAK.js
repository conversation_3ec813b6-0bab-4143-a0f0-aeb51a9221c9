import{r as s,l as Y,m as Z,n as J,o as ge,aO as ue,N as q,$ as ee,g as T,w as me,P as te,K as ve}from"./index-3mE9H3a0.js";import{a as fe,x as ye,R as he,c as be,Q as Ce,t as xe,S as Se,X as Oe,u as Pe,T as $e,b as je}from"./index-B2p2olBm.js";import{u as ze,P as we,t as Ne}from"./button-CMBVME-6.js";const K=s.createContext({}),Ee=e=>{const{antCls:o,componentCls:n,iconCls:t,avatarBg:r,avatarColor:a,containerSize:p,containerSizeLG:l,containerSizeSM:g,textFontSize:f,textFontSizeLG:c,textFontSizeSM:b,borderRadius:O,borderRadiusLG:u,borderRadiusSM:P,lineWidth:d,lineType:y}=e,j=(i,z,w)=>({width:i,height:i,borderRadius:"50%",[`&${n}-square`]:{borderRadius:w},[`&${n}-icon`]:{fontSize:z,[`> ${t}`]:{margin:0}}});return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},J(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:a,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:r,border:`${ge(d)} ${y} transparent`,"&-image":{background:"transparent"},[`${o}-image-img`]:{display:"block"}}),j(p,f,O)),{"&-lg":Object.assign({},j(l,c,u)),"&-sm":Object.assign({},j(g,b,P)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},Be=e=>{const{componentCls:o,groupBorderColor:n,groupOverlapping:t,groupSpace:r}=e;return{[`${o}-group`]:{display:"inline-flex",[o]:{borderColor:n},"> *:not(:first-child)":{marginInlineStart:t}},[`${o}-group-popover`]:{[`${o} + ${o}`]:{marginInlineStart:r}}}},ke=e=>{const{controlHeight:o,controlHeightLG:n,controlHeightSM:t,fontSize:r,fontSizeLG:a,fontSizeXL:p,fontSizeHeading3:l,marginXS:g,marginXXS:f,colorBorderBg:c}=e;return{containerSize:o,containerSizeLG:n,containerSizeSM:t,textFontSize:Math.round((a+p)/2),textFontSizeLG:l,textFontSizeSM:r,groupSpace:f,groupOverlapping:-g,groupBorderColor:c}},oe=Y("Avatar",e=>{const{colorTextLightSolid:o,colorTextPlaceholder:n}=e,t=Z(e,{avatarBg:n,avatarColor:o});return[Ee(t),Be(t)]},ke);var Re=function(e,o){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)o.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};const ne=s.forwardRef((e,o)=>{const{prefixCls:n,shape:t,size:r,src:a,srcSet:p,icon:l,className:g,rootClassName:f,style:c,alt:b,draggable:O,children:u,crossOrigin:P,gap:d=4,onError:y}=e,j=Re(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[i,z]=s.useState(1),[w,_]=s.useState(!1),[B,k]=s.useState(!0),M=s.useRef(null),N=s.useRef(null),R=ue(o,M),{getPrefixCls:E,avatar:$}=s.useContext(q),C=s.useContext(K),I=()=>{if(!N.current||!M.current)return;const h=N.current.offsetWidth,v=M.current.offsetWidth;h!==0&&v!==0&&d*2<v&&z(v-d*2<h?(v-d*2)/h:1)};s.useEffect(()=>{_(!0)},[]),s.useEffect(()=>{k(!0),z(1)},[a]),s.useEffect(I,[d]);const V=()=>{(y==null?void 0:y())!==!1&&k(!1)},m=ze(h=>{var v,H;return(H=(v=r??(C==null?void 0:C.size))!==null&&v!==void 0?v:h)!==null&&H!==void 0?H:"default"}),G=Object.keys(typeof m=="object"?m||{}:{}).some(h=>["xs","sm","md","lg","xl","xxl"].includes(h)),W=fe(G),D=s.useMemo(()=>{if(typeof m!="object")return{};const h=ye.find(H=>W[H]),v=m[h];return v?{width:v,height:v,fontSize:v&&(l||u)?v/2:18}:{}},[W,m]),x=E("avatar",n),S=ee(x),[A,F,ie]=oe(x,S),le=T({[`${x}-lg`]:m==="large",[`${x}-sm`]:m==="small"}),Q=s.isValidElement(a),ce=t||(C==null?void 0:C.shape)||"circle",de=T(x,le,$==null?void 0:$.className,`${x}-${ce}`,{[`${x}-image`]:Q||a&&B,[`${x}-icon`]:!!l},ie,S,g,f,F),pe=typeof m=="number"?{width:m,height:m,fontSize:l?m/2:18}:{};let L;if(typeof a=="string"&&B)L=s.createElement("img",{src:a,draggable:O,srcSet:p,onError:V,alt:b,crossOrigin:P});else if(Q)L=a;else if(l)L=l;else if(w||i!==1){const h=`scale(${i})`,v={msTransform:h,WebkitTransform:h,transform:h};L=s.createElement(he,{onResize:I},s.createElement("span",{className:`${x}-string`,ref:N,style:Object.assign({},v)},u))}else L=s.createElement("span",{className:`${x}-string`,style:{opacity:0},ref:N},u);return A(s.createElement("span",Object.assign({},j,{style:Object.assign(Object.assign(Object.assign(Object.assign({},pe),D),$==null?void 0:$.style),c),className:de,ref:R}),L))}),X=e=>e?typeof e=="function"?e():e:null,Ie=e=>{const{componentCls:o,popoverColor:n,titleMinWidth:t,fontWeightStrong:r,innerPadding:a,boxShadowSecondary:p,colorTextHeading:l,borderRadiusLG:g,zIndexPopup:f,titleMarginBottom:c,colorBgElevated:b,popoverBg:O,titleBorderBottom:u,innerContentPadding:P,titlePadding:d}=e;return[{[o]:Object.assign(Object.assign({},J(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:f,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":b,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${o}-content`]:{position:"relative"},[`${o}-inner`]:{backgroundColor:O,backgroundClip:"padding-box",borderRadius:g,boxShadow:p,padding:a},[`${o}-title`]:{minWidth:t,marginBottom:c,color:l,fontWeight:r,borderBottom:u,padding:d},[`${o}-inner-content`]:{color:n,padding:P}})},Ce(e,"var(--antd-arrow-background-color)"),{[`${o}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${o}-content`]:{display:"inline-block"}}}]},Te=e=>{const{componentCls:o}=e;return{[o]:we.map(n=>{const t=e[`${n}6`];return{[`&${o}-${n}`]:{"--antd-arrow-background-color":t,[`${o}-inner`]:{backgroundColor:t},[`${o}-arrow`]:{background:"transparent"}}}})}},_e=e=>{const{lineWidth:o,controlHeight:n,fontHeight:t,padding:r,wireframe:a,zIndexPopupBase:p,borderRadiusLG:l,marginXS:g,lineType:f,colorSplit:c,paddingSM:b}=e,O=n-t,u=O/2,P=O/2-o,d=r;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:p+30},xe(e)),Se({contentRadius:l,limitVerticalRadius:!0})),{innerPadding:a?0:12,titleMarginBottom:a?0:g,titlePadding:a?`${u}px ${d}px ${P}px`:0,titleBorderBottom:a?`${o}px ${f} ${c}`:"none",innerContentPadding:a?`${b}px ${d}px`:0})},re=Y("Popover",e=>{const{colorBgElevated:o,colorText:n}=e,t=Z(e,{popoverBg:o,popoverColor:n});return[Ie(t),Te(t),be(t,"zoom-big")]},_e,{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});var Me=function(e,o){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)o.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};const se=e=>{let{title:o,content:n,prefixCls:t}=e;return!o&&!n?null:s.createElement(s.Fragment,null,o&&s.createElement("div",{className:`${t}-title`},o),n&&s.createElement("div",{className:`${t}-inner-content`},n))},We=e=>{const{hashId:o,prefixCls:n,className:t,style:r,placement:a="top",title:p,content:l,children:g}=e,f=X(p),c=X(l),b=T(o,n,`${n}-pure`,`${n}-placement-${a}`,t);return s.createElement("div",{className:b,style:r},s.createElement("div",{className:`${n}-arrow`}),s.createElement(Oe,Object.assign({},e,{className:o,prefixCls:n}),g||s.createElement(se,{prefixCls:n,title:f,content:c})))},Ae=e=>{const{prefixCls:o,className:n}=e,t=Me(e,["prefixCls","className"]),{getPrefixCls:r}=s.useContext(q),a=r("popover",o),[p,l,g]=re(a);return p(s.createElement(We,Object.assign({},t,{prefixCls:a,hashId:l,className:T(n,g)})))};var Le=function(e,o){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)o.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};const Ve=s.forwardRef((e,o)=>{var n,t;const{prefixCls:r,title:a,content:p,overlayClassName:l,placement:g="top",trigger:f="hover",children:c,mouseEnterDelay:b=.1,mouseLeaveDelay:O=.1,onOpenChange:u,overlayStyle:P={},styles:d,classNames:y}=e,j=Le(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:i,className:z,style:w,classNames:_,styles:B}=me("popover"),k=i("popover",r),[M,N,R]=re(k),E=i(),$=T(l,N,R,z,_.root,y==null?void 0:y.root),C=T(_.body,y==null?void 0:y.body),[I,V]=Pe(!1,{value:(n=e.open)!==null&&n!==void 0?n:e.visible,defaultValue:(t=e.defaultOpen)!==null&&t!==void 0?t:e.defaultVisible}),m=(S,A)=>{V(S,!0),u==null||u(S,A)},G=S=>{S.keyCode===ve.ESC&&m(!1,S)},W=S=>{m(S)},D=X(a),x=X(p);return M(s.createElement($e,Object.assign({placement:g,trigger:f,mouseEnterDelay:b,mouseLeaveDelay:O},j,{prefixCls:k,classNames:{root:$,body:C},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},B.root),w),P),d==null?void 0:d.root),body:Object.assign(Object.assign({},B.body),d==null?void 0:d.body)},ref:o,open:I,onOpenChange:W,overlay:D||x?s.createElement(se,{prefixCls:k,title:D,content:x}):null,transitionName:je(E,"zoom-big",j.transitionName),"data-popover-inject":!0}),te(c,{onKeyDown:S=>{var A,F;s.isValidElement(c)&&((F=c==null?void 0:(A=c.props).onKeyDown)===null||F===void 0||F.call(A,S)),G(S)}})))}),ae=Ve;ae._InternalPanelDoNotUseOrYouWillBeFired=Ae;const U=e=>{const{size:o,shape:n}=s.useContext(K),t=s.useMemo(()=>({size:e.size||o,shape:e.shape||n}),[e.size,e.shape,o,n]);return s.createElement(K.Provider,{value:t},e.children)},Ge=e=>{var o,n,t,r;const{getPrefixCls:a,direction:p}=s.useContext(q),{prefixCls:l,className:g,rootClassName:f,style:c,maxCount:b,maxStyle:O,size:u,shape:P,maxPopoverPlacement:d,maxPopoverTrigger:y,children:j,max:i}=e,z=a("avatar",l),w=`${z}-group`,_=ee(z),[B,k,M]=oe(z,_),N=T(w,{[`${w}-rtl`]:p==="rtl"},M,_,g,f,k),R=Ne(j).map((C,I)=>te(C,{key:`avatar-key-${I}`})),E=(i==null?void 0:i.count)||b,$=R.length;if(E&&E<$){const C=R.slice(0,E),I=R.slice(E,$),V=(i==null?void 0:i.style)||O,m=((o=i==null?void 0:i.popover)===null||o===void 0?void 0:o.trigger)||y||"hover",G=((n=i==null?void 0:i.popover)===null||n===void 0?void 0:n.placement)||d||"top",W=Object.assign(Object.assign({content:I},i==null?void 0:i.popover),{classNames:{root:T(`${w}-popover`,(r=(t=i==null?void 0:i.popover)===null||t===void 0?void 0:t.classNames)===null||r===void 0?void 0:r.root)},placement:G,trigger:m});return C.push(s.createElement(ae,Object.assign({key:"avatar-popover-key",destroyOnHidden:!0},W),s.createElement(ne,{style:V},`+${$-E}`))),B(s.createElement(U,{shape:P,size:u},s.createElement("div",{className:N,style:c},C)))}return B(s.createElement(U,{shape:P,size:u},s.createElement("div",{className:N,style:c},R)))},Fe=ne;Fe.Group=Ge;export{Fe as A,ae as P};
