import { useState, useCallback } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import apiClient from "@/services/apiClient";

export const useLikePost = (
  postId,
  initialLiked = false,
  initialLikesCount = 0
) => {
  const [isLiked, setIsLiked] = useState(initialLiked);
  const [likesCount, setLikesCount] = useState(initialLikesCount);
  const queryClient = useQueryClient();

  const { mutate: toggleLike, isPending: isToggling } = useMutation({
    mutationFn: async (liked) => {
      const payload = {
        type: "post",
        value: liked,
      };

      const response = await apiClient.request("likePost", {
        slug: postId,
        data: payload,
        useFormData: false,
        showSuccessNotification: false,
      });

      return response;
    },
    onMutate: async (liked) => {
      setIsLiked(liked);
      setLikesCount((prev) => (liked ? prev + 1 : Math.max(0, prev - 1)));
    },
    onError: (error, liked) => {
      // Revert optimistic update on error
      setIsLiked(!liked);
      setLikesCount((prev) => (liked ? Math.max(0, prev - 1) : prev + 1));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["postItem"],
        exact: false,
      });
    },
  });

  const handleLikeToggle = useCallback(() => {
    const newLikedState = !isLiked;
    toggleLike(newLikedState);
  }, [isLiked, toggleLike]);

  return {
    isLiked,
    likesCount,
    isToggling,
    handleLikeToggle,
  };
};

export default useLikePost;
