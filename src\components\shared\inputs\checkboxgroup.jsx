import React from "react";
import { Checkbox, Form } from "antd";

const CheckboxGroupInput = ({
  name,
  rules,
  label,
  options,
  onChange,
  disabled,
  className,
  ...props
}) => {
  return (
    <Form.Item name={name} rules={rules} label={label}>
      <Checkbox.Group
        options={options}
        onChange={onChange}
        disabled={disabled}
        className={className}
        {...props}
      />
    </Form.Item>
  );
};

export default CheckboxGroupInput;