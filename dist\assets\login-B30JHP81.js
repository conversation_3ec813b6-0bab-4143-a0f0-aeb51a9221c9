import{r as u,u as h,a as g,j as e,L as i,H as x}from"./index-3mE9H3a0.js";import{A as p}from"./index-CTEMBgeC.js";import{B as t,c as n,C as j,v as a}from"./index-Cd-Wothc.js";import{F as b}from"./flatbutton-Yo0mdDJ8.js";import{u as f}from"./useMutation-lvneVzbk.js";import{F as v}from"./react-stripe.esm-CaXK0k-R.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";const w=()=>{const l=h();g();const{mutate:r,isPending:o}=f("login",{useFormData:!1,onSuccess:async s=>{s&&(await x.setStorageData("session",s.data),window.user=s.data,l("/home",{replace:!0}))}}),c=s=>{const d={...s,device:"web",device_token:"web-token-"+Date.now()};r(d)},m=s=>{console.log(`checked = ${s.target.checked}`)};return e.jsxs(p,{showSidebar:!1,children:[e.jsx("div",{className:"text-center logo mb-3",children:e.jsx("img",{src:"/assets/img/suscribtion-img.png",alt:"Auth Logo"})}),e.jsxs("div",{className:"col-12 text-center",children:[e.jsx("h1",{className:"font-46 color-black mb-1",children:"SPHERE"}),e.jsx("h1",{className:"font-46 color-black mb-1",children:"Real Estate Network"}),e.jsx("p",{children:"Log in to continue using your account"})]}),e.jsxs(v,{name:"login",layout:"vertical",onFinish:c,initialValues:{remember:!0},autoComplete:"off",children:[e.jsx(t,{name:"email",placeholder:"Email",label:"Email Address",rules:n("email",a.required,a.email)}),e.jsx(t,{type:"password",name:"password",placeholder:"Password",label:"Password",rules:n("password",a.required,a.password)}),e.jsxs("div",{className:"d-flex align-items-center justify-content-between mt-0",children:[e.jsx("div",{className:"mt-2 check-item",children:e.jsx(j,{onChange:m,children:"Remember me"})}),e.jsx("div",{children:e.jsx(i,{to:"/forgetpassword",className:"mt-1 font-600 font-14 d-block color-blue ",children:"Forgot Password?"})})]}),e.jsx("div",{children:e.jsx(b,{title:o?"Sign In...":"Sign In",className:"mx-auto mt-4 signin-btn mt-5",htmlType:"submit",loading:o,disabled:o})}),e.jsx("div",{className:"login-p",children:e.jsx("p",{children:"Or login with"})}),e.jsxs("div",{className:"socail-login",children:[e.jsx("div",{children:e.jsx("img",{src:"/assets/img/google.png",alt:""})}),e.jsx("div",{children:e.jsx("p",{children:"Sign in with Google"})})]}),e.jsx("div",{children:e.jsxs("p",{className:"signup-text",children:["Don’t have an account?",e.jsx(i,{to:"/signup",className:"color-blue font-600 font-16 ms-1",children:"Sign up"})]})})]})]})},P=u.memo(w);export{P as default};
