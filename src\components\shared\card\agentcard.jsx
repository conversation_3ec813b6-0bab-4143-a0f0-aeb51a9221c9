// components/agent/AgentCard.jsx

import React from "react";
import { useNavigate } from "react-router-dom";
import IconButton from "@/components/shared/button/iconbutton";

const AgentCard = ({ agent }) => {
  const navigate = useNavigate();

  return (
    <div
      className="agent-item d-flex align-items-center justify-content-between"
      onClick={() => navigate("/agent/detail/posts")}
    >
      <div className="d-flex align-items-center">
        <div className="agent-profile">
          <img
            src={
              agent.image_url ? agent.image_url : "/assets/img/placeholder.jpg"
            }
            alt={agent.name}
          />
        </div>
        <div className="ms-3">
          <div className="d-flex">
            <p className="me-2 font-600">{agent.name}</p>
          </div>
          <p className="color-light">{agent.city}</p>
        </div>
      </div>
      <div className="d-flex align-items-center">
        <div className="me-3">
          <IconButton
            icon={<img src="/assets/img/call-icon.png" />}
            title="Call"
            className="gray-btn"
            onClick={() => window.open(`tel:${agent.mobile_no}`)}
          />
        </div>
        <div className="me-3">
          <IconButton
            icon={<img src="/assets/img/message-icon.png" />}
            title="Message"
            className="gray-btn"
          />
        </div>
        <div className="me-3">
          <IconButton
            icon={<img src="/assets/img/mail-icon.png" />}
            title="Mail"
            className="blue-btn"
            onClick={() => window.open(`mailto:${agent.email}`)}
          />
        </div>
      </div>
    </div>
  );
};

export default AgentCard;
