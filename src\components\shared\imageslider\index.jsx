import React, { useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Thumbs, Pagination } from "swiper/modules";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/thumbs";
import "swiper/css/pagination";

import "./imageslider.css";

const ImageSlider = ({
  images = [],
  alt = "Property Image",
  className = "",
}) => {
  const [thumbsSwiper, setThumbsSwiper] = useState(null);

  // Use placeholder if no images
  const displayImages =
    images && images.length > 0
      ? images
      : [{ url: "/assets/img/property-placeholder.png", id: "placeholder" }];

  // If only one image (including placeholder), show simple image
  if (displayImages.length === 1) {
    return (
      <div className={`image-slider single-image ${className}`}>
        <div className="main-image">
          <img
            src={
              displayImages[0].url ||
              displayImages[0].image_url ||
              "/assets/img/image-1.png"
            }
            alt={alt}
            onError={(e) => {
              e.target.src = "/assets/img/image-1.png";
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className={`image-slider ${className}`}>
      {/* Main Image Swiper */}
      <Swiper
        spaceBetween={10}
        navigation={true}
        thumbs={{
          swiper: thumbsSwiper && !thumbsSwiper.destroyed ? thumbsSwiper : null,
        }}
        modules={[Navigation, Thumbs, Pagination]}
        className="main-swiper"
        pagination={{
          type: "fraction",
        }}
      >
        {displayImages.map((image, index) => (
          <SwiperSlide key={image.id || index}>
            <div className="main-image">
              <img
                src={
                  image.url || image.image_url || "/assets/img/placeholder.jpg"
                }
                alt={`${alt} ${index + 1}`}
                onError={(e) => {
                  e.target.src = "/assets/img/placeholder.jpg";
                }}
              />
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Thumbnail Swiper */}
      {displayImages.length > 1 && (
        <Swiper
          onSwiper={setThumbsSwiper}
          spaceBetween={10}
          slidesPerView={4}
          freeMode={true}
          watchSlidesProgress={true}
          modules={[Navigation, Thumbs]}
          className="thumbs-swiper"
          breakpoints={{
            320: {
              slidesPerView: 3,
              spaceBetween: 8,
            },
            480: {
              slidesPerView: 4,
              spaceBetween: 10,
            },
            768: {
              slidesPerView: 5,
              spaceBetween: 10,
            },
            1024: {
              slidesPerView: 6,
              spaceBetween: 12,
            },
          }}
        >
          {displayImages.map((image, index) => (
            <SwiperSlide key={`thumb-${image.id || index}`}>
              <div className="thumbnail">
                <img
                  src={
                    image.url || image.image_url || "/assets/img/image-1.png"
                  }
                  alt={`${alt} thumbnail ${index + 1}`}
                  onError={(e) => {
                    e.target.src = "/assets/img/image-1.png";
                  }}
                />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      )}
    </div>
  );
};

export default ImageSlider;
