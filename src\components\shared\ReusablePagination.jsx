import React from "react";
import { Pagination } from "antd";

const ReusablePagination = ({
  pagination,
  handlePageChange,
  isLoading,
  itemName = "items",
  pageSizeOptions = ["10", "20", "50"],
  showSizeChanger = true,
  showQuickJumper = true,
  className = "",
  align = "", // center, start, end
}) => {
  if (isLoading || !pagination || pagination.totalPages <= 1) {
    return null;
  }

  const alignmentClass =
    {
      center: "justify-content-center",
      start: "justify-content-start",
      end: "justify-content-end",
    }[align] || "justify-content-end";

  return (
    <div className={`row my-5 ${className}`}>
      <div className={`col-12 d-flex ${alignmentClass}`}>
        <Pagination
          current={pagination.currentPage}
          pageSize={pagination.pageLimit}
          total={pagination.totalCount}
          onChange={handlePageChange}
          onShowSizeChange={handlePageChange}
          showSizeChanger={showSizeChanger}
          // showQuickJumper={showQuickJumper}
          showTotal={(total, range) =>
            `Showing ${range[0]}-${range[1]} of ${total} ${itemName}`
          }
          pageSizeOptions={pageSizeOptions}
        />
      </div>
    </div>
  );
};

export default ReusablePagination;
