import{r as i,I as Ke,e as te,l as Fe,m as ft,n as yt,ab as Ve,o as B,N as xe,g as A,P as Xe,G as Ln,au as We,K as $e,aO as Wn,a$ as qt,R as le,b3 as Kn,F as fe,f as ie,h as J,aR as ar,at as Fn,k as re,M as Te,J as Vn,ax as Xn,aC as lr,aD as sr,aE as cr,aG as ur,i as dr,aK as mr,b4 as gr,q as pr,aw as fr,a2 as $n,av as vt,O as vr,a0 as qn,X as Gn,$ as Un,s as yn,b2 as br,v as Yn,a1 as hr,w as Zn,b5 as Cr,b6 as $r,b7 as yr,b8 as xr,b9 as Sr,ba as Gt,bb as Ir,bc as wr,bd as Qn,be as Or,bf as Nr,bg as Er,bh as xn,j as I,bi as Pr,u as Rr,a as Mr,L as je,b as Br,bj as jr}from"./index-3mE9H3a0.js";import{J as Jn,K as eo,h as to,u as nt,F as ot,M as Tr,O as zr,T as kr,C as Hr,g as Dr,k as bt,c as no,i as Ar,s as _r,m as Lr,n as Wr,o as Kr,Q as Fr,l as Sn,S as Vr,t as Xr,U as qr,r as Gr,p as Ur}from"./index-B2p2olBm.js";import{i as Yr,o as Oe,t as xt,b as Zr,C as Qr,a as Jr,B as In}from"./button-CMBVME-6.js";import{A as wn}from"./index-BUUUOhAK.js";var ei={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"},ti=function(t,n){return i.createElement(Ke,te({},t,{ref:n,icon:ei}))},Dt=i.forwardRef(ti);const ni=new Ve("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),oi=new Ve("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),ri=new Ve("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),ii=new Ve("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),ai=new Ve("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),li=new Ve("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),si=e=>{const{componentCls:t,iconCls:n,antCls:o,badgeShadowSize:r,textFontSize:l,textFontSizeSM:a,statusSize:s,dotSize:c,textFontWeight:d,indicatorHeight:g,indicatorHeightSM:u,marginXS:m,calc:b}=e,p=`${o}-scroll-number`,f=Jn(e,(v,x)=>{let{darkColor:y}=x;return{[`&${t} ${t}-color-${v}`]:{background:y,[`&:not(${t}-count)`]:{color:y},"a:hover &":{background:y}}}});return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},yt(e)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,[`${t}-count`]:{display:"inline-flex",justifyContent:"center",zIndex:e.indicatorZIndex,minWidth:g,height:g,color:e.badgeTextColor,fontWeight:d,fontSize:l,lineHeight:B(g),whiteSpace:"nowrap",textAlign:"center",background:e.badgeColor,borderRadius:b(g).div(2).equal(),boxShadow:`0 0 0 ${B(r)} ${e.badgeShadowColor}`,transition:`background ${e.motionDurationMid}`,a:{color:e.badgeTextColor},"a:hover":{color:e.badgeTextColor},"a:hover &":{background:e.badgeColorHover}},[`${t}-count-sm`]:{minWidth:u,height:u,fontSize:a,lineHeight:B(u),borderRadius:b(u).div(2).equal()},[`${t}-multiple-words`]:{padding:`0 ${B(e.paddingXS)}`,bdi:{unicodeBidi:"plaintext"}},[`${t}-dot`]:{zIndex:e.indicatorZIndex,width:c,minWidth:c,height:c,background:e.badgeColor,borderRadius:"100%",boxShadow:`0 0 0 ${B(r)} ${e.badgeShadowColor}`},[`${t}-count, ${t}-dot, ${p}-custom-component`]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",[`&${n}-spin`]:{animationName:li,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&${t}-status`]:{lineHeight:"inherit",verticalAlign:"baseline",[`${t}-status-dot`]:{position:"relative",top:-1,display:"inline-block",width:s,height:s,verticalAlign:"middle",borderRadius:"50%"},[`${t}-status-success`]:{backgroundColor:e.colorSuccess},[`${t}-status-processing`]:{overflow:"visible",color:e.colorInfo,backgroundColor:e.colorInfo,borderColor:"currentcolor","&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:r,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:ni,animationDuration:e.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},[`${t}-status-default`]:{backgroundColor:e.colorTextPlaceholder},[`${t}-status-error`]:{backgroundColor:e.colorError},[`${t}-status-warning`]:{backgroundColor:e.colorWarning},[`${t}-status-text`]:{marginInlineStart:m,color:e.colorText,fontSize:e.fontSize}}}),f),{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:oi,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`${t}-zoom-leave`]:{animationName:ri,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`&${t}-not-a-wrapper`]:{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:ii,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`${t}-zoom-leave`]:{animationName:ai,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`&:not(${t}-status)`]:{verticalAlign:"middle"},[`${p}-custom-component, ${t}-count`]:{transform:"none"},[`${p}-custom-component, ${p}`]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[p]:{overflow:"hidden",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack}`,[`${p}-only`]:{position:"relative",display:"inline-block",height:g,transition:`all ${e.motionDurationSlow} ${e.motionEaseOutBack}`,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",[`> p${p}-only-unit`]:{height:g,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},[`${p}-symbol`]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",[`${t}-count, ${t}-dot, ${p}-custom-component`]:{transform:"translate(-50%, -50%)"}}})}},oo=e=>{const{fontHeight:t,lineWidth:n,marginXS:o,colorBorderBg:r}=e,l=t,a=n,s=e.colorTextLightSolid,c=e.colorError,d=e.colorErrorHover;return ft(e,{badgeFontHeight:l,badgeShadowSize:a,badgeTextColor:s,badgeColor:c,badgeColorHover:d,badgeShadowColor:r,badgeProcessingDuration:"1.2s",badgeRibbonOffset:o,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"})},ro=e=>{const{fontSize:t,lineHeight:n,fontSizeSM:o,lineWidth:r}=e;return{indicatorZIndex:"auto",indicatorHeight:Math.round(t*n)-2*r,indicatorHeightSM:t,dotSize:o/2,textFontSize:o,textFontSizeSM:o,textFontWeight:"normal",statusSize:o/2}},ci=Fe("Badge",e=>{const t=oo(e);return si(t)},ro),ui=e=>{const{antCls:t,badgeFontHeight:n,marginXS:o,badgeRibbonOffset:r,calc:l}=e,a=`${t}-ribbon`,s=`${t}-ribbon-wrapper`,c=Jn(e,(d,g)=>{let{darkColor:u}=g;return{[`&${a}-color-${d}`]:{background:u,color:u}}});return{[s]:{position:"relative"},[a]:Object.assign(Object.assign(Object.assign(Object.assign({},yt(e)),{position:"absolute",top:o,padding:`0 ${B(e.paddingXS)}`,color:e.colorPrimary,lineHeight:B(n),whiteSpace:"nowrap",backgroundColor:e.colorPrimary,borderRadius:e.borderRadiusSM,[`${a}-text`]:{color:e.badgeTextColor},[`${a}-corner`]:{position:"absolute",top:"100%",width:r,height:r,color:"currentcolor",border:`${B(l(r).div(2).equal())} solid`,transform:e.badgeRibbonCornerTransform,transformOrigin:"top",filter:e.badgeRibbonCornerFilter}}),c),{[`&${a}-placement-end`]:{insetInlineEnd:l(r).mul(-1).equal(),borderEndEndRadius:0,[`${a}-corner`]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},[`&${a}-placement-start`]:{insetInlineStart:l(r).mul(-1).equal(),borderEndStartRadius:0,[`${a}-corner`]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}},di=Fe(["Badge","Ribbon"],e=>{const t=oo(e);return ui(t)},ro),mi=e=>{const{className:t,prefixCls:n,style:o,color:r,children:l,text:a,placement:s="end",rootClassName:c}=e,{getPrefixCls:d,direction:g}=i.useContext(xe),u=d("ribbon",n),m=`${u}-wrapper`,[b,p,f]=di(u,m),v=eo(r,!1),x=A(u,`${u}-placement-${s}`,{[`${u}-rtl`]:g==="rtl",[`${u}-color-${r}`]:v},t),y={},$={};return r&&!v&&(y.background=r,$.color=r),b(i.createElement("div",{className:A(m,c,p,f)},l,i.createElement("div",{className:A(x,p),style:Object.assign(Object.assign({},y),o)},i.createElement("span",{className:`${u}-text`},a),i.createElement("div",{className:`${u}-corner`,style:$}))))},On=e=>{const{prefixCls:t,value:n,current:o,offset:r=0}=e;let l;return r&&(l={position:"absolute",top:`${r}00%`,left:0}),i.createElement("span",{style:l,className:A(`${t}-only-unit`,{current:o})},n)};function gi(e,t,n){let o=e,r=0;for(;(o+10)%10!==t;)o+=n,r+=n;return r}const pi=e=>{const{prefixCls:t,count:n,value:o}=e,r=Number(o),l=Math.abs(n),[a,s]=i.useState(r),[c,d]=i.useState(l),g=()=>{s(r),d(l)};i.useEffect(()=>{const b=setTimeout(g,1e3);return()=>clearTimeout(b)},[r]);let u,m;if(a===r||Number.isNaN(r)||Number.isNaN(a))u=[i.createElement(On,Object.assign({},e,{key:r,current:!0}))],m={transition:"none"};else{u=[];const b=r+10,p=[];for(let y=r;y<=b;y+=1)p.push(y);const f=c<l?1:-1,v=p.findIndex(y=>y%10===a);u=(f<0?p.slice(0,v+1):p.slice(v)).map((y,$)=>{const w=y%10;return i.createElement(On,Object.assign({},e,{key:y,value:w,offset:f<0?$-v:$,current:$===v}))}),m={transform:`translateY(${-gi(a,r,f)}00%)`}}return i.createElement("span",{className:`${t}-only`,style:m,onTransitionEnd:g},u)};var fi=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const vi=i.forwardRef((e,t)=>{const{prefixCls:n,count:o,className:r,motionClassName:l,style:a,title:s,show:c,component:d="sup",children:g}=e,u=fi(e,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:m}=i.useContext(xe),b=m("scroll-number",n),p=Object.assign(Object.assign({},u),{"data-show":c,style:a,className:A(b,r,l),title:s});let f=o;if(o&&Number(o)%1===0){const v=String(o).split("");f=i.createElement("bdi",null,v.map((x,y)=>i.createElement(pi,{prefixCls:b,count:Number(o),value:x,key:v.length-y})))}return a!=null&&a.borderColor&&(p.style=Object.assign(Object.assign({},a),{boxShadow:`0 0 0 1px ${a.borderColor} inset`})),g?Xe(g,v=>({className:A(`${b}-custom-component`,v==null?void 0:v.className,l)})):i.createElement(d,Object.assign({},p,{ref:t}),f)});var bi=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const hi=i.forwardRef((e,t)=>{var n,o,r,l,a;const{prefixCls:s,scrollNumberPrefixCls:c,children:d,status:g,text:u,color:m,count:b=null,overflowCount:p=99,dot:f=!1,size:v="default",title:x,offset:y,style:$,className:w,rootClassName:S,classNames:h,styles:E,showZero:O=!1}=e,P=bi(e,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:R,direction:z,badge:C}=i.useContext(xe),N=R("badge",s),[M,k,G]=ci(N),j=b>p?`${p}+`:b,H=j==="0"||j===0,q=b===null||H&&!O,V=(g!=null||m!=null)&&q,X=f&&!H,F=X?"":j,L=i.useMemo(()=>(F==null||F===""||H&&!O)&&!X,[F,H,O,X]),W=i.useRef(b);L||(W.current=b);const D=W.current,Q=i.useRef(F);L||(Q.current=F);const Y=Q.current,T=i.useRef(X);L||(T.current=X);const _=i.useMemo(()=>{if(!y)return Object.assign(Object.assign({},C==null?void 0:C.style),$);const K={marginTop:y[1]};return z==="rtl"?K.left=parseInt(y[0],10):K.right=-parseInt(y[0],10),Object.assign(Object.assign(Object.assign({},K),C==null?void 0:C.style),$)},[z,y,$,C==null?void 0:C.style]),ne=x??(typeof D=="string"||typeof D=="number"?D:void 0),oe=L||!u?null:i.createElement("span",{className:`${N}-status-text`},u),Ne=!D||typeof D!="object"?void 0:Xe(D,K=>({style:Object.assign(Object.assign({},_),K.style)})),ve=eo(m,!1),de=A(h==null?void 0:h.indicator,(n=C==null?void 0:C.classNames)===null||n===void 0?void 0:n.indicator,{[`${N}-status-dot`]:V,[`${N}-status-${g}`]:!!g,[`${N}-color-${m}`]:ve}),be={};m&&!ve&&(be.color=m,be.background=m);const ye=A(N,{[`${N}-status`]:V,[`${N}-not-a-wrapper`]:!d,[`${N}-rtl`]:z==="rtl"},w,S,C==null?void 0:C.className,(o=C==null?void 0:C.classNames)===null||o===void 0?void 0:o.root,h==null?void 0:h.root,k,G);if(!d&&V){const K=_.color;return M(i.createElement("span",Object.assign({},P,{className:ye,style:Object.assign(Object.assign(Object.assign({},E==null?void 0:E.root),(r=C==null?void 0:C.styles)===null||r===void 0?void 0:r.root),_)}),i.createElement("span",{className:de,style:Object.assign(Object.assign(Object.assign({},E==null?void 0:E.indicator),(l=C==null?void 0:C.styles)===null||l===void 0?void 0:l.indicator),be)}),u&&i.createElement("span",{style:{color:K},className:`${N}-status-text`},u)))}return M(i.createElement("span",Object.assign({ref:t},P,{className:ye,style:Object.assign(Object.assign({},(a=C==null?void 0:C.styles)===null||a===void 0?void 0:a.root),E==null?void 0:E.root)}),d,i.createElement(Ln,{visible:!L,motionName:`${N}-zoom`,motionAppear:!1,motionDeadline:1e3},K=>{let{className:Se}=K;var Ie,Ee;const Ge=R("scroll-number",c),Pe=T.current,me=A(h==null?void 0:h.indicator,(Ie=C==null?void 0:C.classNames)===null||Ie===void 0?void 0:Ie.indicator,{[`${N}-dot`]:Pe,[`${N}-count`]:!Pe,[`${N}-count-sm`]:v==="small",[`${N}-multiple-words`]:!Pe&&Y&&Y.toString().length>1,[`${N}-status-${g}`]:!!g,[`${N}-color-${m}`]:ve});let Re=Object.assign(Object.assign(Object.assign({},E==null?void 0:E.indicator),(Ee=C==null?void 0:C.styles)===null||Ee===void 0?void 0:Ee.indicator),_);return m&&!ve&&(Re=Re||{},Re.background=m),i.createElement(vi,{prefixCls:Ge,show:!L,motionClassName:Se,className:me,count:Y,title:ne,style:Re,key:"scrollNumber"},Ne)}),oe))}),At=hi;At.Ribbon=mi;var Ci={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"},$i=function(t,n){return i.createElement(Ke,te({},t,{ref:n,icon:Ci}))},_t=i.forwardRef($i),yi=$e.ESC,xi=$e.TAB;function Si(e){var t=e.visible,n=e.triggerRef,o=e.onVisibleChange,r=e.autoFocus,l=e.overlayRef,a=i.useRef(!1),s=function(){if(t){var u,m;(u=n.current)===null||u===void 0||(m=u.focus)===null||m===void 0||m.call(u),o==null||o(!1)}},c=function(){var u;return(u=l.current)!==null&&u!==void 0&&u.focus?(l.current.focus(),a.current=!0,!0):!1},d=function(u){switch(u.keyCode){case yi:s();break;case xi:{var m=!1;a.current||(m=c()),m?u.preventDefault():s();break}}};i.useEffect(function(){return t?(window.addEventListener("keydown",d),r&&We(c,3),function(){window.removeEventListener("keydown",d),a.current=!1}):function(){a.current=!1}},[t])}var Ii=i.forwardRef(function(e,t){var n=e.overlay,o=e.arrow,r=e.prefixCls,l=i.useMemo(function(){var s;return typeof n=="function"?s=n():s=n,s},[n]),a=Wn(t,qt(l));return le.createElement(le.Fragment,null,o&&le.createElement("div",{className:"".concat(r,"-arrow")}),le.cloneElement(l,{ref:Kn(l)?a:void 0}))}),Ae={adjustX:1,adjustY:1},_e=[0,0],wi={topLeft:{points:["bl","tl"],overflow:Ae,offset:[0,-4],targetOffset:_e},top:{points:["bc","tc"],overflow:Ae,offset:[0,-4],targetOffset:_e},topRight:{points:["br","tr"],overflow:Ae,offset:[0,-4],targetOffset:_e},bottomLeft:{points:["tl","bl"],overflow:Ae,offset:[0,4],targetOffset:_e},bottom:{points:["tc","bc"],overflow:Ae,offset:[0,4],targetOffset:_e},bottomRight:{points:["tr","br"],overflow:Ae,offset:[0,4],targetOffset:_e}},Oi=["arrow","prefixCls","transitionName","animation","align","placement","placements","getPopupContainer","showAction","hideAction","overlayClassName","overlayStyle","visible","trigger","autoFocus","overlay","children","onVisibleChange"];function Ni(e,t){var n,o=e.arrow,r=o===void 0?!1:o,l=e.prefixCls,a=l===void 0?"rc-dropdown":l,s=e.transitionName,c=e.animation,d=e.align,g=e.placement,u=g===void 0?"bottomLeft":g,m=e.placements,b=m===void 0?wi:m,p=e.getPopupContainer,f=e.showAction,v=e.hideAction,x=e.overlayClassName,y=e.overlayStyle,$=e.visible,w=e.trigger,S=w===void 0?["hover"]:w,h=e.autoFocus,E=e.overlay,O=e.children,P=e.onVisibleChange,R=fe(e,Oi),z=le.useState(),C=ie(z,2),N=C[0],M=C[1],k="visible"in e?$:N,G=le.useRef(null),j=le.useRef(null),H=le.useRef(null);le.useImperativeHandle(t,function(){return G.current});var q=function(T){M(T),P==null||P(T)};Si({visible:k,triggerRef:H,onVisibleChange:q,autoFocus:h,overlayRef:j});var V=function(T){var _=e.onOverlayClick;M(!1),_&&_(T)},X=function(){return le.createElement(Ii,{ref:j,overlay:E,prefixCls:a,arrow:r})},F=function(){return typeof E=="function"?X:X()},L=function(){var T=e.minOverlayWidthMatchTrigger,_=e.alignPoint;return"minOverlayWidthMatchTrigger"in e?T:!_},W=function(){var T=e.openClassName;return T!==void 0?T:"".concat(a,"-open")},D=le.cloneElement(O,{className:A((n=O.props)===null||n===void 0?void 0:n.className,k&&W()),ref:Kn(O)?Wn(H,qt(O)):void 0}),Q=v;return!Q&&S.indexOf("contextMenu")!==-1&&(Q=["click"]),le.createElement(to,te({builtinPlacements:b},R,{prefixCls:a,ref:G,popupClassName:A(x,J({},"".concat(a,"-show-arrow"),r)),popupStyle:y,action:S,showAction:f,hideAction:Q,popupPlacement:u,popupAlign:d,popupTransitionName:s,popupAnimation:c,popupVisible:k,stretch:L()?"minWidth":"",popup:F(),onPopupVisibleChange:q,onPopupClick:V,getPopupContainer:p}),D)}const Ei=le.forwardRef(Ni),Pi=e=>typeof e!="object"&&typeof e!="function"||e===null;var io=i.createContext(null);function ao(e,t){return e===void 0?null:"".concat(e,"-").concat(t)}function lo(e){var t=i.useContext(io);return ao(t,e)}var Ri=["children","locked"],Ce=i.createContext(null);function Mi(e,t){var n=re({},e);return Object.keys(t).forEach(function(o){var r=t[o];r!==void 0&&(n[o]=r)}),n}function rt(e){var t=e.children,n=e.locked,o=fe(e,Ri),r=i.useContext(Ce),l=ar(function(){return Mi(r,o)},[r,o],function(a,s){return!n&&(a[0]!==s[0]||!Fn(a[1],s[1],!0))});return i.createElement(Ce.Provider,{value:l},t)}var Bi=[],so=i.createContext(null);function St(){return i.useContext(so)}var co=i.createContext(Bi);function qe(e){var t=i.useContext(co);return i.useMemo(function(){return e!==void 0?[].concat(Te(t),[e]):t},[t,e])}var uo=i.createContext(null),Ut=i.createContext({});function Nn(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(Yr(e)){var n=e.nodeName.toLowerCase(),o=["input","select","textarea","button"].includes(n)||e.isContentEditable||n==="a"&&!!e.getAttribute("href"),r=e.getAttribute("tabindex"),l=Number(r),a=null;return r&&!Number.isNaN(l)?a=l:o&&a===null&&(a=0),o&&e.disabled&&(a=null),a!==null&&(a>=0||t&&a<0)}return!1}function ji(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=Te(e.querySelectorAll("*")).filter(function(o){return Nn(o,t)});return Nn(e,t)&&n.unshift(e),n}var Lt=$e.LEFT,Wt=$e.RIGHT,Kt=$e.UP,gt=$e.DOWN,pt=$e.ENTER,mo=$e.ESC,Je=$e.HOME,et=$e.END,En=[Kt,gt,Lt,Wt];function Ti(e,t,n,o){var r,l="prev",a="next",s="children",c="parent";if(e==="inline"&&o===pt)return{inlineTrigger:!0};var d=J(J({},Kt,l),gt,a),g=J(J(J(J({},Lt,n?a:l),Wt,n?l:a),gt,s),pt,s),u=J(J(J(J(J(J({},Kt,l),gt,a),pt,s),mo,c),Lt,n?s:c),Wt,n?c:s),m={inline:d,horizontal:g,vertical:u,inlineSub:d,horizontalSub:u,verticalSub:u},b=(r=m["".concat(e).concat(t?"":"Sub")])===null||r===void 0?void 0:r[o];switch(b){case l:return{offset:-1,sibling:!0};case a:return{offset:1,sibling:!0};case c:return{offset:-1,sibling:!1};case s:return{offset:1,sibling:!1};default:return null}}function zi(e){for(var t=e;t;){if(t.getAttribute("data-menu-list"))return t;t=t.parentElement}return null}function ki(e,t){for(var n=e||document.activeElement;n;){if(t.has(n))return n;n=n.parentElement}return null}function Yt(e,t){var n=ji(e,!0);return n.filter(function(o){return t.has(o)})}function Pn(e,t,n){var o=arguments.length>3&&arguments[3]!==void 0?arguments[3]:1;if(!e)return null;var r=Yt(e,t),l=r.length,a=r.findIndex(function(s){return n===s});return o<0?a===-1?a=l-1:a-=1:o>0&&(a+=1),a=(a+l)%l,r[a]}var Ft=function(t,n){var o=new Set,r=new Map,l=new Map;return t.forEach(function(a){var s=document.querySelector("[data-menu-id='".concat(ao(n,a),"']"));s&&(o.add(s),l.set(s,a),r.set(a,s))}),{elements:o,key2element:r,element2key:l}};function Hi(e,t,n,o,r,l,a,s,c,d){var g=i.useRef(),u=i.useRef();u.current=t;var m=function(){We.cancel(g.current)};return i.useEffect(function(){return function(){m()}},[]),function(b){var p=b.which;if([].concat(En,[pt,mo,Je,et]).includes(p)){var f=l(),v=Ft(f,o),x=v,y=x.elements,$=x.key2element,w=x.element2key,S=$.get(t),h=ki(S,y),E=w.get(h),O=Ti(e,a(E,!0).length===1,n,p);if(!O&&p!==Je&&p!==et)return;(En.includes(p)||[Je,et].includes(p))&&b.preventDefault();var P=function(j){if(j){var H=j,q=j.querySelector("a");q!=null&&q.getAttribute("href")&&(H=q);var V=w.get(j);s(V),m(),g.current=We(function(){u.current===V&&H.focus()})}};if([Je,et].includes(p)||O.sibling||!h){var R;!h||e==="inline"?R=r.current:R=zi(h);var z,C=Yt(R,y);p===Je?z=C[0]:p===et?z=C[C.length-1]:z=Pn(R,y,h,O.offset),P(z)}else if(O.inlineTrigger)c(E);else if(O.offset>0)c(E,!0),m(),g.current=We(function(){v=Ft(f,o);var G=h.getAttribute("aria-controls"),j=document.getElementById(G),H=Pn(j,v.elements);P(H)},5);else if(O.offset<0){var N=a(E,!0),M=N[N.length-2],k=$.get(M);c(M,!1),P(k)}}d==null||d(b)}}function Di(e){Promise.resolve().then(e)}var Zt="__RC_UTIL_PATH_SPLIT__",Rn=function(t){return t.join(Zt)},Ai=function(t){return t.split(Zt)},Vt="rc-menu-more";function _i(){var e=i.useState({}),t=ie(e,2),n=t[1],o=i.useRef(new Map),r=i.useRef(new Map),l=i.useState([]),a=ie(l,2),s=a[0],c=a[1],d=i.useRef(0),g=i.useRef(!1),u=function(){g.current||n({})},m=i.useCallback(function($,w){var S=Rn(w);r.current.set(S,$),o.current.set($,S),d.current+=1;var h=d.current;Di(function(){h===d.current&&u()})},[]),b=i.useCallback(function($,w){var S=Rn(w);r.current.delete(S),o.current.delete($)},[]),p=i.useCallback(function($){c($)},[]),f=i.useCallback(function($,w){var S=o.current.get($)||"",h=Ai(S);return w&&s.includes(h[0])&&h.unshift(Vt),h},[s]),v=i.useCallback(function($,w){return $.filter(function(S){return S!==void 0}).some(function(S){var h=f(S,!0);return h.includes(w)})},[f]),x=function(){var w=Te(o.current.keys());return s.length&&w.push(Vt),w},y=i.useCallback(function($){var w="".concat(o.current.get($)).concat(Zt),S=new Set;return Te(r.current.keys()).forEach(function(h){h.startsWith(w)&&S.add(r.current.get(h))}),S},[]);return i.useEffect(function(){return function(){g.current=!0}},[]),{registerPath:m,unregisterPath:b,refreshOverflowKeys:p,isSubPathKey:v,getKeyPath:f,getKeys:x,getSubPathKeys:y}}function tt(e){var t=i.useRef(e);t.current=e;var n=i.useCallback(function(){for(var o,r=arguments.length,l=new Array(r),a=0;a<r;a++)l[a]=arguments[a];return(o=t.current)===null||o===void 0?void 0:o.call.apply(o,[t].concat(l))},[]);return e?n:void 0}var Li=Math.random().toFixed(5).toString().slice(2),Mn=0;function Wi(e){var t=nt(e,{value:e}),n=ie(t,2),o=n[0],r=n[1];return i.useEffect(function(){Mn+=1;var l="".concat(Li,"-").concat(Mn);r("rc-menu-uuid-".concat(l))},[]),o}function go(e,t,n,o){var r=i.useContext(Ce),l=r.activeKey,a=r.onActive,s=r.onInactive,c={active:l===e};return t||(c.onMouseEnter=function(d){n==null||n({key:e,domEvent:d}),a(e)},c.onMouseLeave=function(d){o==null||o({key:e,domEvent:d}),s(e)}),c}function po(e){var t=i.useContext(Ce),n=t.mode,o=t.rtl,r=t.inlineIndent;if(n!=="inline")return null;var l=e;return o?{paddingRight:l*r}:{paddingLeft:l*r}}function fo(e){var t=e.icon,n=e.props,o=e.children,r;return t===null||t===!1?null:(typeof t=="function"?r=i.createElement(t,re({},n)):typeof t!="boolean"&&(r=t),r||o||null)}var Ki=["item"];function ht(e){var t=e.item,n=fe(e,Ki);return Object.defineProperty(n,"item",{get:function(){return Vn(!1,"`info.item` is deprecated since we will move to function component that not provides React Node instance in future."),t}}),n}var Fi=["title","attribute","elementRef"],Vi=["style","className","eventKey","warnKey","disabled","itemIcon","children","role","onMouseEnter","onMouseLeave","onClick","onKeyDown","onFocus"],Xi=["active"],qi=function(e){lr(n,e);var t=sr(n);function n(){return cr(this,n),t.apply(this,arguments)}return ur(n,[{key:"render",value:function(){var r=this.props,l=r.title,a=r.attribute,s=r.elementRef,c=fe(r,Fi),d=Oe(c,["eventKey","popupClassName","popupOffset","onTitleClick"]);return Vn(!a,"`attribute` of Menu.Item is deprecated. Please pass attribute directly."),i.createElement(ot.Item,te({},a,{title:typeof l=="string"?l:void 0},d,{ref:s}))}}]),n}(i.Component),Gi=i.forwardRef(function(e,t){var n=e.style,o=e.className,r=e.eventKey;e.warnKey;var l=e.disabled,a=e.itemIcon,s=e.children,c=e.role,d=e.onMouseEnter,g=e.onMouseLeave,u=e.onClick,m=e.onKeyDown,b=e.onFocus,p=fe(e,Vi),f=lo(r),v=i.useContext(Ce),x=v.prefixCls,y=v.onItemClick,$=v.disabled,w=v.overflowDisabled,S=v.itemIcon,h=v.selectedKeys,E=v.onActive,O=i.useContext(Ut),P=O._internalRenderMenuItem,R="".concat(x,"-item"),z=i.useRef(),C=i.useRef(),N=$||l,M=Xn(t,C),k=qe(r),G=function(_){return{key:r,keyPath:Te(k).reverse(),item:z.current,domEvent:_}},j=a||S,H=go(r,N,d,g),q=H.active,V=fe(H,Xi),X=h.includes(r),F=po(k.length),L=function(_){if(!N){var ne=G(_);u==null||u(ht(ne)),y(ne)}},W=function(_){if(m==null||m(_),_.which===$e.ENTER){var ne=G(_);u==null||u(ht(ne)),y(ne)}},D=function(_){E(r),b==null||b(_)},Q={};e.role==="option"&&(Q["aria-selected"]=X);var Y=i.createElement(qi,te({ref:z,elementRef:M,role:c===null?"none":c||"menuitem",tabIndex:l?null:-1,"data-menu-id":w&&f?null:f},Oe(p,["extra"]),V,Q,{component:"li","aria-disabled":l,style:re(re({},F),n),className:A(R,J(J(J({},"".concat(R,"-active"),q),"".concat(R,"-selected"),X),"".concat(R,"-disabled"),N),o),onClick:L,onKeyDown:W,onFocus:D}),s,i.createElement(fo,{props:re(re({},e),{},{isSelected:X}),icon:j}));return P&&(Y=P(Y,e,{selected:X})),Y});function Ui(e,t){var n=e.eventKey,o=St(),r=qe(n);return i.useEffect(function(){if(o)return o.registerPath(n,r),function(){o.unregisterPath(n,r)}},[r]),o?null:i.createElement(Gi,te({},e,{ref:t}))}const It=i.forwardRef(Ui);var Yi=["className","children"],Zi=function(t,n){var o=t.className,r=t.children,l=fe(t,Yi),a=i.useContext(Ce),s=a.prefixCls,c=a.mode,d=a.rtl;return i.createElement("ul",te({className:A(s,d&&"".concat(s,"-rtl"),"".concat(s,"-sub"),"".concat(s,"-").concat(c==="inline"?"inline":"vertical"),o),role:"menu"},l,{"data-menu-list":!0,ref:n}),r)},Qt=i.forwardRef(Zi);Qt.displayName="SubMenuList";function Jt(e,t){return xt(e).map(function(n,o){if(i.isValidElement(n)){var r,l,a=n.key,s=(r=(l=n.props)===null||l===void 0?void 0:l.eventKey)!==null&&r!==void 0?r:a,c=s==null;c&&(s="tmp_key-".concat([].concat(Te(t),[o]).join("-")));var d={key:s,eventKey:s};return i.cloneElement(n,d)}return n})}var ae={adjustX:1,adjustY:1},Qi={topLeft:{points:["bl","tl"],overflow:ae},topRight:{points:["br","tr"],overflow:ae},bottomLeft:{points:["tl","bl"],overflow:ae},bottomRight:{points:["tr","br"],overflow:ae},leftTop:{points:["tr","tl"],overflow:ae},leftBottom:{points:["br","bl"],overflow:ae},rightTop:{points:["tl","tr"],overflow:ae},rightBottom:{points:["bl","br"],overflow:ae}},Ji={topLeft:{points:["bl","tl"],overflow:ae},topRight:{points:["br","tr"],overflow:ae},bottomLeft:{points:["tl","bl"],overflow:ae},bottomRight:{points:["tr","br"],overflow:ae},rightTop:{points:["tr","tl"],overflow:ae},rightBottom:{points:["br","bl"],overflow:ae},leftTop:{points:["tl","tr"],overflow:ae},leftBottom:{points:["bl","br"],overflow:ae}};function vo(e,t,n){if(t)return t;if(n)return n[e]||n.other}var ea={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"};function ta(e){var t=e.prefixCls,n=e.visible,o=e.children,r=e.popup,l=e.popupStyle,a=e.popupClassName,s=e.popupOffset,c=e.disabled,d=e.mode,g=e.onVisibleChange,u=i.useContext(Ce),m=u.getPopupContainer,b=u.rtl,p=u.subMenuOpenDelay,f=u.subMenuCloseDelay,v=u.builtinPlacements,x=u.triggerSubMenuAction,y=u.forceSubMenuRender,$=u.rootClassName,w=u.motion,S=u.defaultMotions,h=i.useState(!1),E=ie(h,2),O=E[0],P=E[1],R=b?re(re({},Ji),v):re(re({},Qi),v),z=ea[d],C=vo(d,w,S),N=i.useRef(C);d!=="inline"&&(N.current=C);var M=re(re({},N.current),{},{leavedClassName:"".concat(t,"-hidden"),removeOnLeave:!1,motionAppear:!0}),k=i.useRef();return i.useEffect(function(){return k.current=We(function(){P(n)}),function(){We.cancel(k.current)}},[n]),i.createElement(to,{prefixCls:t,popupClassName:A("".concat(t,"-popup"),J({},"".concat(t,"-rtl"),b),a,$),stretch:d==="horizontal"?"minWidth":null,getPopupContainer:m,builtinPlacements:R,popupPlacement:z,popupVisible:O,popup:r,popupStyle:l,popupAlign:s&&{offset:s},action:c?[]:[x],mouseEnterDelay:p,mouseLeaveDelay:f,onPopupVisibleChange:g,forceRender:y,popupMotion:M,fresh:!0},o)}function na(e){var t=e.id,n=e.open,o=e.keyPath,r=e.children,l="inline",a=i.useContext(Ce),s=a.prefixCls,c=a.forceSubMenuRender,d=a.motion,g=a.defaultMotions,u=a.mode,m=i.useRef(!1);m.current=u===l;var b=i.useState(!m.current),p=ie(b,2),f=p[0],v=p[1],x=m.current?n:!1;i.useEffect(function(){m.current&&v(!1)},[u]);var y=re({},vo(l,d,g));o.length>1&&(y.motionAppear=!1);var $=y.onVisibleChanged;return y.onVisibleChanged=function(w){return!m.current&&!w&&v(!0),$==null?void 0:$(w)},f?null:i.createElement(rt,{mode:l,locked:!m.current},i.createElement(Ln,te({visible:x},y,{forceRender:c,removeOnLeave:!1,leavedClassName:"".concat(s,"-hidden")}),function(w){var S=w.className,h=w.style;return i.createElement(Qt,{id:t,className:S,style:h},r)}))}var oa=["style","className","title","eventKey","warnKey","disabled","internalPopupClose","children","itemIcon","expandIcon","popupClassName","popupOffset","popupStyle","onClick","onMouseEnter","onMouseLeave","onTitleClick","onTitleMouseEnter","onTitleMouseLeave"],ra=["active"],ia=i.forwardRef(function(e,t){var n=e.style,o=e.className,r=e.title,l=e.eventKey;e.warnKey;var a=e.disabled,s=e.internalPopupClose,c=e.children,d=e.itemIcon,g=e.expandIcon,u=e.popupClassName,m=e.popupOffset,b=e.popupStyle,p=e.onClick,f=e.onMouseEnter,v=e.onMouseLeave,x=e.onTitleClick,y=e.onTitleMouseEnter,$=e.onTitleMouseLeave,w=fe(e,oa),S=lo(l),h=i.useContext(Ce),E=h.prefixCls,O=h.mode,P=h.openKeys,R=h.disabled,z=h.overflowDisabled,C=h.activeKey,N=h.selectedKeys,M=h.itemIcon,k=h.expandIcon,G=h.onItemClick,j=h.onOpenChange,H=h.onActive,q=i.useContext(Ut),V=q._internalRenderSubMenuItem,X=i.useContext(uo),F=X.isSubPathKey,L=qe(),W="".concat(E,"-submenu"),D=R||a,Q=i.useRef(),Y=i.useRef(),T=d??M,_=g??k,ne=P.includes(l),oe=!z&&ne,Ne=F(N,l),ve=go(l,D,y,$),de=ve.active,be=fe(ve,ra),ye=i.useState(!1),K=ie(ye,2),Se=K[0],Ie=K[1],Ee=function(se){D||Ie(se)},Ge=function(se){Ee(!0),f==null||f({key:l,domEvent:se})},Pe=function(se){Ee(!1),v==null||v({key:l,domEvent:se})},me=i.useMemo(function(){return de||(O!=="inline"?Se||F([C],l):!1)},[O,de,C,Se,l,F]),Re=po(L.length),Pt=function(se){D||(x==null||x({key:l,domEvent:se}),O==="inline"&&j(l,!ne))},lt=tt(function(he){p==null||p(ht(he)),G(he)}),st=function(se){O!=="inline"&&j(l,se)},Rt=function(){H(l)},Me=S&&"".concat(S,"-popup"),Ue=i.useMemo(function(){return i.createElement(fo,{icon:O!=="horizontal"?_:void 0,props:re(re({},e),{},{isOpen:oe,isSubMenu:!0})},i.createElement("i",{className:"".concat(W,"-arrow")}))},[O,_,e,oe,W]),ze=i.createElement("div",te({role:"menuitem",style:Re,className:"".concat(W,"-title"),tabIndex:D?null:-1,ref:Q,title:typeof r=="string"?r:null,"data-menu-id":z&&S?null:S,"aria-expanded":oe,"aria-haspopup":!0,"aria-controls":Me,"aria-disabled":D,onClick:Pt,onFocus:Rt},be),r,Ue),Ye=i.useRef(O);if(O!=="inline"&&L.length>1?Ye.current="vertical":Ye.current=O,!z){var De=Ye.current;ze=i.createElement(ta,{mode:De,prefixCls:W,visible:!s&&oe&&O!=="inline",popupClassName:u,popupOffset:m,popupStyle:b,popup:i.createElement(rt,{mode:De==="horizontal"?"vertical":De},i.createElement(Qt,{id:Me,ref:Y},c)),disabled:D,onVisibleChange:st},ze)}var ge=i.createElement(ot.Item,te({ref:t,role:"none"},w,{component:"li",style:n,className:A(W,"".concat(W,"-").concat(O),o,J(J(J(J({},"".concat(W,"-open"),oe),"".concat(W,"-active"),me),"".concat(W,"-selected"),Ne),"".concat(W,"-disabled"),D)),onMouseEnter:Ge,onMouseLeave:Pe}),ze,!z&&i.createElement(na,{id:Me,open:oe,keyPath:L},c));return V&&(ge=V(ge,e,{selected:Ne,active:me,open:oe,disabled:D})),i.createElement(rt,{onItemClick:lt,mode:O==="horizontal"?"vertical":O,itemIcon:T,expandIcon:_},ge)}),wt=i.forwardRef(function(e,t){var n=e.eventKey,o=e.children,r=qe(n),l=Jt(o,r),a=St();i.useEffect(function(){if(a)return a.registerPath(n,r),function(){a.unregisterPath(n,r)}},[r]);var s;return a?s=l:s=i.createElement(ia,te({ref:t},e),l),i.createElement(co.Provider,{value:r},s)});function en(e){var t=e.className,n=e.style,o=i.useContext(Ce),r=o.prefixCls,l=St();return l?null:i.createElement("li",{role:"separator",className:A("".concat(r,"-item-divider"),t),style:n})}var aa=["className","title","eventKey","children"],la=i.forwardRef(function(e,t){var n=e.className,o=e.title;e.eventKey;var r=e.children,l=fe(e,aa),a=i.useContext(Ce),s=a.prefixCls,c="".concat(s,"-item-group");return i.createElement("li",te({ref:t,role:"presentation"},l,{onClick:function(g){return g.stopPropagation()},className:A(c,n)}),i.createElement("div",{role:"presentation",className:"".concat(c,"-title"),title:typeof o=="string"?o:void 0},o),i.createElement("ul",{role:"group",className:"".concat(c,"-list")},r))}),tn=i.forwardRef(function(e,t){var n=e.eventKey,o=e.children,r=qe(n),l=Jt(o,r),a=St();return a?l:i.createElement(la,te({ref:t},Oe(e,["warnKey"])),l)}),sa=["label","children","key","type","extra"];function Xt(e,t,n){var o=t.item,r=t.group,l=t.submenu,a=t.divider;return(e||[]).map(function(s,c){if(s&&dr(s)==="object"){var d=s,g=d.label,u=d.children,m=d.key,b=d.type,p=d.extra,f=fe(d,sa),v=m??"tmp-".concat(c);return u||b==="group"?b==="group"?i.createElement(r,te({key:v},f,{title:g}),Xt(u,t,n)):i.createElement(l,te({key:v},f,{title:g}),Xt(u,t,n)):b==="divider"?i.createElement(a,te({key:v},f)):i.createElement(o,te({key:v},f,{extra:p}),g,(!!p||p===0)&&i.createElement("span",{className:"".concat(n,"-item-extra")},p))}return null}).filter(function(s){return s})}function Bn(e,t,n,o,r){var l=e,a=re({divider:en,item:It,group:tn,submenu:wt},o);return t&&(l=Xt(t,a,r)),Jt(l,n)}var ca=["prefixCls","rootClassName","style","className","tabIndex","items","children","direction","id","mode","inlineCollapsed","disabled","disabledOverflow","subMenuOpenDelay","subMenuCloseDelay","forceSubMenuRender","defaultOpenKeys","openKeys","activeKey","defaultActiveFirst","selectable","multiple","defaultSelectedKeys","selectedKeys","onSelect","onDeselect","inlineIndent","motion","defaultMotions","triggerSubMenuAction","builtinPlacements","itemIcon","expandIcon","overflowedIndicator","overflowedIndicatorPopupClassName","getPopupContainer","onClick","onOpenChange","onKeyDown","openAnimation","openTransitionName","_internalRenderMenuItem","_internalRenderSubMenuItem","_internalComponents"],He=[],ua=i.forwardRef(function(e,t){var n,o=e,r=o.prefixCls,l=r===void 0?"rc-menu":r,a=o.rootClassName,s=o.style,c=o.className,d=o.tabIndex,g=d===void 0?0:d,u=o.items,m=o.children,b=o.direction,p=o.id,f=o.mode,v=f===void 0?"vertical":f,x=o.inlineCollapsed,y=o.disabled,$=o.disabledOverflow,w=o.subMenuOpenDelay,S=w===void 0?.1:w,h=o.subMenuCloseDelay,E=h===void 0?.1:h,O=o.forceSubMenuRender,P=o.defaultOpenKeys,R=o.openKeys,z=o.activeKey,C=o.defaultActiveFirst,N=o.selectable,M=N===void 0?!0:N,k=o.multiple,G=k===void 0?!1:k,j=o.defaultSelectedKeys,H=o.selectedKeys,q=o.onSelect,V=o.onDeselect,X=o.inlineIndent,F=X===void 0?24:X,L=o.motion,W=o.defaultMotions,D=o.triggerSubMenuAction,Q=D===void 0?"hover":D,Y=o.builtinPlacements,T=o.itemIcon,_=o.expandIcon,ne=o.overflowedIndicator,oe=ne===void 0?"...":ne,Ne=o.overflowedIndicatorPopupClassName,ve=o.getPopupContainer,de=o.onClick,be=o.onOpenChange,ye=o.onKeyDown;o.openAnimation,o.openTransitionName;var K=o._internalRenderMenuItem,Se=o._internalRenderSubMenuItem,Ie=o._internalComponents,Ee=fe(o,ca),Ge=i.useMemo(function(){return[Bn(m,u,He,Ie,l),Bn(m,u,He,{},l)]},[m,u,Ie]),Pe=ie(Ge,2),me=Pe[0],Re=Pe[1],Pt=i.useState(!1),lt=ie(Pt,2),st=lt[0],Rt=lt[1],Me=i.useRef(),Ue=Wi(p),ze=b==="rtl",Ye=nt(P,{value:R,postState:function(U){return U||He}}),De=ie(Ye,2),ge=De[0],he=De[1],se=function(U){var Z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;function ce(){he(U),be==null||be(U)}Z?mr.flushSync(ce):ce()},Ro=i.useState(ge),rn=ie(Ro,2),Mo=rn[0],Bo=rn[1],Mt=i.useRef(!1),jo=i.useMemo(function(){return(v==="inline"||v==="vertical")&&x?["vertical",x]:[v,!1]},[v,x]),an=ie(jo,2),ct=an[0],Bt=an[1],ln=ct==="inline",To=i.useState(ct),sn=ie(To,2),Be=sn[0],zo=sn[1],ko=i.useState(Bt),cn=ie(ko,2),Ho=cn[0],Do=cn[1];i.useEffect(function(){zo(ct),Do(Bt),Mt.current&&(ln?he(Mo):se(He))},[ct,Bt]);var Ao=i.useState(0),un=ie(Ao,2),ut=un[0],_o=un[1],jt=ut>=me.length-1||Be!=="horizontal"||$;i.useEffect(function(){ln&&Bo(ge)},[ge]),i.useEffect(function(){return Mt.current=!0,function(){Mt.current=!1}},[]);var ke=_i(),dn=ke.registerPath,mn=ke.unregisterPath,Lo=ke.refreshOverflowKeys,gn=ke.isSubPathKey,Wo=ke.getKeyPath,pn=ke.getKeys,Ko=ke.getSubPathKeys,Fo=i.useMemo(function(){return{registerPath:dn,unregisterPath:mn}},[dn,mn]),Vo=i.useMemo(function(){return{isSubPathKey:gn}},[gn]);i.useEffect(function(){Lo(jt?He:me.slice(ut+1).map(function(ee){return ee.key}))},[ut,jt]);var Xo=nt(z||C&&((n=me[0])===null||n===void 0?void 0:n.key),{value:z}),fn=ie(Xo,2),Ze=fn[0],Tt=fn[1],qo=tt(function(ee){Tt(ee)}),Go=tt(function(){Tt(void 0)});i.useImperativeHandle(t,function(){return{list:Me.current,focus:function(U){var Z,ce=pn(),ue=Ft(ce,Ue),mt=ue.elements,zt=ue.key2element,rr=ue.element2key,hn=Yt(Me.current,mt),Cn=Ze??(hn[0]?rr.get(hn[0]):(Z=me.find(function(ir){return!ir.props.disabled}))===null||Z===void 0?void 0:Z.key),Qe=zt.get(Cn);if(Cn&&Qe){var kt;Qe==null||(kt=Qe.focus)===null||kt===void 0||kt.call(Qe,U)}}}});var Uo=nt(j||[],{value:H,postState:function(U){return Array.isArray(U)?U:U==null?He:[U]}}),vn=ie(Uo,2),dt=vn[0],Yo=vn[1],Zo=function(U){if(M){var Z=U.key,ce=dt.includes(Z),ue;G?ce?ue=dt.filter(function(zt){return zt!==Z}):ue=[].concat(Te(dt),[Z]):ue=[Z],Yo(ue);var mt=re(re({},U),{},{selectedKeys:ue});ce?V==null||V(mt):q==null||q(mt)}!G&&ge.length&&Be!=="inline"&&se(He)},Qo=tt(function(ee){de==null||de(ht(ee)),Zo(ee)}),bn=tt(function(ee,U){var Z=ge.filter(function(ue){return ue!==ee});if(U)Z.push(ee);else if(Be!=="inline"){var ce=Ko(ee);Z=Z.filter(function(ue){return!ce.has(ue)})}Fn(ge,Z,!0)||se(Z,!0)}),Jo=function(U,Z){var ce=Z??!ge.includes(U);bn(U,ce)},er=Hi(Be,Ze,ze,Ue,Me,pn,Wo,Tt,Jo,ye);i.useEffect(function(){Rt(!0)},[]);var tr=i.useMemo(function(){return{_internalRenderMenuItem:K,_internalRenderSubMenuItem:Se}},[K,Se]),nr=Be!=="horizontal"||$?me:me.map(function(ee,U){return i.createElement(rt,{key:ee.key,overflowDisabled:U>ut},ee)}),or=i.createElement(ot,te({id:p,ref:Me,prefixCls:"".concat(l,"-overflow"),component:"ul",itemComponent:It,className:A(l,"".concat(l,"-root"),"".concat(l,"-").concat(Be),c,J(J({},"".concat(l,"-inline-collapsed"),Ho),"".concat(l,"-rtl"),ze),a),dir:b,style:s,role:"menu",tabIndex:g,data:nr,renderRawItem:function(U){return U},renderRawRest:function(U){var Z=U.length,ce=Z?me.slice(-Z):null;return i.createElement(wt,{eventKey:Vt,title:oe,disabled:jt,internalPopupClose:Z===0,popupClassName:Ne},ce)},maxCount:Be!=="horizontal"||$?ot.INVALIDATE:ot.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:function(U){_o(U)},onKeyDown:er},Ee));return i.createElement(Ut.Provider,{value:tr},i.createElement(io.Provider,{value:Ue},i.createElement(rt,{prefixCls:l,rootClassName:a,mode:Be,openKeys:ge,rtl:ze,disabled:y,motion:st?L:null,defaultMotions:st?W:null,activeKey:Ze,onActive:qo,onInactive:Go,selectedKeys:dt,inlineIndent:F,subMenuOpenDelay:S,subMenuCloseDelay:E,forceSubMenuRender:O,builtinPlacements:Y,triggerSubMenuAction:Q,getPopupContainer:ve,itemIcon:T,expandIcon:_,onItemClick:Qo,onOpenChange:bn},i.createElement(uo.Provider,{value:Vo},or),i.createElement("div",{style:{display:"none"},"aria-hidden":!0},i.createElement(so.Provider,{value:Fo},Re)))))}),it=ua;it.Item=It;it.SubMenu=wt;it.ItemGroup=tn;it.Divider=en;var da={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"},ma=function(t,n){return i.createElement(Ke,te({},t,{ref:n,icon:da}))},ga=i.forwardRef(ma);const bo=i.createContext({siderHook:{addSider:()=>null,removeSider:()=>null}}),pa=e=>{const{antCls:t,componentCls:n,colorText:o,footerBg:r,headerHeight:l,headerPadding:a,headerColor:s,footerPadding:c,fontSize:d,bodyBg:g,headerBg:u}=e;return{[n]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:g,"&, *":{boxSizing:"border-box"},[`&${n}-has-sider`]:{flexDirection:"row",[`> ${n}, > ${n}-content`]:{width:0}},[`${n}-header, &${n}-footer`]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},[`${n}-header`]:{height:l,padding:a,color:s,lineHeight:B(l),background:u,[`${t}-menu`]:{lineHeight:"inherit"}},[`${n}-footer`]:{padding:c,color:o,fontSize:d,background:r},[`${n}-content`]:{flex:"auto",color:o,minHeight:0}}},ho=e=>{const{colorBgLayout:t,controlHeight:n,controlHeightLG:o,colorText:r,controlHeightSM:l,marginXXS:a,colorTextLightSolid:s,colorBgContainer:c}=e,d=o*1.25;return{colorBgHeader:"#001529",colorBgBody:t,colorBgTrigger:"#002140",bodyBg:t,headerBg:"#001529",headerHeight:n*2,headerPadding:`0 ${d}px`,headerColor:r,footerPadding:`${l}px ${d}px`,footerBg:t,siderBg:"#001529",triggerHeight:o+a*2,triggerBg:"#002140",triggerColor:s,zeroTriggerWidth:o,zeroTriggerHeight:o,lightSiderBg:c,lightTriggerBg:c,lightTriggerColor:r}},Co=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]],$o=Fe("Layout",e=>[pa(e)],ho,{deprecatedTokens:Co}),fa=e=>{const{componentCls:t,siderBg:n,motionDurationMid:o,motionDurationSlow:r,antCls:l,triggerHeight:a,triggerColor:s,triggerBg:c,headerHeight:d,zeroTriggerWidth:g,zeroTriggerHeight:u,borderRadiusLG:m,lightSiderBg:b,lightTriggerColor:p,lightTriggerBg:f,bodyBg:v}=e;return{[t]:{position:"relative",minWidth:0,background:n,transition:`all ${o}, background 0s`,"&-has-trigger":{paddingBottom:a},"&-right":{order:1},[`${t}-children`]:{height:"100%",marginTop:-.1,paddingTop:.1,[`${l}-menu${l}-menu-inline-collapsed`]:{width:"auto"}},[`&-zero-width ${t}-children`]:{overflow:"hidden"},[`${t}-trigger`]:{position:"fixed",bottom:0,zIndex:1,height:a,color:s,lineHeight:B(a),textAlign:"center",background:c,cursor:"pointer",transition:`all ${o}`},[`${t}-zero-width-trigger`]:{position:"absolute",top:d,insetInlineEnd:e.calc(g).mul(-1).equal(),zIndex:1,width:g,height:u,color:s,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:n,borderRadius:`0 ${B(m)} ${B(m)} 0`,cursor:"pointer",transition:`background ${r} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${r}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(g).mul(-1).equal(),borderRadius:`${B(m)} 0 0 ${B(m)}`}},"&-light":{background:b,[`${t}-trigger`]:{color:p,background:f},[`${t}-zero-width-trigger`]:{color:p,background:f,border:`1px solid ${v}`,borderInlineStart:0}}}}},va=Fe(["Layout","Sider"],e=>[fa(e)],ho,{deprecatedTokens:Co});var ba=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const jn={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},ha=e=>!Number.isNaN(Number.parseFloat(e))&&isFinite(e),Ot=i.createContext({}),Ca=(()=>{let e=0;return function(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return e+=1,`${t}${e}`}})(),yo=i.forwardRef((e,t)=>{const{prefixCls:n,className:o,trigger:r,children:l,defaultCollapsed:a=!1,theme:s="dark",style:c={},collapsible:d=!1,reverseArrow:g=!1,width:u=200,collapsedWidth:m=80,zeroWidthTriggerStyle:b,breakpoint:p,onCollapse:f,onBreakpoint:v}=e,x=ba(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:y}=i.useContext(bo),[$,w]=i.useState("collapsed"in e?e.collapsed:a),[S,h]=i.useState(!1);i.useEffect(()=>{"collapsed"in e&&w(e.collapsed)},[e.collapsed]);const E=(T,_)=>{"collapsed"in e||w(T),f==null||f(T,_)},{getPrefixCls:O,direction:P}=i.useContext(xe),R=O("layout-sider",n),[z,C,N]=va(R),M=i.useRef(null);M.current=T=>{h(T.matches),v==null||v(T.matches),$!==T.matches&&E(T.matches,"responsive")},i.useEffect(()=>{function T(ne){var oe;return(oe=M.current)===null||oe===void 0?void 0:oe.call(M,ne)}let _;return typeof(window==null?void 0:window.matchMedia)<"u"&&p&&p in jn&&(_=window.matchMedia(`screen and (max-width: ${jn[p]})`),Tr(_,T),T(_)),()=>{zr(_,T)}},[p]),i.useEffect(()=>{const T=Ca("ant-sider-");return y.addSider(T),()=>y.removeSider(T)},[]);const k=()=>{E(!$,"clickTrigger")},G=Oe(x,["collapsed"]),j=$?m:u,H=ha(j)?`${j}px`:String(j),q=parseFloat(String(m||0))===0?i.createElement("span",{onClick:k,className:A(`${R}-zero-width-trigger`,`${R}-zero-width-trigger-${g?"right":"left"}`),style:b},r||i.createElement(ga,null)):null,V=P==="rtl"==!g,L={expanded:V?i.createElement(Dt,null):i.createElement(_t,null),collapsed:V?i.createElement(_t,null):i.createElement(Dt,null)}[$?"collapsed":"expanded"],W=r!==null?q||i.createElement("div",{className:`${R}-trigger`,onClick:k,style:{width:H}},r||L):null,D=Object.assign(Object.assign({},c),{flex:`0 0 ${H}`,maxWidth:H,minWidth:H,width:H}),Q=A(R,`${R}-${s}`,{[`${R}-collapsed`]:!!$,[`${R}-has-trigger`]:d&&r!==null&&!q,[`${R}-below`]:!!S,[`${R}-zero-width`]:parseFloat(H)===0},o,C,N),Y=i.useMemo(()=>({siderCollapsed:$}),[$]);return z(i.createElement(Ot.Provider,{value:Y},i.createElement("aside",Object.assign({className:Q},G,{style:D,ref:t}),i.createElement("div",{className:`${R}-children`},l),d||S&&q?W:null)))});var $a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"},ya=function(t,n){return i.createElement(Ke,te({},t,{ref:n,icon:$a}))},xo=i.forwardRef(ya);const Ct=i.createContext({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});var xa=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const So=e=>{const{prefixCls:t,className:n,dashed:o}=e,r=xa(e,["prefixCls","className","dashed"]),{getPrefixCls:l}=i.useContext(xe),a=l("menu",t),s=A({[`${a}-item-divider-dashed`]:!!o},n);return i.createElement(en,Object.assign({className:s},r))},Io=e=>{var t;const{className:n,children:o,icon:r,title:l,danger:a,extra:s}=e,{prefixCls:c,firstLevel:d,direction:g,disableMenuItemTitleTooltip:u,inlineCollapsed:m}=i.useContext(Ct),b=$=>{const w=o==null?void 0:o[0],S=i.createElement("span",{className:A(`${c}-title-content`,{[`${c}-title-content-with-extra`]:!!s||s===0})},o);return(!r||i.isValidElement(o)&&o.type==="span")&&o&&$&&d&&typeof w=="string"?i.createElement("div",{className:`${c}-inline-collapsed-noicon`},w.charAt(0)):S},{siderCollapsed:p}=i.useContext(Ot);let f=l;typeof l>"u"?f=d?o:"":l===!1&&(f="");const v={title:f};!p&&!m&&(v.title=null,v.open=!1);const x=xt(o).length;let y=i.createElement(It,Object.assign({},Oe(e,["title","icon","danger"]),{className:A({[`${c}-item-danger`]:a,[`${c}-item-only-child`]:(r?x+1:x)===1},n),title:typeof l=="string"?l:void 0}),Xe(r,{className:A(i.isValidElement(r)?(t=r.props)===null||t===void 0?void 0:t.className:"",`${c}-item-icon`)}),b(m));return u||(y=i.createElement(kr,Object.assign({},v,{placement:g==="rtl"?"left":"right",classNames:{root:`${c}-inline-collapsed-tooltip`}}),y)),y};var Sa=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const $t=i.createContext(null),Ia=i.forwardRef((e,t)=>{const{children:n}=e,o=Sa(e,["children"]),r=i.useContext($t),l=i.useMemo(()=>Object.assign(Object.assign({},r),o),[r,o.prefixCls,o.mode,o.selectable,o.rootClassName]),a=gr(n),s=Xn(t,a?qt(n):null);return i.createElement($t.Provider,{value:l},i.createElement(Hr,{space:!0},a?i.cloneElement(n,{ref:s}):n))}),wa=e=>{const{componentCls:t,motionDurationSlow:n,horizontalLineHeight:o,colorSplit:r,lineWidth:l,lineType:a,itemPaddingInline:s}=e;return{[`${t}-horizontal`]:{lineHeight:o,border:0,borderBottom:`${B(l)} ${a} ${r}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${t}-item, ${t}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:s},[`> ${t}-item:hover,
        > ${t}-item-active,
        > ${t}-submenu ${t}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${t}-item, ${t}-submenu-title`]:{transition:[`border-color ${n}`,`background ${n}`].join(",")},[`${t}-submenu-arrow`]:{display:"none"}}}},Oa=e=>{let{componentCls:t,menuArrowOffset:n,calc:o}=e;return{[`${t}-rtl`]:{direction:"rtl"},[`${t}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${t}-rtl${t}-vertical,
    ${t}-submenu-rtl ${t}-vertical`]:{[`${t}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(${B(o(n).mul(-1).equal())})`},"&::after":{transform:`rotate(45deg) translateY(${B(n)})`}}}}},Tn=e=>Object.assign({},pr(e)),zn=(e,t)=>{const{componentCls:n,itemColor:o,itemSelectedColor:r,subMenuItemSelectedColor:l,groupTitleColor:a,itemBg:s,subMenuItemBg:c,itemSelectedBg:d,activeBarHeight:g,activeBarWidth:u,activeBarBorderWidth:m,motionDurationSlow:b,motionEaseInOut:p,motionEaseOut:f,itemPaddingInline:v,motionDurationMid:x,itemHoverColor:y,lineType:$,colorSplit:w,itemDisabledColor:S,dangerItemColor:h,dangerItemHoverColor:E,dangerItemSelectedColor:O,dangerItemActiveBg:P,dangerItemSelectedBg:R,popupBg:z,itemHoverBg:C,itemActiveBg:N,menuSubMenuBg:M,horizontalItemSelectedColor:k,horizontalItemSelectedBg:G,horizontalItemBorderRadius:j,horizontalItemHoverBg:H}=e;return{[`${n}-${t}, ${n}-${t} > ${n}`]:{color:o,background:s,[`&${n}-root:focus-visible`]:Object.assign({},Tn(e)),[`${n}-item`]:{"&-group-title, &-extra":{color:a}},[`${n}-submenu-selected > ${n}-submenu-title`]:{color:l},[`${n}-item, ${n}-submenu-title`]:{color:o,[`&:not(${n}-item-disabled):focus-visible`]:Object.assign({},Tn(e))},[`${n}-item-disabled, ${n}-submenu-disabled`]:{color:`${S} !important`},[`${n}-item:not(${n}-item-selected):not(${n}-submenu-selected)`]:{[`&:hover, > ${n}-submenu-title:hover`]:{color:y}},[`&:not(${n}-horizontal)`]:{[`${n}-item:not(${n}-item-selected)`]:{"&:hover":{backgroundColor:C},"&:active":{backgroundColor:N}},[`${n}-submenu-title`]:{"&:hover":{backgroundColor:C},"&:active":{backgroundColor:N}}},[`${n}-item-danger`]:{color:h,[`&${n}-item:hover`]:{[`&:not(${n}-item-selected):not(${n}-submenu-selected)`]:{color:E}},[`&${n}-item:active`]:{background:P}},[`${n}-item a`]:{"&, &:hover":{color:"inherit"}},[`${n}-item-selected`]:{color:r,[`&${n}-item-danger`]:{color:O},"a, a:hover":{color:"inherit"}},[`& ${n}-item-selected`]:{backgroundColor:d,[`&${n}-item-danger`]:{backgroundColor:R}},[`&${n}-submenu > ${n}`]:{backgroundColor:M},[`&${n}-popup > ${n}`]:{backgroundColor:z},[`&${n}-submenu-popup > ${n}`]:{backgroundColor:z},[`&${n}-horizontal`]:Object.assign(Object.assign({},t==="dark"?{borderBottom:0}:{}),{[`> ${n}-item, > ${n}-submenu`]:{top:m,marginTop:e.calc(m).mul(-1).equal(),marginBottom:0,borderRadius:j,"&::after":{position:"absolute",insetInline:v,bottom:0,borderBottom:`${B(g)} solid transparent`,transition:`border-color ${b} ${p}`,content:'""'},"&:hover, &-active, &-open":{background:H,"&::after":{borderBottomWidth:g,borderBottomColor:k}},"&-selected":{color:k,backgroundColor:G,"&:hover":{backgroundColor:G},"&::after":{borderBottomWidth:g,borderBottomColor:k}}}}),[`&${n}-root`]:{[`&${n}-inline, &${n}-vertical`]:{borderInlineEnd:`${B(m)} ${$} ${w}`}},[`&${n}-inline`]:{[`${n}-sub${n}-inline`]:{background:c},[`${n}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${B(u)} solid ${r}`,transform:"scaleY(0.0001)",opacity:0,transition:[`transform ${x} ${f}`,`opacity ${x} ${f}`].join(","),content:'""'},[`&${n}-item-danger`]:{"&::after":{borderInlineEndColor:O}}},[`${n}-selected, ${n}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:[`transform ${x} ${p}`,`opacity ${x} ${p}`].join(",")}}}}}},kn=e=>{const{componentCls:t,itemHeight:n,itemMarginInline:o,padding:r,menuArrowSize:l,marginXS:a,itemMarginBlock:s,itemWidth:c,itemPaddingInline:d}=e,g=e.calc(l).add(r).add(a).equal();return{[`${t}-item`]:{position:"relative",overflow:"hidden"},[`${t}-item, ${t}-submenu-title`]:{height:n,lineHeight:B(n),paddingInline:d,overflow:"hidden",textOverflow:"ellipsis",marginInline:o,marginBlock:s,width:c},[`> ${t}-item,
            > ${t}-submenu > ${t}-submenu-title`]:{height:n,lineHeight:B(n)},[`${t}-item-group-list ${t}-submenu-title,
            ${t}-submenu-title`]:{paddingInlineEnd:g}}},Na=e=>{const{componentCls:t,iconCls:n,itemHeight:o,colorTextLightSolid:r,dropdownWidth:l,controlHeightLG:a,motionEaseOut:s,paddingXL:c,itemMarginInline:d,fontSizeLG:g,motionDurationFast:u,motionDurationSlow:m,paddingXS:b,boxShadowSecondary:p,collapsedWidth:f,collapsedIconSize:v}=e,x={height:o,lineHeight:B(o),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({[`&${t}-root`]:{boxShadow:"none"}},kn(e))},[`${t}-submenu-popup`]:{[`${t}-vertical`]:Object.assign(Object.assign({},kn(e)),{boxShadow:p})}},{[`${t}-submenu-popup ${t}-vertical${t}-sub`]:{minWidth:l,maxHeight:`calc(100vh - ${B(e.calc(a).mul(2.5).equal())})`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${t}-inline`]:{width:"100%",[`&${t}-root`]:{[`${t}-item, ${t}-submenu-title`]:{display:"flex",alignItems:"center",transition:[`border-color ${m}`,`background ${m}`,`padding ${u} ${s}`].join(","),[`> ${t}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${t}-sub${t}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${t}-submenu > ${t}-submenu-title`]:x,[`& ${t}-item-group-title`]:{paddingInlineStart:c}},[`${t}-item`]:x}},{[`${t}-inline-collapsed`]:{width:f,[`&${t}-root`]:{[`${t}-item, ${t}-submenu ${t}-submenu-title`]:{[`> ${t}-inline-collapsed-noicon`]:{fontSize:g,textAlign:"center"}}},[`> ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-submenu > ${t}-submenu-title,
          > ${t}-submenu > ${t}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${B(e.calc(v).div(2).equal())} - ${B(d)})`,textOverflow:"clip",[`
            ${t}-submenu-arrow,
            ${t}-submenu-expand-icon
          `]:{opacity:0},[`${t}-item-icon, ${n}`]:{margin:0,fontSize:v,lineHeight:B(o),"+ span":{display:"inline-block",opacity:0}}},[`${t}-item-icon, ${n}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${t}-item-icon, ${n}`]:{display:"none"},"a, a:hover":{color:r}},[`${t}-item-group-title`]:Object.assign(Object.assign({},fr),{paddingInline:b})}}]},Hn=e=>{const{componentCls:t,motionDurationSlow:n,motionDurationMid:o,motionEaseInOut:r,motionEaseOut:l,iconCls:a,iconSize:s,iconMarginInlineEnd:c}=e;return{[`${t}-item, ${t}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:[`border-color ${n}`,`background ${n}`,`padding calc(${n} + 0.1s) ${r}`].join(","),[`${t}-item-icon, ${a}`]:{minWidth:s,fontSize:s,transition:[`font-size ${o} ${l}`,`margin ${n} ${r}`,`color ${n}`].join(","),"+ span":{marginInlineStart:c,opacity:1,transition:[`opacity ${n} ${r}`,`margin ${n}`,`color ${n}`].join(",")}},[`${t}-item-icon`]:Object.assign({},vr()),[`&${t}-item-only-child`]:{[`> ${a}, > ${t}-item-icon`]:{marginInlineEnd:0}}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},[`> ${t}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},Dn=e=>{const{componentCls:t,motionDurationSlow:n,motionEaseInOut:o,borderRadius:r,menuArrowSize:l,menuArrowOffset:a}=e;return{[`${t}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:l,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${n} ${o}, opacity ${n}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(l).mul(.6).equal(),height:e.calc(l).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:r,transition:[`background ${n} ${o}`,`transform ${n} ${o}`,`top ${n} ${o}`,`color ${n} ${o}`].join(","),content:'""'},"&::before":{transform:`rotate(45deg) translateY(${B(e.calc(a).mul(-1).equal())})`},"&::after":{transform:`rotate(-45deg) translateY(${B(a)})`}}}}},Ea=e=>{const{antCls:t,componentCls:n,fontSize:o,motionDurationSlow:r,motionDurationMid:l,motionEaseInOut:a,paddingXS:s,padding:c,colorSplit:d,lineWidth:g,zIndexPopup:u,borderRadiusLG:m,subMenuItemBorderRadius:b,menuArrowSize:p,menuArrowOffset:f,lineType:v,groupTitleLineHeight:x,groupTitleFontSize:y}=e;return[{"":{[n]:Object.assign(Object.assign({},$n()),{"&-hidden":{display:"none"}})},[`${n}-submenu-hidden`]:{display:"none"}},{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},yt(e)),$n()),{marginBottom:0,paddingInlineStart:0,fontSize:o,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${r} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${n}-item`]:{flex:"none"}},[`${n}-item, ${n}-submenu, ${n}-submenu-title`]:{borderRadius:e.itemBorderRadius},[`${n}-item-group-title`]:{padding:`${B(s)} ${B(c)}`,fontSize:y,lineHeight:x,transition:`all ${r}`},[`&-horizontal ${n}-submenu`]:{transition:[`border-color ${r} ${a}`,`background ${r} ${a}`].join(",")},[`${n}-submenu, ${n}-submenu-inline`]:{transition:[`border-color ${r} ${a}`,`background ${r} ${a}`,`padding ${l} ${a}`].join(",")},[`${n}-submenu ${n}-sub`]:{cursor:"initial",transition:[`background ${r} ${a}`,`padding ${r} ${a}`].join(",")},[`${n}-title-content`]:{transition:`color ${r}`,"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},[`> ${t}-typography-ellipsis-single-line`]:{display:"inline",verticalAlign:"unset"},[`${n}-item-extra`]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},[`${n}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${n}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:d,borderStyle:v,borderWidth:0,borderTopWidth:g,marginBlock:g,padding:0,"&-dashed":{borderStyle:"dashed"}}}),Hn(e)),{[`${n}-item-group`]:{[`${n}-item-group-list`]:{margin:0,padding:0,[`${n}-item, ${n}-submenu-title`]:{paddingInline:`${B(e.calc(o).mul(2).equal())} ${B(c)}`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:u,borderRadius:m,boxShadow:"none",transformOrigin:"0 0",[`&${n}-submenu`]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},[`> ${n}`]:Object.assign(Object.assign(Object.assign({borderRadius:m},Hn(e)),Dn(e)),{[`${n}-item, ${n}-submenu > ${n}-submenu-title`]:{borderRadius:b},[`${n}-submenu-title::after`]:{transition:`transform ${r} ${a}`}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),Dn(e)),{[`&-inline-collapsed ${n}-submenu-arrow,
        &-inline ${n}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${B(f)})`},"&::after":{transform:`rotate(45deg) translateX(${B(e.calc(f).mul(-1).equal())})`}},[`${n}-submenu-open${n}-submenu-inline > ${n}-submenu-title > ${n}-submenu-arrow`]:{transform:`translateY(${B(e.calc(p).mul(.2).mul(-1).equal())})`,"&::after":{transform:`rotate(-45deg) translateX(${B(e.calc(f).mul(-1).equal())})`},"&::before":{transform:`rotate(45deg) translateX(${B(f)})`}}})},{[`${t}-layout-header`]:{[n]:{lineHeight:"inherit"}}}]},Pa=e=>{var t,n,o;const{colorPrimary:r,colorError:l,colorTextDisabled:a,colorErrorBg:s,colorText:c,colorTextDescription:d,colorBgContainer:g,colorFillAlter:u,colorFillContent:m,lineWidth:b,lineWidthBold:p,controlItemBgActive:f,colorBgTextHover:v,controlHeightLG:x,lineHeight:y,colorBgElevated:$,marginXXS:w,padding:S,fontSize:h,controlHeightSM:E,fontSizeLG:O,colorTextLightSolid:P,colorErrorHover:R}=e,z=(t=e.activeBarWidth)!==null&&t!==void 0?t:0,C=(n=e.activeBarBorderWidth)!==null&&n!==void 0?n:b,N=(o=e.itemMarginInline)!==null&&o!==void 0?o:e.marginXXS,M=new vt(P).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:c,itemColor:c,colorItemTextHover:c,itemHoverColor:c,colorItemTextHoverHorizontal:r,horizontalItemHoverColor:r,colorGroupTitle:d,groupTitleColor:d,colorItemTextSelected:r,itemSelectedColor:r,subMenuItemSelectedColor:r,colorItemTextSelectedHorizontal:r,horizontalItemSelectedColor:r,colorItemBg:g,itemBg:g,colorItemBgHover:v,itemHoverBg:v,colorItemBgActive:m,itemActiveBg:f,colorSubItemBg:u,subMenuItemBg:u,colorItemBgSelected:f,itemSelectedBg:f,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:z,colorActiveBarHeight:p,activeBarHeight:p,colorActiveBarBorderSize:b,activeBarBorderWidth:C,colorItemTextDisabled:a,itemDisabledColor:a,colorDangerItemText:l,dangerItemColor:l,colorDangerItemTextHover:l,dangerItemHoverColor:l,colorDangerItemTextSelected:l,dangerItemSelectedColor:l,colorDangerItemBgActive:s,dangerItemActiveBg:s,colorDangerItemBgSelected:s,dangerItemSelectedBg:s,itemMarginInline:N,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:x,groupTitleLineHeight:y,collapsedWidth:x*2,popupBg:$,itemMarginBlock:w,itemPaddingInline:S,horizontalLineHeight:`${x*1.15}px`,iconSize:h,iconMarginInlineEnd:E-h,collapsedIconSize:O,groupTitleFontSize:h,darkItemDisabledColor:new vt(P).setA(.25).toRgbString(),darkItemColor:M,darkDangerItemColor:l,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:P,darkItemSelectedBg:r,darkDangerItemSelectedBg:l,darkItemHoverBg:"transparent",darkGroupTitleColor:M,darkItemHoverColor:P,darkDangerItemHoverColor:R,darkDangerItemSelectedColor:P,darkDangerItemActiveBg:l,itemWidth:z?`calc(100% + ${C}px)`:`calc(100% - ${N*2}px)`}},Ra=function(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return Fe("Menu",r=>{const{colorBgElevated:l,controlHeightLG:a,fontSize:s,darkItemColor:c,darkDangerItemColor:d,darkItemBg:g,darkSubMenuItemBg:u,darkItemSelectedColor:m,darkItemSelectedBg:b,darkDangerItemSelectedBg:p,darkItemHoverBg:f,darkGroupTitleColor:v,darkItemHoverColor:x,darkItemDisabledColor:y,darkDangerItemHoverColor:$,darkDangerItemSelectedColor:w,darkDangerItemActiveBg:S,popupBg:h,darkPopupBg:E}=r,O=r.calc(s).div(7).mul(5).equal(),P=ft(r,{menuArrowSize:O,menuHorizontalHeight:r.calc(a).mul(1.15).equal(),menuArrowOffset:r.calc(O).mul(.25).equal(),menuSubMenuBg:l,calc:r.calc,popupBg:h}),R=ft(P,{itemColor:c,itemHoverColor:x,groupTitleColor:v,itemSelectedColor:m,subMenuItemSelectedColor:m,itemBg:g,popupBg:E,subMenuItemBg:u,itemActiveBg:"transparent",itemSelectedBg:b,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:f,itemDisabledColor:y,dangerItemColor:d,dangerItemHoverColor:$,dangerItemSelectedColor:w,dangerItemActiveBg:S,dangerItemSelectedBg:p,menuSubMenuBg:u,horizontalItemSelectedColor:m,horizontalItemSelectedBg:b});return[Ea(P),wa(P),Na(P),zn(P,"light"),zn(R,"dark"),Oa(P),Dr(P),bt(P,"slide-up"),bt(P,"slide-down"),no(P,"zoom-big")]},Pa,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:n,unitless:{groupTitleLineHeight:!0}})(e,t)},wo=e=>{var t;const{popupClassName:n,icon:o,title:r,theme:l}=e,a=i.useContext(Ct),{prefixCls:s,inlineCollapsed:c,theme:d}=a,g=qe();let u;if(!o)u=c&&!g.length&&r&&typeof r=="string"?i.createElement("div",{className:`${s}-inline-collapsed-noicon`},r.charAt(0)):i.createElement("span",{className:`${s}-title-content`},r);else{const p=i.isValidElement(r)&&r.type==="span";u=i.createElement(i.Fragment,null,Xe(o,{className:A(i.isValidElement(o)?(t=o.props)===null||t===void 0?void 0:t.className:"",`${s}-item-icon`)}),p?r:i.createElement("span",{className:`${s}-title-content`},r))}const m=i.useMemo(()=>Object.assign(Object.assign({},a),{firstLevel:!1}),[a]),[b]=qn("Menu");return i.createElement(Ct.Provider,{value:m},i.createElement(wt,Object.assign({},Oe(e,["icon"]),{title:u,popupClassName:A(s,n,`${s}-${l||d}`),popupStyle:Object.assign({zIndex:b},e.popupStyle)})))};var Ma=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};function Ht(e){return e===null||e===!1}const Ba={item:Io,submenu:wo,divider:So},ja=i.forwardRef((e,t)=>{var n;const o=i.useContext($t),r=o||{},{getPrefixCls:l,getPopupContainer:a,direction:s,menu:c}=i.useContext(xe),d=l(),{prefixCls:g,className:u,style:m,theme:b="light",expandIcon:p,_internalDisableMenuItemTitleTooltip:f,inlineCollapsed:v,siderCollapsed:x,rootClassName:y,mode:$,selectable:w,onClick:S,overflowedIndicatorPopupClassName:h}=e,E=Ma(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),O=Oe(E,["collapsedWidth"]);(n=r.validator)===null||n===void 0||n.call(r,{mode:$});const P=Gn(function(){var F;S==null||S.apply(void 0,arguments),(F=r.onClick)===null||F===void 0||F.call(r)}),R=r.mode||$,z=w??r.selectable,C=v??x,N={horizontal:{motionName:`${d}-slide-up`},inline:Ar(d),other:{motionName:`${d}-zoom-big`}},M=l("menu",g||r.prefixCls),k=Un(M),[G,j,H]=Ra(M,k,!o),q=A(`${M}-${b}`,c==null?void 0:c.className,u),V=i.useMemo(()=>{var F,L;if(typeof p=="function"||Ht(p))return p||null;if(typeof r.expandIcon=="function"||Ht(r.expandIcon))return r.expandIcon||null;if(typeof(c==null?void 0:c.expandIcon)=="function"||Ht(c==null?void 0:c.expandIcon))return(c==null?void 0:c.expandIcon)||null;const W=(F=p??(r==null?void 0:r.expandIcon))!==null&&F!==void 0?F:c==null?void 0:c.expandIcon;return Xe(W,{className:A(`${M}-submenu-expand-icon`,i.isValidElement(W)?(L=W.props)===null||L===void 0?void 0:L.className:void 0)})},[p,r==null?void 0:r.expandIcon,c==null?void 0:c.expandIcon,M]),X=i.useMemo(()=>({prefixCls:M,inlineCollapsed:C||!1,direction:s,firstLevel:!0,theme:b,mode:R,disableMenuItemTitleTooltip:f}),[M,C,s,f,b]);return G(i.createElement($t.Provider,{value:null},i.createElement(Ct.Provider,{value:X},i.createElement(it,Object.assign({getPopupContainer:a,overflowedIndicator:i.createElement(xo,null),overflowedIndicatorPopupClassName:A(M,`${M}-${b}`,h),mode:R,selectable:z,onClick:P},O,{inlineCollapsed:C,style:Object.assign(Object.assign({},c==null?void 0:c.style),m),className:q,prefixCls:M,direction:s,defaultMotions:N,expandIcon:V,ref:t,rootClassName:A(y,j,r.rootClassName,H,k),_internalComponents:Ba})))))}),at=i.forwardRef((e,t)=>{const n=i.useRef(null),o=i.useContext(Ot);return i.useImperativeHandle(t,()=>({menu:n.current,focus:r=>{var l;(l=n.current)===null||l===void 0||l.focus(r)}})),i.createElement(ja,Object.assign({ref:n},e,o))});at.Item=Io;at.SubMenu=wo;at.Divider=So;at.ItemGroup=tn;const Ta=e=>{const{componentCls:t,menuCls:n,colorError:o,colorTextLightSolid:r}=e,l=`${n}-item`;return{[`${t}, ${t}-menu-submenu`]:{[`${n} ${l}`]:{[`&${l}-danger:not(${l}-disabled)`]:{color:o,"&:hover":{color:r,backgroundColor:o}}}}}},za=e=>{const{componentCls:t,menuCls:n,zIndexPopup:o,dropdownArrowDistance:r,sizePopupArrow:l,antCls:a,iconCls:s,motionDurationMid:c,paddingBlock:d,fontSize:g,dropdownEdgeChildPadding:u,colorTextDisabled:m,fontSizeIcon:b,controlPaddingHorizontal:p,colorBgElevated:f}=e;return[{[t]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:o,display:"block","&::before":{position:"absolute",insetBlock:e.calc(l).div(2).sub(r).equal(),zIndex:-9999,opacity:1e-4,content:'""'},"&-menu-vertical":{maxHeight:"100vh",overflowY:"auto"},[`&-trigger${a}-btn`]:{[`& > ${s}-down, & > ${a}-btn-icon > ${s}-down`]:{fontSize:b}},[`${t}-wrap`]:{position:"relative",[`${a}-btn > ${s}-down`]:{fontSize:b},[`${s}-down::before`]:{transition:`transform ${c}`}},[`${t}-wrap-open`]:{[`${s}-down::before`]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},[`&${a}-slide-down-enter${a}-slide-down-enter-active${t}-placement-bottomLeft,
          &${a}-slide-down-appear${a}-slide-down-appear-active${t}-placement-bottomLeft,
          &${a}-slide-down-enter${a}-slide-down-enter-active${t}-placement-bottom,
          &${a}-slide-down-appear${a}-slide-down-appear-active${t}-placement-bottom,
          &${a}-slide-down-enter${a}-slide-down-enter-active${t}-placement-bottomRight,
          &${a}-slide-down-appear${a}-slide-down-appear-active${t}-placement-bottomRight`]:{animationName:Kr},[`&${a}-slide-up-enter${a}-slide-up-enter-active${t}-placement-topLeft,
          &${a}-slide-up-appear${a}-slide-up-appear-active${t}-placement-topLeft,
          &${a}-slide-up-enter${a}-slide-up-enter-active${t}-placement-top,
          &${a}-slide-up-appear${a}-slide-up-appear-active${t}-placement-top,
          &${a}-slide-up-enter${a}-slide-up-enter-active${t}-placement-topRight,
          &${a}-slide-up-appear${a}-slide-up-appear-active${t}-placement-topRight`]:{animationName:Wr},[`&${a}-slide-down-leave${a}-slide-down-leave-active${t}-placement-bottomLeft,
          &${a}-slide-down-leave${a}-slide-down-leave-active${t}-placement-bottom,
          &${a}-slide-down-leave${a}-slide-down-leave-active${t}-placement-bottomRight`]:{animationName:Lr},[`&${a}-slide-up-leave${a}-slide-up-leave-active${t}-placement-topLeft,
          &${a}-slide-up-leave${a}-slide-up-leave-active${t}-placement-top,
          &${a}-slide-up-leave${a}-slide-up-leave-active${t}-placement-topRight`]:{animationName:_r}}},Fr(e,f,{arrowPlacement:{top:!0,bottom:!0}}),{[`${t} ${n}`]:{position:"relative",margin:0},[`${n}-submenu-popup`]:{position:"absolute",zIndex:o,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},[`${t}, ${t}-menu-submenu`]:Object.assign(Object.assign({},yt(e)),{[n]:Object.assign(Object.assign({padding:u,listStyleType:"none",backgroundColor:f,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},yn(e)),{"&:empty":{padding:0,boxShadow:"none"},[`${n}-item-group-title`]:{padding:`${B(d)} ${B(p)}`,color:e.colorTextDescription,transition:`all ${c}`},[`${n}-item`]:{position:"relative",display:"flex",alignItems:"center"},[`${n}-item-icon`]:{minWidth:g,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${n}-title-content`]:{flex:"auto","&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},"> a":{color:"inherit",transition:`all ${c}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}},[`${n}-item-extra`]:{paddingInlineStart:e.padding,marginInlineStart:"auto",fontSize:e.fontSizeSM,color:e.colorTextDescription}},[`${n}-item, ${n}-submenu-title`]:Object.assign(Object.assign({display:"flex",margin:0,padding:`${B(d)} ${B(p)}`,color:e.colorText,fontWeight:"normal",fontSize:g,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${c}`,borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},yn(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:m,cursor:"not-allowed","&:hover":{color:m,backgroundColor:f,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${B(e.marginXXS)} 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${t}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${t}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorIcon,fontSize:b,fontStyle:"normal"}}}),[`${n}-item-group-list`]:{margin:`0 ${B(e.marginXS)}`,padding:0,listStyle:"none"},[`${n}-submenu-title`]:{paddingInlineEnd:e.calc(p).add(e.fontSizeSM).equal()},[`${n}-submenu-vertical`]:{position:"relative"},[`${n}-submenu${n}-submenu-disabled ${t}-menu-submenu-title`]:{[`&, ${t}-menu-submenu-arrow-icon`]:{color:m,backgroundColor:f,cursor:"not-allowed"}},[`${n}-submenu-selected ${t}-menu-submenu-title`]:{color:e.colorPrimary}})})},[bt(e,"slide-up"),bt(e,"slide-down"),Sn(e,"move-up"),Sn(e,"move-down"),no(e,"zoom-big")]]},ka=e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},Vr({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),Xr(e)),Ha=Fe("Dropdown",e=>{const{marginXXS:t,sizePopupArrow:n,paddingXXS:o,componentCls:r}=e,l=ft(e,{menuCls:`${r}-menu`,dropdownArrowDistance:e.calc(n).div(2).add(t).equal(),dropdownEdgeChildPadding:o});return[za(l),Ta(l)]},ka,{resetStyle:!1}),Nt=e=>{var t;const{menu:n,arrow:o,prefixCls:r,children:l,trigger:a,disabled:s,dropdownRender:c,popupRender:d,getPopupContainer:g,overlayClassName:u,rootClassName:m,overlayStyle:b,open:p,onOpenChange:f,visible:v,onVisibleChange:x,mouseEnterDelay:y=.15,mouseLeaveDelay:$=.1,autoAdjustOverflow:w=!0,placement:S="",overlay:h,transitionName:E,destroyOnHidden:O,destroyPopupOnHide:P}=e,{getPopupContainer:R,getPrefixCls:z,direction:C,dropdown:N}=i.useContext(xe),M=d||c;br();const k=i.useMemo(()=>{const K=z();return E!==void 0?E:S.includes("top")?`${K}-slide-down`:`${K}-slide-up`},[z,S,E]),G=i.useMemo(()=>S?S.includes("Center")?S.slice(0,S.indexOf("Center")):S:C==="rtl"?"bottomRight":"bottomLeft",[S,C]),j=z("dropdown",r),H=Un(j),[q,V,X]=Ha(j,H),[,F]=Yn(),L=i.Children.only(Pi(l)?i.createElement("span",null,l):l),W=Xe(L,{className:A(`${j}-trigger`,{[`${j}-rtl`]:C==="rtl"},L.props.className),disabled:(t=L.props.disabled)!==null&&t!==void 0?t:s}),D=s?[]:a,Q=!!(D!=null&&D.includes("contextMenu")),[Y,T]=nt(!1,{value:p??v}),_=Gn(K=>{f==null||f(K,{source:"trigger"}),x==null||x(K),T(K)}),ne=A(u,m,V,X,H,N==null?void 0:N.className,{[`${j}-rtl`]:C==="rtl"}),oe=qr({arrowPointAtCenter:typeof o=="object"&&o.pointAtCenter,autoAdjustOverflow:w,offset:F.marginXXS,arrowWidth:o?F.sizePopupArrow:0,borderRadius:F.borderRadius}),Ne=i.useCallback(()=>{n!=null&&n.selectable&&(n!=null&&n.multiple)||(f==null||f(!1,{source:"menu"}),T(!1))},[n==null?void 0:n.selectable,n==null?void 0:n.multiple]),ve=()=>{let K;return n!=null&&n.items?K=i.createElement(at,Object.assign({},n)):typeof h=="function"?K=h():K=h,M&&(K=M(K)),K=i.Children.only(typeof K=="string"?i.createElement("span",null,K):K),i.createElement(Ia,{prefixCls:`${j}-menu`,rootClassName:A(X,H),expandIcon:i.createElement("span",{className:`${j}-menu-submenu-arrow`},C==="rtl"?i.createElement(_t,{className:`${j}-menu-submenu-arrow-icon`}):i.createElement(Dt,{className:`${j}-menu-submenu-arrow-icon`})),mode:"vertical",selectable:!1,onClick:Ne,validator:Se=>{let{mode:Ie}=Se}},K)},[de,be]=qn("Dropdown",b==null?void 0:b.zIndex);let ye=i.createElement(Ei,Object.assign({alignPoint:Q},Oe(e,["rootClassName"]),{mouseEnterDelay:y,mouseLeaveDelay:$,visible:Y,builtinPlacements:oe,arrow:!!o,overlayClassName:ne,prefixCls:j,getPopupContainer:g||R,transitionName:k,trigger:D,overlay:ve,placement:G,onVisibleChange:_,overlayStyle:Object.assign(Object.assign(Object.assign({},N==null?void 0:N.style),b),{zIndex:de}),autoDestroy:O??P}),W);return de&&(ye=i.createElement(hr.Provider,{value:be},ye)),q(ye)},Da=Gr(Nt,"align",void 0,"dropdown",e=>e),Aa=e=>i.createElement(Da,Object.assign({},e),i.createElement("span",null));Nt._InternalPanelDoNotUseOrYouWillBeFired=Aa;function An(e){return["small","middle","large"].includes(e)}function _n(e){return e?typeof e=="number"&&!Number.isNaN(e):!1}const Oo=le.createContext({latestIndex:0}),_a=Oo.Provider,La=e=>{let{className:t,index:n,children:o,split:r,style:l}=e;const{latestIndex:a}=i.useContext(Oo);return o==null?null:i.createElement(i.Fragment,null,i.createElement("div",{className:t,style:l},o),n<a&&r&&i.createElement("span",{className:`${t}-split`},r))};var Wa=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Ka=i.forwardRef((e,t)=>{var n;const{getPrefixCls:o,direction:r,size:l,className:a,style:s,classNames:c,styles:d}=Zn("space"),{size:g=l??"small",align:u,className:m,rootClassName:b,children:p,direction:f="horizontal",prefixCls:v,split:x,style:y,wrap:$=!1,classNames:w,styles:S}=e,h=Wa(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[E,O]=Array.isArray(g)?g:[g,g],P=An(O),R=An(E),z=_n(O),C=_n(E),N=xt(p,{keepEmpty:!0}),M=u===void 0&&f==="horizontal"?"center":u,k=o("space",v),[G,j,H]=Zr(k),q=A(k,a,j,`${k}-${f}`,{[`${k}-rtl`]:r==="rtl",[`${k}-align-${M}`]:M,[`${k}-gap-row-${O}`]:P,[`${k}-gap-col-${E}`]:R},m,b,H),V=A(`${k}-item`,(n=w==null?void 0:w.item)!==null&&n!==void 0?n:c.item);let X=0;const F=N.map((D,Q)=>{var Y;D!=null&&(X=Q);const T=(D==null?void 0:D.key)||`${V}-${Q}`;return i.createElement(La,{className:V,key:T,index:Q,split:x,style:(Y=S==null?void 0:S.item)!==null&&Y!==void 0?Y:d.item},D)}),L=i.useMemo(()=>({latestIndex:X}),[X]);if(N.length===0)return null;const W={};return $&&(W.flexWrap="wrap"),!R&&C&&(W.columnGap=E),!P&&z&&(W.rowGap=O),G(i.createElement("div",Object.assign({ref:t,className:q,style:Object.assign(Object.assign(Object.assign({},W),s),y)},h),i.createElement(_a,{value:L},F)))}),nn=Ka;nn.Compact=Qr;var Fa=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const No=e=>{const{getPopupContainer:t,getPrefixCls:n,direction:o}=i.useContext(xe),{prefixCls:r,type:l="default",danger:a,disabled:s,loading:c,onClick:d,htmlType:g,children:u,className:m,menu:b,arrow:p,autoFocus:f,overlay:v,trigger:x,align:y,open:$,onOpenChange:w,placement:S,getPopupContainer:h,href:E,icon:O=i.createElement(xo,null),title:P,buttonsRender:R=oe=>oe,mouseEnterDelay:z,mouseLeaveDelay:C,overlayClassName:N,overlayStyle:M,destroyOnHidden:k,destroyPopupOnHide:G,dropdownRender:j,popupRender:H}=e,q=Fa(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyOnHidden","destroyPopupOnHide","dropdownRender","popupRender"]),V=n("dropdown",r),X=`${V}-button`,L={menu:b,arrow:p,autoFocus:f,align:y,disabled:s,trigger:s?[]:x,onOpenChange:w,getPopupContainer:h||t,mouseEnterDelay:z,mouseLeaveDelay:C,overlayClassName:N,overlayStyle:M,destroyOnHidden:k,popupRender:H||j},{compactSize:W,compactItemClassnames:D}=Jr(V,o),Q=A(X,D,m);"destroyPopupOnHide"in e&&(L.destroyPopupOnHide=G),"overlay"in e&&(L.overlay=v),"open"in e&&(L.open=$),"placement"in e?L.placement=S:L.placement=o==="rtl"?"bottomLeft":"bottomRight";const Y=i.createElement(In,{type:l,danger:a,disabled:s,loading:c,onClick:d,htmlType:g,href:E,title:P},u),T=i.createElement(In,{type:l,danger:a,icon:O}),[_,ne]=R([Y,T]);return i.createElement(nn.Compact,Object.assign({className:Q,size:W,block:!0},q),_,i.createElement(Nt,Object.assign({},L),ne))};No.__ANT_BUTTON=!0;const Eo=Nt;Eo.Button=No;function Va(e,t,n){return typeof n=="boolean"?n:e.length?!0:xt(t).some(r=>r.type===yo)}var Po=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};function Et(e){let{suffixCls:t,tagName:n,displayName:o}=e;return r=>i.forwardRef((a,s)=>i.createElement(r,Object.assign({ref:s,suffixCls:t,tagName:n},a)))}const on=i.forwardRef((e,t)=>{const{prefixCls:n,suffixCls:o,className:r,tagName:l}=e,a=Po(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:s}=i.useContext(xe),c=s("layout",n),[d,g,u]=$o(c),m=o?`${c}-${o}`:c;return d(i.createElement(l,Object.assign({className:A(n||m,r,g,u),ref:t},a)))}),Xa=i.forwardRef((e,t)=>{const{direction:n}=i.useContext(xe),[o,r]=i.useState([]),{prefixCls:l,className:a,rootClassName:s,children:c,hasSider:d,tagName:g,style:u}=e,m=Po(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),b=Oe(m,["suffixCls"]),{getPrefixCls:p,className:f,style:v}=Zn("layout"),x=p("layout",l),y=Va(o,c,d),[$,w,S]=$o(x),h=A(x,{[`${x}-has-sider`]:y,[`${x}-rtl`]:n==="rtl"},f,a,s,w,S),E=i.useMemo(()=>({siderHook:{addSider:O=>{r(P=>[].concat(Te(P),[O]))},removeSider:O=>{r(P=>P.filter(R=>R!==O))}}}),[]);return $(i.createElement(bo.Provider,{value:E},i.createElement(g,Object.assign({ref:t,className:h,style:Object.assign(Object.assign({},v),u)},b),c)))}),qa=Et({tagName:"div",displayName:"Layout"})(Xa),Ga=Et({suffixCls:"header",tagName:"header",displayName:"Header"})(on),Ua=Et({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(on),Ya=Et({suffixCls:"content",tagName:"main",displayName:"Content"})(on),we=qa;we.Header=Ga;we.Footer=Ua;we.Content=Ya;we.Sider=yo;we._InternalSiderContext=Ot;var Za={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M816 768h-24V428c0-141.1-104.3-257.7-240-277.1V112c0-22.1-17.9-40-40-40s-40 17.9-40 40v38.9c-135.7 19.4-240 136-240 277.1v340h-24c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h216c0 61.8 50.2 112 112 112s112-50.2 112-112h216c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM512 888c-26.5 0-48-21.5-48-48h96c0 26.5-21.5 48-48 48zM304 768V428c0-55.6 21.6-107.8 60.9-147.1S456.4 220 512 220c55.6 0 107.8 21.6 147.1 60.9S720 372.4 720 428v340H304z"}}]},name:"bell",theme:"outlined"},Qa=function(t,n){return i.createElement(Ke,te({},t,{ref:n,icon:Za}))},Ja=i.forwardRef(Qa),el={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"},tl=function(t,n){return i.createElement(Ke,te({},t,{ref:n,icon:el}))},nl=i.forwardRef(tl);const ol=e=>{const t=e!=null&&e.algorithm?Cr(e.algorithm):$r,n=Object.assign(Object.assign({},yr),e==null?void 0:e.token);return xr(n,{override:e==null?void 0:e.token},t,Sr)};function rl(e){const{sizeUnit:t,sizeStep:n}=e,o=n-2;return{sizeXXL:t*(o+10),sizeXL:t*(o+6),sizeLG:t*(o+2),sizeMD:t*(o+2),sizeMS:t*(o+1),size:t*o,sizeSM:t*o,sizeXS:t*(o-1),sizeXXS:t*(o-1)}}const il=(e,t)=>{const n=t??Gt(e),o=n.fontSizeSM,r=n.controlHeight-4;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},n),rl(t??e)),Ir(o)),{controlHeight:r}),wr(Object.assign(Object.assign({},n),{controlHeight:r})))},pe=(e,t)=>new vt(e).setA(t).toRgbString(),Le=(e,t)=>new vt(e).lighten(t).toHexString(),al=e=>{const t=Qn(e,{theme:"dark"});return{1:t[0],2:t[1],3:t[2],4:t[3],5:t[6],6:t[5],7:t[4],8:t[6],9:t[5],10:t[4]}},ll=(e,t)=>{const n=e||"#000",o=t||"#fff";return{colorBgBase:n,colorTextBase:o,colorText:pe(o,.85),colorTextSecondary:pe(o,.65),colorTextTertiary:pe(o,.45),colorTextQuaternary:pe(o,.25),colorFill:pe(o,.18),colorFillSecondary:pe(o,.12),colorFillTertiary:pe(o,.08),colorFillQuaternary:pe(o,.04),colorBgSolid:pe(o,.95),colorBgSolidHover:pe(o,1),colorBgSolidActive:pe(o,.9),colorBgElevated:Le(n,12),colorBgContainer:Le(n,8),colorBgLayout:Le(n,0),colorBgSpotlight:Le(n,26),colorBgBlur:pe(o,.04),colorBorder:Le(n,26),colorBorderSecondary:Le(n,19)}},sl=(e,t)=>{const n=Object.keys(Or).map(r=>{const l=Qn(e[r],{theme:"dark"});return Array.from({length:10},()=>1).reduce((a,s,c)=>(a[`${r}-${c+1}`]=l[c],a[`${r}${c+1}`]=l[c],a),{})}).reduce((r,l)=>(r=Object.assign(Object.assign({},r),l),r),{}),o=t??Gt(e);return Object.assign(Object.assign(Object.assign({},o),n),Nr(e,{generateColorPalettes:al,generateNeutralColorPalettes:ll}))};function cl(){const[e,t,n]=Yn();return{theme:e,token:t,hashId:n}}const ul={defaultSeed:xn.token,useToken:cl,defaultAlgorithm:Gt,darkAlgorithm:sl,compactAlgorithm:il,getDesignToken:ol,defaultConfig:xn,_internalContext:Er},dl=({title:e,items:t,className:n,icon:o,overlayClassName:r})=>I.jsx(Eo,{menu:{items:t},trigger:["click"],className:n,overlayClassName:r,children:I.jsx("span",{onClick:l=>l.preventDefault(),style:{cursor:"pointer"},children:I.jsxs(nn,{children:[I.jsx("span",{children:e}),o&&I.jsx(Ur,{})]})})}),ml=()=>({showAlert:async({title:t,text:n,icon:o,background:r,showCancelButton:l,confirmButtonText:a,cancelButtonText:s})=>await Pr.mixin({customClass:{confirmButton:"custom-swal-confirm-btn",cancelButton:"custom-swal-cancel-btn",popup:"custom-swal-popup",title:"custom-swal-title",content:"custom-swal-content"}}).fire({title:t,text:n,icon:o,background:r,showCancelButton:l,confirmButtonText:a,cancelButtonText:s})}),{Header:gl}=we,pl=[{label:"Home",path:"/home"},{label:"Sphere it",path:"/sphare-it"},{label:"Agents",path:"/agent"},{label:"Listings",path:"/listing"},{label:"States",path:"/state"},{label:"Contract Q",path:"/contract"}],fl=()=>{let e=window.user;const[t,n]=i.useState(!1);Rr();const{showAlert:o}=ml(),l=Mr().pathname,a=async()=>{(await o({title:"Sign Out",text:"Are you sure you want to sign out?",icon:"question",showCancelButton:!0,confirmButtonText:"Yes, Sign Out",cancelButtonText:"Cancel"})).isConfirmed&&(window.helper.removeStorageData(),window.user=null,window.location.replace("/login"))},s=[{key:"1",label:I.jsxs(je,{to:"/profile/posts",className:`d-flex align-items-center ${l==="/profile/posts"?"active-dropdown-link":""}`,children:[I.jsx("div",{children:I.jsx("img",{src:"/assets/img/heart-icon.png",alt:""})}),I.jsx("div",{className:"ms-2",children:I.jsx("p",{children:"My Profile"})})]})},{key:"2",label:I.jsxs(je,{to:"/favourite",className:`d-flex align-items-center ${l==="/favourite"?"active-dropdown-link":""}`,children:[I.jsx("div",{children:I.jsx("img",{src:"/assets/img/heart-icon.png",alt:""})}),I.jsx("div",{className:"ms-2",children:I.jsx("p",{children:"Favorite Properties"})})]})},{key:"3",label:I.jsxs(je,{to:"/subscription",className:`d-flex align-items-center ${l==="/subscription"?"active-dropdown-link":""}`,children:[I.jsx("div",{children:I.jsx("img",{src:"/assets/img/card-icon.png",alt:""})}),I.jsx("div",{className:"ms-2",children:I.jsx("p",{children:"Subscription"})})]})},{key:"4",label:I.jsxs(je,{to:"/about",className:`d-flex align-items-center ${l==="/about"?"active-dropdown-link":""}`,children:[I.jsx("div",{children:I.jsx("img",{src:"/assets/img/about-icon.png",alt:""})}),I.jsx("div",{className:"ms-2",children:I.jsx("p",{children:"About"})})]})},{key:"5",label:I.jsxs(je,{to:"/setting",className:`d-flex align-items-center ${l==="/setting"?"active-dropdown-link":""}`,children:[I.jsx("div",{children:I.jsx("img",{src:"/assets/img/heart-icon.png",alt:""})}),I.jsx("div",{className:"ms-2",children:I.jsx("p",{children:"Settings"})})]})},{key:"6",label:I.jsxs("div",{className:"d-flex align-items-center",onClick:a,style:{cursor:"pointer"},children:[I.jsx("div",{children:I.jsx("img",{src:"/assets/img/logout-icon.png",alt:""})}),I.jsx("div",{className:"ms-2",children:I.jsx("p",{children:"Sign out"})})]})}];return I.jsxs(gl,{className:"app-header",children:[I.jsxs("div",{className:"header-left",children:[I.jsx(je,{to:"/home",children:I.jsx("img",{src:"/assets/img/logo.png",alt:"Logo",className:"logo"})}),I.jsx("div",{className:"mobile-toggle",onClick:()=>n(!t),children:t?I.jsx(Br,{}):I.jsx(nl,{})}),I.jsx("nav",{className:`nav-links d-flex align-items-center ${t?"open":""}`,children:pl.map(c=>I.jsx(jr,{to:c.path,onClick:()=>n(!1),className:({isActive:d})=>d?"nav-link active":"nav-link",children:c.label},c.path))})]}),I.jsxs("div",{className:"header-right",children:[I.jsxs("div",{className:"icons d-flex align-items-center",children:[I.jsx("div",{className:"ms-3 ",children:I.jsx(At,{count:5,style:{backgroundColor:"#3883E2",color:"#fff"},children:I.jsx(je,{to:"/notifications",children:I.jsx(wn,{shape:"square",size:"large",icon:I.jsx(Ja,{className:"icon"})})})})}),I.jsx("div",{className:"ms-3 me-3",children:I.jsx(At,{count:5,style:{backgroundColor:"#3883E2",color:"#fff"},children:I.jsx(je,{to:"/inbox",children:I.jsx(wn,{shape:"square",size:"large",icon:I.jsx("img",{src:"/assets/img/message-icon.png",alt:""})})})})})]}),I.jsx(dl,{overlayClassName:"profle-dropdown",title:I.jsxs("div",{className:"user-info",children:[I.jsx("img",{src:e==null?void 0:e.image_url,alt:e==null?void 0:e.name,className:"user-img"}),I.jsx("span",{className:"user-name",children:e==null?void 0:e.name})]}),items:s})]})]})},vl=i.memo(fl),{Content:bl}=we,xl=({children:e})=>{const{token:{colorBgContainer:t,borderRadiusLG:n}}=ul.useToken();return I.jsx(we,{children:I.jsxs(we,{children:[I.jsx(vl,{}),I.jsx(bl,{children:e})]})})};export{At as B,dl as C,xl as I,_t as R,nn as S,Dt as a,ml as u};
