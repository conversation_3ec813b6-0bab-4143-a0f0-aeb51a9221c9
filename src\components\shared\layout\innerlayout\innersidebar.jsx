import React, { useEffect, useState } from "react";
import { Layout, Menu } from "antd";
import { Link, useLocation } from "react-router-dom";

const { Sider } = Layout;

const InnerSideBar = () => {
  const [activeKey, setActiveKey] = useState("1");
  const userRole = window.user?.user?.role; // Get the user role
  const location = useLocation();

  // Consolidate menu items into a single structure
  const menuItemsMap = {
    company: [
      {
        key: "1",
        icon: "../admin/assets/img/project-icon.png",
        activeIcon: "../admin/assets/img/project-active.png",
        label: "Projects",
        link: "/projects",
      },
      {
        key: "2",
        icon: "../admin/assets/img/userrole-icon.png",
        activeIcon: "../admin/assets/img/userrole-active.png",
        label: "User Roles",
        link: "/user-role",
      },
      {
        key: "3",
        icon: "../admin/assets/img/employees-icon.png",
        activeIcon: "../admin/assets/img/employees-active.png",
        label: "Employees",
        link: "/employees",
      },
      {
        key: "4",
        icon: "../admin/assets/img/assigned-icon.png",
        activeIcon: "../admin/assets/img/assigned-active.png",
        label: "Assigned",
        link: "/assign-task",
      },
      {
        key: "5",
        icon: "../admin/assets/img/subscription-icon.png",
        activeIcon: "../admin/assets/img/subscription-active.png",
        label: "Subscription",
        link: "/subscription",
      },
      {
        key: "6",
        icon: "../admin/assets/img/notifications-icon.png",
        activeIcon: "../admin/assets/img/notifications-active.png",
        label: "Notifications",
        link: "/notification",
      },
    ],
    employee: [
      {
        key: "1",
        icon: "../admin/assets/img/project-icon.png",
        activeIcon: "../admin/assets/img/project-active.png",
        label: "Projects",
        link: "/projects",
      },
      {
        key: "2",
        icon: "../admin/assets/img/assigned-icon.png",
        activeIcon: "../admin/assets/img/assigned-active.png",
        label: "Assigned Tasks",
        link: "/assign-task",
      },
      {
        key: "3",
        icon: "../admin/assets/img/notifications-icon.png",
        activeIcon: "../admin/assets/img/notifications-active.png",
        label: "Notifications",
        link: "/notification",
      },
      {
        key: "4",
        icon: "../admin/assets/img/assigned-icon.png",
        activeIcon: "../admin/assets/img/assigned-active.png",
        label: "About App",
        link: "/employe/aboutapp",
      },
      {
        key: "5",
        icon: "../admin/assets/img/terms-icon.png",
        activeIcon: "../admin/assets/img/terms-active.png",
        label: "Terms & Conditions",
        link: "/employe/terms-conditions",
      },
      {
        key: "6",
        icon: "../admin/assets/img/privacy-icon.png",
        activeIcon: "../admin/assets/img/privacy-active.png",
        label: "Privacy Policy",
        link: "/employe/privacy-policy",
      },
    ],
  };

  // Dynamically create menu items based on the user role
  const items = menuItemsMap[userRole] || menuItemsMap["employee"]; // Default to employee items if userRole is not defined

  useEffect(() => {
    const pathToKeyMap = items.reduce((acc, item) => {
      acc[item.link] = item.key; // Create a mapping of paths to keys
      return acc;
    }, {});

    setActiveKey(pathToKeyMap[location.pathname] || "1"); // Set the active key based on the current path
  }, [location.pathname, items]);

  const siderStyle = {
    overflow: "auto",
    height: "100vh",
    position: "fixed",
    insetInlineStart: 0,
    top: 0,
    bottom: 0,
    scrollbarWidth: "thin",
    scrollbarColor: "unset",
  };

  const menuItems = items.map((item) => ({
    key: item.key,
    icon: (
      <Link to={item.link} style={{ display: "flex", alignItems: "center" }}>
        <img
          src={activeKey === item.key ? item.activeIcon : item.icon}
          alt={item.label}
        />
      </Link>
    ),
    label: (
      <Link to={item.link} style={{ display: "flex", alignItems: "center" }}>
        <span>{item.label}</span>
      </Link>
    ),
  }));

  return (
    <Sider style={siderStyle}>
      <div className="demo-logo-vertical">
        <div>
          <img src="../admin/assets/img/sidelogo.png" alt="logo" />
        </div>
        <div className="logo-text">
          <h2>Constructify</h2>
        </div>
      </div>
      <Menu
        onClick={({ key }) => setActiveKey(key)} // Update active key on menu click
        selectedKeys={[activeKey]} // Set the selected key
        theme="dark"
        mode="inline"
        items={menuItems} // Use the dynamically created menu items
      />
    </Sider>
  );
};

export default InnerSideBar;
