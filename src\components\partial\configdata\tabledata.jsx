// src/components/shared/table/tableData.js

import { Link } from "react-router-dom";
import CustomDropdown from "../../shared/dropdown";

// Dropdown items
export const items = [
  {
    label: (
      <a href="#" className="color-blue">
        Edit
      </a>
    ),
    key: "0",
  },
  {
    label: (
      <a href="#" className="color-red">
        Delete
      </a>
    ),
    key: "1",
  },
];

// Common image
const folderIcon = <img src="../admin/assets/img/folder-icon.png" alt="" />;
const deleteIcon = (
  <img src="../admin/assets/img/table-delete-icon.png" alt="" />
);
const pdfIcon = <img src="../admin/assets/img/pdf-icon.png" alt="" />;
const employeeImg = <img src="../admin/assets/img/table-img.png" alt="" />;

// Common render for names with images
const renderName = (text, icon) => (
  <div className="d-flex align-items-center">
    <div>{icon}</div>
    <div className="ms-2 color-dark-blue ">{text}</div>
  </div>
);

// Columns and Data for ProjectDetail
export const ColumnsProjectDetail = [
  {
    title: "Name",
    dataIndex: "name",
    render: (text) => <Link to="/projects-category">{text}</Link>,
    sorter: (a, b) => a.name.length - b.name.length,
    width: "45%",
  },
  { title: "Last Updated", dataIndex: "lastupdated", width: "30%" },
  { title: "Updated by", dataIndex: "updatedby", width: "15%" },
  { title: "", dataIndex: "icon", align: "center", width: "5%" },
];
export const DataProjectDetail = Array.from({ length: 6 }, (_, i) => ({
  key: i + 1,
  name: renderName(
    ["Architectural", "Mechanical", "Electrical", "Plumbing", "Elevation"][
      i % 5
    ] + " Designs",
    folderIcon
  ),
  lastupdated: "July 7, 2024. 08:45 AM",
  updatedby: "Justin",
  icon: deleteIcon,
}));

// Columns and Data for Categories
export const ColumnsProjectCategory = [
  {
    title: "Name",
    dataIndex: "name",
    render: (text) => <Link to="/project-sub-category">{text}</Link>,
    sorter: (a, b) => a.name.length - b.name.length,
    width: "45%",
  },
  { title: "Last Updated", dataIndex: "lastupdated", width: "30%" },
  { title: "Updated by", dataIndex: "updatedby", width: "15%" },
  { title: "", dataIndex: "icon", align: "center", width: "5%" },
];
export const DataProjectCategory = Array.from({ length: 8 }, (_, i) => ({
  key: i + 1,
  name: renderName(
    [
      "Main Room",
      "Mechanical Room",
      "Electrical Room",
      "First Floor",
      "Basement",
      "Parking",
      "Stairs",
      "Roof",
    ][i],
    folderIcon
  ),
  lastupdated: "July 7, 2024. 08:45 AM",
  updatedby: "Justin",
  icon: deleteIcon,
}));

// Columns and Data for Subcategories
export const ColumnsProjectSubCategory = [
  {
    title: "Name",
    dataIndex: "name",
    render: (text) => <Link to="/pdf-single">{text}</Link>,
    sorter: (a, b) => a.name.length - b.name.length,
    width: "30%",
  },
  { title: "Revision", dataIndex: "revision", width: "20%" },
  { title: "Last Updated", dataIndex: "lastupdated", width: "30%" },
  { title: "Updated by", dataIndex: "updatedby", width: "15%" },
  { title: "", dataIndex: "icon", align: "center", width: "5%" },
];
export const DataProjectSubCategory = Array.from({ length: 9 }, (_, i) => ({
  key: i + 1,
  name: renderName("ASF Trench block 1", pdfIcon),
  revision: ["Revision "] + [i + 1],
  lastupdated: "July 7, 2024. 08:45 AM",
  updatedby: "Justin",
  icon: deleteIcon,
}));

export const DataEmployees = Array.from({ length: 5 }, (_, i) => ({
  key: i + 1,
  name: renderName("Paul Harrisons", employeeImg),
  status: "Active",
  emailaddress: "<EMAIL>",
  company: "BluePrintFix",
  usertype: "Design & Office",
  action: (
    <CustomDropdown
      title="Action"
      className="table-dropdown"
      icon="true"
      items={items}
    />
  ),
}));

export const DataUserRole = Array.from({ length: 8 }, (_, i) => ({
  key: i + 1,
  usertype: ["Sales Representative", "Electrical Engineer"][i % 2],
  action: (
    <CustomDropdown
      title="Action"
      icon="true"
      className="table-dropdown"
      items={items}
    />
  ),
}));
