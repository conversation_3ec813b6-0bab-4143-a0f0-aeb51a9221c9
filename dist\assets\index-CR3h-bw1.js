import{u as m,j as e,_ as d}from"./index-3mE9H3a0.js";import{I as p}from"./index-C-I6oq0X.js";import{I as l}from"./iconbutton-DFqB2BBL.js";import{S as g}from"./searchbar-Dn-5q6-d.js";import{u as x,F as j,E as h,R as N}from"./useSearchFilterPagination-BF2wgsiQ.js";import{S as f}from"./Skeleton-BqfVCYaM.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";import"./index-BUUUOhAK.js";import"./index-Cd-Wothc.js";import"./react-stripe.esm-CaXK0k-R.js";import"./useLocationData-QmC5yoKM.js";import"./useQuery-Bzo2W4ue.js";import"./languageUtils-BKYM3hOY.js";import"./index-KeJPQTWG.js";import"./index-DX-6A722.js";import"./fade-yKzH711D.js";const u=({agent:s})=>{const a=m();return e.jsxs("div",{className:"agent-item d-flex align-items-center justify-content-between",onClick:()=>a("/agent/detail/posts"),children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"agent-profile",children:e.jsx("img",{src:s.image_url?s.image_url:"/assets/img/placeholder.jpg",alt:s.name})}),e.jsxs("div",{className:"ms-3",children:[e.jsx("div",{className:"d-flex",children:e.jsx("p",{className:"me-2 font-600",children:s.name})}),e.jsx("p",{className:"color-light",children:s.city})]})]}),e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"me-3",children:e.jsx(l,{icon:e.jsx("img",{src:"/assets/img/call-icon.png"}),title:"Call",className:"gray-btn",onClick:()=>window.open(`tel:${s.mobile_no}`)})}),e.jsx("div",{className:"me-3",children:e.jsx(l,{icon:e.jsx("img",{src:"/assets/img/message-icon.png"}),title:"Message",className:"gray-btn"})}),e.jsx("div",{className:"me-3",children:e.jsx(l,{icon:e.jsx("img",{src:"/assets/img/mail-icon.png"}),title:"Mail",className:"blue-btn",onClick:()=>window.open(`mailto:${s.email}`)})})]})]})},M=()=>{const{data:s,isLoading:a,pagination:t,handlePageChange:n,handleFilterClick:r}=x("getUser",{pageSize:10}),c=[{name:"state",label:"State",type:"select",placeholder:"Select State"},{name:"city",label:"City",type:"select",placeholder:"Select City"},{name:"professional_type",label:"Professional Type",type:"radio"},{name:"languages",label:"Languages",type:"select",placeholder:"Select Languages"}];return e.jsx(p,{children:e.jsxs("div",{className:"container-fluid",children:[e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-12",children:e.jsx(g,{onFilterClick:r})})}),e.jsx(j,{fields:c}),e.jsx("div",{className:"row mt-5",children:a?Array.from({length:10}).map((i,o)=>e.jsx("div",{className:"col-12 col-md-6 col-lg-6",children:e.jsx(f,{active:!0,avatar:!0,paragraph:{rows:3}})},o)):d.isEmpty(s==null?void 0:s.data)?e.jsx(h,{title:"No agents found",description:"No agents available at the moment"}):s.data.map(i=>e.jsx("div",{className:"col-12 col-md-6 col-lg-6",children:e.jsx(u,{agent:i})},i.id))}),e.jsx(N,{pagination:t,handlePageChange:n,isLoading:a,itemName:"agents",pageSizeOptions:["10","20","50"],align:"end"})]})})};export{M as default};
