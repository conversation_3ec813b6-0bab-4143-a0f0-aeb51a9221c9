import{r as l,I as w,e as C,j as s,L as R}from"./index-3mE9H3a0.js";import{R as E}from"./DeleteOutlined-DCDN0YWx.js";var I={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"},P=function(a,r){return l.createElement(w,C({},a,{ref:r,icon:I}))},k=l.forwardRef(P);const D=({title:i,location:a,price:r,detail:x,bath_tub:m,bed_room:h,square_feet:g,src:j,forceFavorite:t=!1,showActions:v=!1,onEdit:n=null,onDelete:c=null,id:d,slug:o,image:f})=>{const[p,u]=l.useState(t),b=()=>{t||u(!p)},N=e=>{e.preventDefault(),e.stopPropagation(),n&&n({id:d,slug:o,title:i})},y=e=>{e.preventDefault(),e.stopPropagation(),c&&c({id:d,slug:o,title:i})};return s.jsx(R,{to:j,children:s.jsxs("div",{className:"property-card border rounded overflow-hidden bg-white",children:[s.jsxs("div",{className:"p-card-header position-relative",children:[s.jsx("img",{src:f||"/assets/img/card-img.png",alt:i,className:"w-100",style:{height:"200px",objectFit:"cover"}}),s.jsx("div",{className:"favorite-icon position-absolute",onClick:e=>{e.preventDefault(),e.stopPropagation(),b()},style:{top:"10px",right:"10px",borderRadius:"50%",cursor:t?"default":"pointer"},children:s.jsx("img",{src:p||t?"/assets/img/heart-active.png":"/assets/img/heart-icon.png",alt:"favorite"})}),v&&s.jsxs("div",{className:"action-buttons position-absolute",style:{top:"10px",left:"10px",display:"flex",gap:"8px"},children:[s.jsx("div",{onClick:N,style:{backgroundColor:"rgba(0, 0, 0, 0.9)",borderRadius:"50%",width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",color:"white",border:"1px solid rgba(255, 255, 255, 0.3)"},title:"Edit Property",children:s.jsx(k,{style:{fontSize:"14px"}})}),s.jsx("div",{onClick:y,style:{backgroundColor:"rgba(220, 53, 69, 0.95)",borderRadius:"50%",width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",color:"white",border:"1px solid rgba(255, 255, 255, 0.3)"},title:"Delete Property",children:s.jsx(E,{style:{fontSize:"14px"}})})]})]}),s.jsxs("div",{className:"",children:[s.jsxs("div",{className:"p-card-body",children:[s.jsx("h6",{className:"mt-3",children:i}),s.jsxs("p",{className:"mt-3 mb-3",children:[" ",r]}),s.jsxs("div",{className:"d-flex align-items-center",children:[s.jsx("div",{children:s.jsx("img",{src:"/assets/img/card-location.png",alt:""})}),s.jsx("div",{className:"ms-2",children:s.jsx("p",{children:a})})]}),s.jsx("p",{className:"detail-para mt-2",style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:x})]}),s.jsxs("div",{className:"p-card-footer mt-3 mb-3",children:[s.jsxs("div",{className:"d-flex align-items-center",children:[s.jsx("div",{children:s.jsx("img",{src:"/assets/img/bathtub-icon.png",alt:""})}),s.jsx("div",{className:"ms-2",children:s.jsx("p",{children:m})})]}),s.jsxs("div",{className:"d-flex align-items-center ms-3",children:[s.jsx("div",{children:s.jsx("img",{src:"/assets/img/bed-icon.png",alt:""})}),s.jsx("div",{className:"ms-2",children:s.jsx("p",{children:h})})]}),s.jsxs("div",{className:"d-flex align-items-center ms-3",children:[s.jsx("div",{children:s.jsx("img",{src:"/assets/img/squarefoot-icon.png",alt:""})}),s.jsx("div",{className:"ms-2",children:s.jsx("p",{children:g})})]})]})]})]})})};export{D as P};
