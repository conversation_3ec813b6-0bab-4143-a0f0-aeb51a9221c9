import{r as o,I as h,e as d,z as p,j as r}from"./index-3mE9H3a0.js";import{I as u,R as x}from"./index-Cd-Wothc.js";var f={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880.1 154H143.9c-24.5 0-39.8 26.7-27.5 48L349 597.4V838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V597.4L907.7 202c12.2-21.3-3.1-48-27.6-48zM603.4 798H420.6V642h182.9v156zm9.6-236.6l-9.5 16.6h-183l-9.5-16.6L212.7 226h598.6L613 561.4z"}}]},name:"filter",theme:"outlined"},m=function(e,a){return o.createElement(h,d({},e,{ref:a,icon:f}))},g=o.forwardRef(m);const R=({onSearch:t,onFilterClick:e,filterIcon:a})=>{const{searchKeyword:n,setSearchKeyword:c}=p(),l=i=>{const s=i.target.value;t&&t(s),c(s)};return r.jsx(u,{placeholder:"Search",prefix:r.jsx(x,{style:{color:"gray",fontSize:"150%"}}),value:n,onChange:l,suffix:a?r.jsx("span",{onClick:e,children:a}):r.jsx(g,{style:{color:"gray",fontSize:"150%"},onClick:e}),style:{width:"100%",borderRadius:"4px",padding:"6px 10px",height:"40px"},className:"search-bar mt-4"})};export{g as R,R as S};
