import{r as l,f as jt,W as Rt,i as he,k as Ae,F as rt,e as pe,g as B,az as Me,l as nt,m as ot,n as at,o as Q,ab as Te,av as Ft,N as Ie,a6 as Lt,b as Nt,a7 as kt,I as xe,J as At,aA as ve,aB as ne,M as oe,aC as it,aD as st,aE as lt,h as X,aF as Z,aG as ct,R as Ue,p as Mt,a2 as ut,aw as dt,aH as He,G as pt,aI as Tt,P as qe,C as Ve,aJ as Ut,V as _t,aK as Ge}from"./index-3mE9H3a0.js";import{T as ft,g as Wt,e as zt,i as Bt,u as Xt}from"./index-B2p2olBm.js";import{i as Ht}from"./fade-yKzH711D.js";import{o as mt,B as Ke}from"./button-CMBVME-6.js";import{R as qt}from"./DeleteOutlined-DCDN0YWx.js";import{j as Vt,h as Gt}from"./index-Cd-Wothc.js";import{u as Kt}from"./react-stripe.esm-CaXK0k-R.js";var Jt={percent:0,prefixCls:"rc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1,gapPosition:"bottom"},Yt=function(){var t=l.useRef([]),r=l.useRef(null);return l.useEffect(function(){var n=Date.now(),a=!1;t.current.forEach(function(i){if(i){a=!0;var s=i.style;s.transitionDuration=".3s, .3s, .3s, .06s",r.current&&n-r.current<100&&(s.transitionDuration="0s, 0s")}}),a&&(r.current=Date.now())}),t.current},Je=0,Zt=Rt();function Qt(){var e;return Zt?(e=Je,Je+=1):e="TEST_OR_SSR",e}const er=function(e){var t=l.useState(),r=jt(t,2),n=r[0],a=r[1];return l.useEffect(function(){a("rc_progress_".concat(Qt()))},[]),e||n};var Ye=function(t){var r=t.bg,n=t.children;return l.createElement("div",{style:{width:"100%",height:"100%",background:r}},n)};function Ze(e,t){return Object.keys(e).map(function(r){var n=parseFloat(r),a="".concat(Math.floor(n*t),"%");return"".concat(e[r]," ").concat(a)})}var tr=l.forwardRef(function(e,t){var r=e.prefixCls,n=e.color,a=e.gradientId,i=e.radius,s=e.style,o=e.ptg,c=e.strokeLinecap,u=e.strokeWidth,d=e.size,f=e.gapDegree,m=n&&he(n)==="object",b=m?"#FFF":void 0,v=d/2,y=l.createElement("circle",{className:"".concat(r,"-circle-path"),r:i,cx:v,cy:v,stroke:b,strokeLinecap:c,strokeWidth:u,opacity:o===0?0:1,style:s,ref:t});if(!m)return y;var w="".concat(a,"-conic"),h=f?"".concat(180+f/2,"deg"):"0deg",g=Ze(n,(360-f)/360),x=Ze(n,1),$="conic-gradient(from ".concat(h,", ").concat(g.join(", "),")"),p="linear-gradient(to ".concat(f?"bottom":"top",", ").concat(x.join(", "),")");return l.createElement(l.Fragment,null,l.createElement("mask",{id:w},y),l.createElement("foreignObject",{x:0,y:0,width:d,height:d,mask:"url(#".concat(w,")")},l.createElement(Ye,{bg:p},l.createElement(Ye,{bg:$}))))}),ye=100,Re=function(t,r,n,a,i,s,o,c,u,d){var f=arguments.length>10&&arguments[10]!==void 0?arguments[10]:0,m=n/100*360*((360-s)/360),b=s===0?0:{bottom:0,top:180,left:90,right:-90}[o],v=(100-a)/100*r;u==="round"&&a!==100&&(v+=d/2,v>=r&&(v=r-.01));var y=ye/2;return{stroke:typeof c=="string"?c:void 0,strokeDasharray:"".concat(r,"px ").concat(t),strokeDashoffset:v+f,transform:"rotate(".concat(i+m+b,"deg)"),transformOrigin:"".concat(y,"px ").concat(y,"px"),transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s",fillOpacity:0}},rr=["id","prefixCls","steps","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","style","className","strokeColor","percent"];function Qe(e){var t=e??[];return Array.isArray(t)?t:[t]}var nr=function(t){var r=Ae(Ae({},Jt),t),n=r.id,a=r.prefixCls,i=r.steps,s=r.strokeWidth,o=r.trailWidth,c=r.gapDegree,u=c===void 0?0:c,d=r.gapPosition,f=r.trailColor,m=r.strokeLinecap,b=r.style,v=r.className,y=r.strokeColor,w=r.percent,h=rt(r,rr),g=ye/2,x=er(n),$="".concat(x,"-gradient"),p=g-s/2,P=Math.PI*2*p,S=u>0?90+u/2:-90,j=P*((360-u)/360),M=he(i)==="object"?i:{count:i,gap:2},E=M.count,U=M.gap,ee=Qe(w),G=Qe(y),K=G.find(function(_){return _&&he(_)==="object"}),J=K&&he(K)==="object",T=J?"butt":m,F=Re(P,j,0,100,S,u,d,f,T,s),te=Yt(),H=function(){var q=0;return ee.map(function(C,R){var V=G[R]||G[G.length-1],z=Re(P,j,q,C,S,u,d,V,T,s);return q+=C,l.createElement(tr,{key:R,color:V,ptg:C,radius:p,prefixCls:a,gradientId:$,style:z,strokeLinecap:T,strokeWidth:s,gapDegree:u,ref:function(ae){te[R]=ae},size:ye})}).reverse()},W=function(){var q=Math.round(E*(ee[0]/100)),C=100/E,R=0;return new Array(E).fill(null).map(function(V,z){var re=z<=q-1?G[0]:f,ae=re&&he(re)==="object"?"url(#".concat($,")"):void 0,ce=Re(P,j,R,C,S,u,d,re,"butt",s,U);return R+=(j-ce.strokeDashoffset+U)*100/j,l.createElement("circle",{key:z,className:"".concat(a,"-circle-path"),r:p,cx:g,cy:g,stroke:ae,strokeWidth:s,opacity:1,style:ce,ref:function(se){te[z]=se}})})};return l.createElement("svg",pe({className:B("".concat(a,"-circle"),v),viewBox:"0 0 ".concat(ye," ").concat(ye),style:b,id:n,role:"presentation"},h),!E&&l.createElement("circle",{className:"".concat(a,"-circle-trail"),r:p,cx:g,cy:g,stroke:f,strokeLinecap:T,strokeWidth:o||s,style:F}),E?W():H())};function de(e){return!e||e<0?0:e>100?100:e}function Se(e){let{success:t,successPercent:r}=e,n=r;return t&&"progress"in t&&(n=t.progress),t&&"percent"in t&&(n=t.percent),n}const or=e=>{let{percent:t,success:r,successPercent:n}=e;const a=de(Se({success:r,successPercent:n}));return[a,de(de(t)-a)]},ar=e=>{let{success:t={},strokeColor:r}=e;const{strokeColor:n}=t;return[n||Me.green,r||null]},Pe=(e,t,r)=>{var n,a,i,s;let o=-1,c=-1;if(t==="step"){const u=r.steps,d=r.strokeWidth;typeof e=="string"||typeof e>"u"?(o=e==="small"?2:14,c=d??8):typeof e=="number"?[o,c]=[e,e]:[o=14,c=8]=Array.isArray(e)?e:[e.width,e.height],o*=u}else if(t==="line"){const u=r==null?void 0:r.strokeWidth;typeof e=="string"||typeof e>"u"?c=u||(e==="small"?6:8):typeof e=="number"?[o,c]=[e,e]:[o=-1,c=8]=Array.isArray(e)?e:[e.width,e.height]}else(t==="circle"||t==="dashboard")&&(typeof e=="string"||typeof e>"u"?[o,c]=e==="small"?[60,60]:[120,120]:typeof e=="number"?[o,c]=[e,e]:Array.isArray(e)&&(o=(a=(n=e[0])!==null&&n!==void 0?n:e[1])!==null&&a!==void 0?a:120,c=(s=(i=e[0])!==null&&i!==void 0?i:e[1])!==null&&s!==void 0?s:120));return[o,c]},ir=3,sr=e=>ir/e*100,lr=e=>{const{prefixCls:t,trailColor:r=null,strokeLinecap:n="round",gapPosition:a,gapDegree:i,width:s=120,type:o,children:c,success:u,size:d=s,steps:f}=e,[m,b]=Pe(d,"circle");let{strokeWidth:v}=e;v===void 0&&(v=Math.max(sr(m),6));const y={width:m,height:b,fontSize:m*.15+6},w=l.useMemo(()=>{if(i||i===0)return i;if(o==="dashboard")return 75},[i,o]),h=or(e),g=a||o==="dashboard"&&"bottom"||void 0,x=Object.prototype.toString.call(e.strokeColor)==="[object Object]",$=ar({success:u,strokeColor:e.strokeColor}),p=B(`${t}-inner`,{[`${t}-circle-gradient`]:x}),P=l.createElement(nr,{steps:f,percent:f?h[1]:h,strokeWidth:v,trailWidth:v,strokeColor:f?$[1]:$,strokeLinecap:n,trailColor:r,prefixCls:t,gapDegree:w,gapPosition:g}),S=m<=20,j=l.createElement("div",{className:p,style:y},P,!S&&c);return S?l.createElement(ft,{title:c},j):j},Ee="--progress-line-stroke-color",gt="--progress-percent",et=e=>{const t=e?"100%":"-100%";return new Te(`antProgress${e?"RTL":"LTR"}Active`,{"0%":{transform:`translateX(${t}) scaleX(0)`,opacity:.1},"20%":{transform:`translateX(${t}) scaleX(0)`,opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}})},cr=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:Object.assign(Object.assign({},at(e)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:e.fontSize},[`${t}-outer`]:{display:"inline-flex",alignItems:"center",width:"100%"},[`${t}-inner`]:{position:"relative",display:"inline-block",width:"100%",flex:1,overflow:"hidden",verticalAlign:"middle",backgroundColor:e.remainingColor,borderRadius:e.lineBorderRadius},[`${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.defaultColor}},[`${t}-success-bg, ${t}-bg`]:{position:"relative",background:e.defaultColor,borderRadius:e.lineBorderRadius,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`},[`${t}-layout-bottom`]:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",[`${t}-text`]:{width:"max-content",marginInlineStart:0,marginTop:e.marginXXS}},[`${t}-bg`]:{overflow:"hidden","&::after":{content:'""',background:{_multi_value_:!0,value:["inherit",`var(${Ee})`]},height:"100%",width:`calc(1 / var(${gt}) * 100%)`,display:"block"},[`&${t}-bg-inner`]:{minWidth:"max-content","&::after":{content:"none"},[`${t}-text-inner`]:{color:e.colorWhite,[`&${t}-text-bright`]:{color:"rgba(0, 0, 0, 0.45)"}}}},[`${t}-success-bg`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:e.colorSuccess},[`${t}-text`]:{display:"inline-block",marginInlineStart:e.marginXS,color:e.colorText,lineHeight:1,width:"2em",whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[r]:{fontSize:e.fontSize},[`&${t}-text-outer`]:{width:"max-content"},[`&${t}-text-outer${t}-text-start`]:{width:"max-content",marginInlineStart:0,marginInlineEnd:e.marginXS}},[`${t}-text-inner`]:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",marginInlineStart:0,padding:`0 ${Q(e.paddingXXS)}`,[`&${t}-text-start`]:{justifyContent:"start"},[`&${t}-text-end`]:{justifyContent:"end"}},[`&${t}-status-active`]:{[`${t}-bg::before`]:{position:"absolute",inset:0,backgroundColor:e.colorBgContainer,borderRadius:e.lineBorderRadius,opacity:0,animationName:et(),animationDuration:e.progressActiveMotionDuration,animationTimingFunction:e.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},[`&${t}-rtl${t}-status-active`]:{[`${t}-bg::before`]:{animationName:et(!0)}},[`&${t}-status-exception`]:{[`${t}-bg`]:{backgroundColor:e.colorError},[`${t}-text`]:{color:e.colorError}},[`&${t}-status-exception ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorError}},[`&${t}-status-success`]:{[`${t}-bg`]:{backgroundColor:e.colorSuccess},[`${t}-text`]:{color:e.colorSuccess}},[`&${t}-status-success ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorSuccess}}})}},ur=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-circle-trail`]:{stroke:e.remainingColor},[`&${t}-circle ${t}-inner`]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},[`&${t}-circle ${t}-text`]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:e.circleTextColor,fontSize:e.circleTextFontSize,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[r]:{fontSize:e.circleIconFontSize}},[`${t}-circle&-status-exception`]:{[`${t}-text`]:{color:e.colorError}},[`${t}-circle&-status-success`]:{[`${t}-text`]:{color:e.colorSuccess}}},[`${t}-inline-circle`]:{lineHeight:1,[`${t}-inner`]:{verticalAlign:"bottom"}}}},dr=e=>{const{componentCls:t}=e;return{[t]:{[`${t}-steps`]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:e.progressStepMinWidth,marginInlineEnd:e.progressStepMarginInlineEnd,backgroundColor:e.remainingColor,transition:`all ${e.motionDurationSlow}`,"&-active":{backgroundColor:e.defaultColor}}}}}},pr=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-small&-line, ${t}-small&-line ${t}-text ${r}`]:{fontSize:e.fontSizeSM}}}},fr=e=>({circleTextColor:e.colorText,defaultColor:e.colorInfo,remainingColor:e.colorFillSecondary,lineBorderRadius:100,circleTextFontSize:"1em",circleIconFontSize:`${e.fontSize/e.fontSizeSM}em`}),mr=nt("Progress",e=>{const t=e.calc(e.marginXXS).div(2).equal(),r=ot(e,{progressStepMarginInlineEnd:t,progressStepMinWidth:t,progressActiveMotionDuration:"2.4s"});return[cr(r),ur(r),dr(r),pr(r)]},fr);var gr=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};const hr=e=>{let t=[];return Object.keys(e).forEach(r=>{const n=parseFloat(r.replace(/%/g,""));Number.isNaN(n)||t.push({key:n,value:e[r]})}),t=t.sort((r,n)=>r.key-n.key),t.map(r=>{let{key:n,value:a}=r;return`${a} ${n}%`}).join(", ")},vr=(e,t)=>{const{from:r=Me.blue,to:n=Me.blue,direction:a=t==="rtl"?"to left":"to right"}=e,i=gr(e,["from","to","direction"]);if(Object.keys(i).length!==0){const o=hr(i),c=`linear-gradient(${a}, ${o})`;return{background:c,[Ee]:c}}const s=`linear-gradient(${a}, ${r}, ${n})`;return{background:s,[Ee]:s}},br=e=>{const{prefixCls:t,direction:r,percent:n,size:a,strokeWidth:i,strokeColor:s,strokeLinecap:o="round",children:c,trailColor:u=null,percentPosition:d,success:f}=e,{align:m,type:b}=d,v=s&&typeof s!="string"?vr(s,r):{[Ee]:s,background:s},y=o==="square"||o==="butt"?0:void 0,w=a??[-1,i||(a==="small"?6:8)],[h,g]=Pe(w,"line",{strokeWidth:i}),x={backgroundColor:u||void 0,borderRadius:y},$=Object.assign(Object.assign({width:`${de(n)}%`,height:g,borderRadius:y},v),{[gt]:de(n)/100}),p=Se(e),P={width:`${de(p)}%`,height:g,borderRadius:y,backgroundColor:f==null?void 0:f.strokeColor},S={width:h<0?"100%":h},j=l.createElement("div",{className:`${t}-inner`,style:x},l.createElement("div",{className:B(`${t}-bg`,`${t}-bg-${b}`),style:$},b==="inner"&&c),p!==void 0&&l.createElement("div",{className:`${t}-success-bg`,style:P})),M=b==="outer"&&m==="start",E=b==="outer"&&m==="end";return b==="outer"&&m==="center"?l.createElement("div",{className:`${t}-layout-bottom`},j,c):l.createElement("div",{className:`${t}-outer`,style:S},M&&c,j,E&&c)},yr=e=>{const{size:t,steps:r,rounding:n=Math.round,percent:a=0,strokeWidth:i=8,strokeColor:s,trailColor:o=null,prefixCls:c,children:u}=e,d=n(r*(a/100)),m=t??[t==="small"?2:14,i],[b,v]=Pe(m,"step",{steps:r,strokeWidth:i}),y=b/r,w=Array.from({length:r});for(let h=0;h<r;h++){const g=Array.isArray(s)?s[h]:s;w[h]=l.createElement("div",{key:h,className:B(`${c}-steps-item`,{[`${c}-steps-item-active`]:h<=d-1}),style:{backgroundColor:h<=d-1?g:o,width:y,height:v}})}return l.createElement("div",{className:`${c}-steps-outer`},w,u)};var $r=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};const wr=["normal","exception","active","success"],Cr=l.forwardRef((e,t)=>{const{prefixCls:r,className:n,rootClassName:a,steps:i,strokeColor:s,percent:o=0,size:c="default",showInfo:u=!0,type:d="line",status:f,format:m,style:b,percentPosition:v={}}=e,y=$r(e,["prefixCls","className","rootClassName","steps","strokeColor","percent","size","showInfo","type","status","format","style","percentPosition"]),{align:w="end",type:h="outer"}=v,g=Array.isArray(s)?s[0]:s,x=typeof s=="string"||Array.isArray(s)?s:void 0,$=l.useMemo(()=>{if(g){const H=typeof g=="string"?g:Object.values(g)[0];return new Ft(H).isLight()}return!1},[s]),p=l.useMemo(()=>{var H,W;const _=Se(e);return parseInt(_!==void 0?(H=_??0)===null||H===void 0?void 0:H.toString():(W=o??0)===null||W===void 0?void 0:W.toString(),10)},[o,e.success,e.successPercent]),P=l.useMemo(()=>!wr.includes(f)&&p>=100?"success":f||"normal",[f,p]),{getPrefixCls:S,direction:j,progress:M}=l.useContext(Ie),E=S("progress",r),[U,ee,G]=mr(E),K=d==="line",J=K&&!i,T=l.useMemo(()=>{if(!u)return null;const H=Se(e);let W;const _=m||(C=>`${C}%`),q=K&&$&&h==="inner";return h==="inner"||m||P!=="exception"&&P!=="success"?W=_(de(o),de(H)):P==="exception"?W=K?l.createElement(Lt,null):l.createElement(Nt,null):P==="success"&&(W=K?l.createElement(kt,null):l.createElement(Vt,null)),l.createElement("span",{className:B(`${E}-text`,{[`${E}-text-bright`]:q,[`${E}-text-${w}`]:J,[`${E}-text-${h}`]:J}),title:typeof W=="string"?W:void 0},W)},[u,o,p,P,d,E,m]);let F;d==="line"?F=i?l.createElement(yr,Object.assign({},e,{strokeColor:x,prefixCls:E,steps:typeof i=="object"?i.count:i}),T):l.createElement(br,Object.assign({},e,{strokeColor:g,prefixCls:E,direction:j,percentPosition:{align:w,type:h}}),T):(d==="circle"||d==="dashboard")&&(F=l.createElement(lr,Object.assign({},e,{strokeColor:g,prefixCls:E,progressStatus:P}),T));const te=B(E,`${E}-status-${P}`,{[`${E}-${d==="dashboard"&&"circle"||d}`]:d!=="line",[`${E}-inline-circle`]:d==="circle"&&Pe(c,"circle")[0]<=20,[`${E}-line`]:J,[`${E}-line-align-${w}`]:J,[`${E}-line-position-${h}`]:J,[`${E}-steps`]:i,[`${E}-show-info`]:u,[`${E}-${c}`]:typeof c=="string",[`${E}-rtl`]:j==="rtl"},M==null?void 0:M.className,n,a,ee,G);return U(l.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},M==null?void 0:M.style),b),className:te,role:"progressbar","aria-valuenow":p,"aria-valuemin":0,"aria-valuemax":100},mt(y,["trailColor","strokeWidth","width","gapDegree","gapPosition","strokeLinecap","success","successPercent"])),F))});var Sr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"},Er=function(t,r){return l.createElement(xe,pe({},t,{ref:r,icon:Sr}))},Ir=l.forwardRef(Er),xr={icon:function(t,r){return{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M534 352V136H232v752h560V394H576a42 42 0 01-42-42z",fill:r}},{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM602 137.8L790.2 326H602V137.8zM792 888H232V136h302v216a42 42 0 0042 42h216v494z",fill:t}}]}},name:"file",theme:"twotone"},Pr=function(t,r){return l.createElement(xe,pe({},t,{ref:r,icon:xr}))},Or=l.forwardRef(Pr),Dr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M779.3 196.6c-94.2-94.2-247.6-94.2-341.7 0l-261 260.8c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l261-260.8c32.4-32.4 75.5-50.2 121.3-50.2s88.9 17.8 121.2 50.2c32.4 32.4 50.2 75.5 50.2 121.2 0 45.8-17.8 88.8-50.2 121.2l-266 265.9-43.1 43.1c-40.3 40.3-105.8 40.3-146.1 0-19.5-19.5-30.2-45.4-30.2-73s10.7-53.5 30.2-73l263.9-263.8c6.7-6.6 15.5-10.3 24.9-10.3h.1c9.4 0 18.1 3.7 24.7 10.3 6.7 6.7 10.3 15.5 10.3 24.9 0 9.3-3.7 18.1-10.3 24.7L372.4 653c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l215.6-215.6c19.9-19.9 30.8-46.3 30.8-74.4s-11-54.6-30.8-74.4c-41.1-41.1-107.9-41-149 0L463 364 224.8 602.1A172.22 172.22 0 00174 724.8c0 46.3 18.1 89.8 50.8 122.5 33.9 33.8 78.3 50.7 122.7 50.7 44.4 0 88.8-16.9 122.6-50.7l309.2-309C824.8 492.7 850 432 850 367.5c.1-64.6-25.1-125.3-70.7-170.9z"}}]},name:"paper-clip",theme:"outlined"},jr=function(t,r){return l.createElement(xe,pe({},t,{ref:r,icon:Dr}))},Rr=l.forwardRef(jr),Fr={icon:function(t,r){return{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2z",fill:t}},{tag:"path",attrs:{d:"M424.6 765.8l-150.1-178L136 752.1V792h752v-30.4L658.1 489z",fill:r}},{tag:"path",attrs:{d:"M136 652.7l132.4-157c3.2-3.8 9-3.8 12.2 0l144 170.7L652 396.8c3.2-3.8 9-3.8 12.2 0L888 662.2V232H136v420.7zM304 280a88 88 0 110 176 88 88 0 010-176z",fill:r}},{tag:"path",attrs:{d:"M276 368a28 28 0 1056 0 28 28 0 10-56 0z",fill:r}},{tag:"path",attrs:{d:"M304 456a88 88 0 100-176 88 88 0 000 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z",fill:t}}]}},name:"picture",theme:"twotone"},Lr=function(t,r){return l.createElement(xe,pe({},t,{ref:r,icon:Fr}))},Nr=l.forwardRef(Lr);const Fe=function(e,t){if(e&&t){var r=Array.isArray(t)?t:t.split(","),n=e.name||"",a=e.type||"",i=a.replace(/\/.*$/,"");return r.some(function(s){var o=s.trim();if(/^\*(\/\*)?$/.test(s))return!0;if(o.charAt(0)==="."){var c=n.toLowerCase(),u=o.toLowerCase(),d=[u];return(u===".jpg"||u===".jpeg")&&(d=[".jpg",".jpeg"]),d.some(function(f){return c.endsWith(f)})}return/\/\*$/.test(o)?i===o.replace(/\/.*$/,""):a===o?!0:/^\w+$/.test(o)?(At(!1,"Upload takes an invalidate 'accept' type '".concat(o,"'.Skip for check.")),!0):!1})}return!0};function kr(e,t){var r="cannot ".concat(e.method," ").concat(e.action," ").concat(t.status,"'"),n=new Error(r);return n.status=t.status,n.method=e.method,n.url=e.action,n}function tt(e){var t=e.responseText||e.response;if(!t)return t;try{return JSON.parse(t)}catch{return t}}function Ar(e){var t=new XMLHttpRequest;e.onProgress&&t.upload&&(t.upload.onprogress=function(i){i.total>0&&(i.percent=i.loaded/i.total*100),e.onProgress(i)});var r=new FormData;e.data&&Object.keys(e.data).forEach(function(a){var i=e.data[a];if(Array.isArray(i)){i.forEach(function(s){r.append("".concat(a,"[]"),s)});return}r.append(a,i)}),e.file instanceof Blob?r.append(e.filename,e.file,e.file.name):r.append(e.filename,e.file),t.onerror=function(i){e.onError(i)},t.onload=function(){return t.status<200||t.status>=300?e.onError(kr(e,t),tt(t)):e.onSuccess(tt(t),t)},t.open(e.method,e.action,!0),e.withCredentials&&"withCredentials"in t&&(t.withCredentials=!0);var n=e.headers||{};return n["X-Requested-With"]!==null&&t.setRequestHeader("X-Requested-With","XMLHttpRequest"),Object.keys(n).forEach(function(a){n[a]!==null&&t.setRequestHeader(a,n[a])}),t.send(r),{abort:function(){t.abort()}}}var Mr=function(){var e=ve(ne().mark(function t(r,n){var a,i,s,o,c,u,d,f;return ne().wrap(function(b){for(;;)switch(b.prev=b.next){case 0:u=function(){return u=ve(ne().mark(function y(w){return ne().wrap(function(g){for(;;)switch(g.prev=g.next){case 0:return g.abrupt("return",new Promise(function(x){w.file(function($){n($)?(w.fullPath&&!$.webkitRelativePath&&(Object.defineProperties($,{webkitRelativePath:{writable:!0}}),$.webkitRelativePath=w.fullPath.replace(/^\//,""),Object.defineProperties($,{webkitRelativePath:{writable:!1}})),x($)):x(null)})}));case 1:case"end":return g.stop()}},y)})),u.apply(this,arguments)},c=function(y){return u.apply(this,arguments)},o=function(){return o=ve(ne().mark(function y(w){var h,g,x,$,p;return ne().wrap(function(S){for(;;)switch(S.prev=S.next){case 0:h=w.createReader(),g=[];case 2:return S.next=5,new Promise(function(j){h.readEntries(j,function(){return j([])})});case 5:if(x=S.sent,$=x.length,$){S.next=9;break}return S.abrupt("break",12);case 9:for(p=0;p<$;p++)g.push(x[p]);S.next=2;break;case 12:return S.abrupt("return",g);case 13:case"end":return S.stop()}},y)})),o.apply(this,arguments)},s=function(y){return o.apply(this,arguments)},a=[],i=[],r.forEach(function(v){return i.push(v.webkitGetAsEntry())}),d=function(){var v=ve(ne().mark(function y(w,h){var g,x;return ne().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:if(w){p.next=2;break}return p.abrupt("return");case 2:if(w.path=h||"",!w.isFile){p.next=10;break}return p.next=6,c(w);case 6:g=p.sent,g&&a.push(g),p.next=15;break;case 10:if(!w.isDirectory){p.next=15;break}return p.next=13,s(w);case 13:x=p.sent,i.push.apply(i,oe(x));case 15:case"end":return p.stop()}},y)}));return function(w,h){return v.apply(this,arguments)}}(),f=0;case 9:if(!(f<i.length)){b.next=15;break}return b.next=12,d(i[f]);case 12:f++,b.next=9;break;case 15:return b.abrupt("return",a);case 16:case"end":return b.stop()}},t)}));return function(r,n){return e.apply(this,arguments)}}(),Tr=+new Date,Ur=0;function Le(){return"rc-upload-".concat(Tr,"-").concat(++Ur)}var _r=["component","prefixCls","className","classNames","disabled","id","name","style","styles","multiple","accept","capture","children","directory","openFileDialogOnClick","onMouseEnter","onMouseLeave","hasControlInside"],Wr=function(e){it(r,e);var t=st(r);function r(){var n;lt(this,r);for(var a=arguments.length,i=new Array(a),s=0;s<a;s++)i[s]=arguments[s];return n=t.call.apply(t,[this].concat(i)),X(Z(n),"state",{uid:Le()}),X(Z(n),"reqs",{}),X(Z(n),"fileInput",void 0),X(Z(n),"_isMounted",void 0),X(Z(n),"onChange",function(o){var c=n.props,u=c.accept,d=c.directory,f=o.target.files,m=oe(f).filter(function(b){return!d||Fe(b,u)});n.uploadFiles(m),n.reset()}),X(Z(n),"onClick",function(o){var c=n.fileInput;if(c){var u=o.target,d=n.props.onClick;if(u&&u.tagName==="BUTTON"){var f=c.parentNode;f.focus(),u.blur()}c.click(),d&&d(o)}}),X(Z(n),"onKeyDown",function(o){o.key==="Enter"&&n.onClick(o)}),X(Z(n),"onFileDropOrPaste",function(){var o=ve(ne().mark(function c(u){var d,f,m,b,v,y,w,h,g;return ne().wrap(function($){for(;;)switch($.prev=$.next){case 0:if(u.preventDefault(),u.type!=="dragover"){$.next=3;break}return $.abrupt("return");case 3:if(d=n.props,f=d.multiple,m=d.accept,b=d.directory,v=[],y=[],u.type==="drop"?(w=u.dataTransfer,v=oe(w.items||[]),y=oe(w.files||[])):u.type==="paste"&&(h=u.clipboardData,v=oe(h.items||[]),y=oe(h.files||[])),!b){$.next=14;break}return $.next=10,Mr(Array.prototype.slice.call(v),function(p){return Fe(p,n.props.accept)});case 10:y=$.sent,n.uploadFiles(y),$.next=17;break;case 14:g=oe(y).filter(function(p){return Fe(p,m)}),f===!1&&(g=y.slice(0,1)),n.uploadFiles(g);case 17:case"end":return $.stop()}},c)}));return function(c){return o.apply(this,arguments)}}()),X(Z(n),"onPrePaste",function(o){var c=n.props.pastable;c&&n.onFileDropOrPaste(o)}),X(Z(n),"uploadFiles",function(o){var c=oe(o),u=c.map(function(d){return d.uid=Le(),n.processFile(d,c)});Promise.all(u).then(function(d){var f=n.props.onBatchStart;f==null||f(d.map(function(m){var b=m.origin,v=m.parsedFile;return{file:b,parsedFile:v}})),d.filter(function(m){return m.parsedFile!==null}).forEach(function(m){n.post(m)})})}),X(Z(n),"processFile",function(){var o=ve(ne().mark(function c(u,d){var f,m,b,v,y,w,h,g,x;return ne().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:if(f=n.props.beforeUpload,m=u,!f){p.next=14;break}return p.prev=3,p.next=6,f(u,d);case 6:m=p.sent,p.next=12;break;case 9:p.prev=9,p.t0=p.catch(3),m=!1;case 12:if(m!==!1){p.next=14;break}return p.abrupt("return",{origin:u,parsedFile:null,action:null,data:null});case 14:if(b=n.props.action,typeof b!="function"){p.next=21;break}return p.next=18,b(u);case 18:v=p.sent,p.next=22;break;case 21:v=b;case 22:if(y=n.props.data,typeof y!="function"){p.next=29;break}return p.next=26,y(u);case 26:w=p.sent,p.next=30;break;case 29:w=y;case 30:return h=(he(m)==="object"||typeof m=="string")&&m?m:u,h instanceof File?g=h:g=new File([h],u.name,{type:u.type}),x=g,x.uid=u.uid,p.abrupt("return",{origin:u,data:w,parsedFile:x,action:v});case 35:case"end":return p.stop()}},c,null,[[3,9]])}));return function(c,u){return o.apply(this,arguments)}}()),X(Z(n),"saveFileInput",function(o){n.fileInput=o}),n}return ct(r,[{key:"componentDidMount",value:function(){this._isMounted=!0;var a=this.props.pastable;a&&document.addEventListener("paste",this.onPrePaste)}},{key:"componentWillUnmount",value:function(){this._isMounted=!1,this.abort(),document.removeEventListener("paste",this.onPrePaste)}},{key:"componentDidUpdate",value:function(a){var i=this.props.pastable;i&&!a.pastable?document.addEventListener("paste",this.onPrePaste):!i&&a.pastable&&document.removeEventListener("paste",this.onPrePaste)}},{key:"post",value:function(a){var i=this,s=a.data,o=a.origin,c=a.action,u=a.parsedFile;if(this._isMounted){var d=this.props,f=d.onStart,m=d.customRequest,b=d.name,v=d.headers,y=d.withCredentials,w=d.method,h=o.uid,g=m||Ar,x={action:c,filename:b,data:s,file:u,headers:v,withCredentials:y,method:w||"post",onProgress:function(p){var P=i.props.onProgress;P==null||P(p,u)},onSuccess:function(p,P){var S=i.props.onSuccess;S==null||S(p,u,P),delete i.reqs[h]},onError:function(p,P){var S=i.props.onError;S==null||S(p,P,u),delete i.reqs[h]}};f(o),this.reqs[h]=g(x)}}},{key:"reset",value:function(){this.setState({uid:Le()})}},{key:"abort",value:function(a){var i=this.reqs;if(a){var s=a.uid?a.uid:a;i[s]&&i[s].abort&&i[s].abort(),delete i[s]}else Object.keys(i).forEach(function(o){i[o]&&i[o].abort&&i[o].abort(),delete i[o]})}},{key:"render",value:function(){var a=this.props,i=a.component,s=a.prefixCls,o=a.className,c=a.classNames,u=c===void 0?{}:c,d=a.disabled,f=a.id,m=a.name,b=a.style,v=a.styles,y=v===void 0?{}:v,w=a.multiple,h=a.accept,g=a.capture,x=a.children,$=a.directory,p=a.openFileDialogOnClick,P=a.onMouseEnter,S=a.onMouseLeave,j=a.hasControlInside,M=rt(a,_r),E=B(X(X(X({},s,!0),"".concat(s,"-disabled"),d),o,o)),U=$?{directory:"directory",webkitdirectory:"webkitdirectory"}:{},ee=d?{}:{onClick:p?this.onClick:function(){},onKeyDown:p?this.onKeyDown:function(){},onMouseEnter:P,onMouseLeave:S,onDrop:this.onFileDropOrPaste,onDragOver:this.onFileDropOrPaste,tabIndex:j?void 0:"0"};return Ue.createElement(i,pe({},ee,{className:E,role:j?void 0:"button",style:b}),Ue.createElement("input",pe({},Mt(M,{aria:!0,data:!0}),{id:f,name:m,disabled:d,type:"file",ref:this.saveFileInput,onClick:function(K){return K.stopPropagation()},key:this.state.uid,style:Ae({display:"none"},y.input),className:u.input,accept:h},U,{multiple:w,onChange:this.onChange},g!=null?{capture:g}:{})),x)}}]),r}(l.Component);function Ne(){}var _e=function(e){it(r,e);var t=st(r);function r(){var n;lt(this,r);for(var a=arguments.length,i=new Array(a),s=0;s<a;s++)i[s]=arguments[s];return n=t.call.apply(t,[this].concat(i)),X(Z(n),"uploader",void 0),X(Z(n),"saveUploader",function(o){n.uploader=o}),n}return ct(r,[{key:"abort",value:function(a){this.uploader.abort(a)}},{key:"render",value:function(){return Ue.createElement(Wr,pe({},this.props,{ref:this.saveUploader}))}}]),r}(l.Component);X(_e,"defaultProps",{component:"span",prefixCls:"rc-upload",data:{},headers:{},name:"file",multipart:!1,onStart:Ne,onError:Ne,onSuccess:Ne,multiple:!1,beforeUpload:null,customRequest:null,withCredentials:!1,openFileDialogOnClick:!0,hasControlInside:!1});const zr=e=>{const{componentCls:t,iconCls:r}=e;return{[`${t}-wrapper`]:{[`${t}-drag`]:{position:"relative",width:"100%",height:"100%",textAlign:"center",background:e.colorFillAlter,border:`${Q(e.lineWidth)} dashed ${e.colorBorder}`,borderRadius:e.borderRadiusLG,cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,[t]:{padding:e.padding},[`${t}-btn`]:{display:"table",width:"100%",height:"100%",outline:"none",borderRadius:e.borderRadiusLG,"&:focus-visible":{outline:`${Q(e.lineWidthFocus)} solid ${e.colorPrimaryBorder}`}},[`${t}-drag-container`]:{display:"table-cell",verticalAlign:"middle"},[`
          &:not(${t}-disabled):hover,
          &-hover:not(${t}-disabled)
        `]:{borderColor:e.colorPrimaryHover},[`p${t}-drag-icon`]:{marginBottom:e.margin,[r]:{color:e.colorPrimary,fontSize:e.uploadThumbnailSize}},[`p${t}-text`]:{margin:`0 0 ${Q(e.marginXXS)}`,color:e.colorTextHeading,fontSize:e.fontSizeLG},[`p${t}-hint`]:{color:e.colorTextDescription,fontSize:e.fontSize},[`&${t}-disabled`]:{[`p${t}-drag-icon ${r},
            p${t}-text,
            p${t}-hint
          `]:{color:e.colorTextDisabled}}}}}},Br=e=>{const{componentCls:t,iconCls:r,fontSize:n,lineHeight:a,calc:i}=e,s=`${t}-list-item`,o=`${s}-actions`,c=`${s}-action`;return{[`${t}-wrapper`]:{[`${t}-list`]:Object.assign(Object.assign({},ut()),{lineHeight:e.lineHeight,[s]:{position:"relative",height:i(e.lineHeight).mul(n).equal(),marginTop:e.marginXS,fontSize:n,display:"flex",alignItems:"center",transition:`background-color ${e.motionDurationSlow}`,borderRadius:e.borderRadiusSM,"&:hover":{backgroundColor:e.controlItemBgHover},[`${s}-name`]:Object.assign(Object.assign({},dt),{padding:`0 ${Q(e.paddingXS)}`,lineHeight:a,flex:"auto",transition:`all ${e.motionDurationSlow}`}),[o]:{whiteSpace:"nowrap",[c]:{opacity:0},[r]:{color:e.actionsColor,transition:`all ${e.motionDurationSlow}`},[`
              ${c}:focus-visible,
              &.picture ${c}
            `]:{opacity:1}},[`${t}-icon ${r}`]:{color:e.colorIcon,fontSize:n},[`${s}-progress`]:{position:"absolute",bottom:e.calc(e.uploadProgressOffset).mul(-1).equal(),width:"100%",paddingInlineStart:i(n).add(e.paddingXS).equal(),fontSize:n,lineHeight:0,pointerEvents:"none","> div":{margin:0}}},[`${s}:hover ${c}`]:{opacity:1},[`${s}-error`]:{color:e.colorError,[`${s}-name, ${t}-icon ${r}`]:{color:e.colorError},[o]:{[`${r}, ${r}:hover`]:{color:e.colorError},[c]:{opacity:1}}},[`${t}-list-item-container`]:{transition:`opacity ${e.motionDurationSlow}, height ${e.motionDurationSlow}`,"&::before":{display:"table",width:0,height:0,content:'""'}}})}}},Xr=e=>{const{componentCls:t}=e,r=new Te("uploadAnimateInlineIn",{from:{width:0,height:0,padding:0,opacity:0,margin:e.calc(e.marginXS).div(-2).equal()}}),n=new Te("uploadAnimateInlineOut",{to:{width:0,height:0,padding:0,opacity:0,margin:e.calc(e.marginXS).div(-2).equal()}}),a=`${t}-animate-inline`;return[{[`${t}-wrapper`]:{[`${a}-appear, ${a}-enter, ${a}-leave`]:{animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseInOutCirc,animationFillMode:"forwards"},[`${a}-appear, ${a}-enter`]:{animationName:r},[`${a}-leave`]:{animationName:n}}},{[`${t}-wrapper`]:Ht(e)},r,n]},Hr=e=>{const{componentCls:t,iconCls:r,uploadThumbnailSize:n,uploadProgressOffset:a,calc:i}=e,s=`${t}-list`,o=`${s}-item`;return{[`${t}-wrapper`]:{[`
        ${s}${s}-picture,
        ${s}${s}-picture-card,
        ${s}${s}-picture-circle
      `]:{[o]:{position:"relative",height:i(n).add(i(e.lineWidth).mul(2)).add(i(e.paddingXS).mul(2)).equal(),padding:e.paddingXS,border:`${Q(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusLG,"&:hover":{background:"transparent"},[`${o}-thumbnail`]:Object.assign(Object.assign({},dt),{width:n,height:n,lineHeight:Q(i(n).add(e.paddingSM).equal()),textAlign:"center",flex:"none",[r]:{fontSize:e.fontSizeHeading2,color:e.colorPrimary},img:{display:"block",width:"100%",height:"100%",overflow:"hidden"}}),[`${o}-progress`]:{bottom:a,width:`calc(100% - ${Q(i(e.paddingSM).mul(2).equal())})`,marginTop:0,paddingInlineStart:i(n).add(e.paddingXS).equal()}},[`${o}-error`]:{borderColor:e.colorError,[`${o}-thumbnail ${r}`]:{[`svg path[fill='${He[0]}']`]:{fill:e.colorErrorBg},[`svg path[fill='${He.primary}']`]:{fill:e.colorError}}},[`${o}-uploading`]:{borderStyle:"dashed",[`${o}-name`]:{marginBottom:a}}},[`${s}${s}-picture-circle ${o}`]:{[`&, &::before, ${o}-thumbnail`]:{borderRadius:"50%"}}}}},qr=e=>{const{componentCls:t,iconCls:r,fontSizeLG:n,colorTextLightSolid:a,calc:i}=e,s=`${t}-list`,o=`${s}-item`,c=e.uploadPicCardSize;return{[`
      ${t}-wrapper${t}-picture-card-wrapper,
      ${t}-wrapper${t}-picture-circle-wrapper
    `]:Object.assign(Object.assign({},ut()),{display:"block",[`${t}${t}-select`]:{width:c,height:c,textAlign:"center",verticalAlign:"top",backgroundColor:e.colorFillAlter,border:`${Q(e.lineWidth)} dashed ${e.colorBorder}`,borderRadius:e.borderRadiusLG,cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,[`> ${t}`]:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%",textAlign:"center"},[`&:not(${t}-disabled):hover`]:{borderColor:e.colorPrimary}},[`${s}${s}-picture-card, ${s}${s}-picture-circle`]:{display:"flex",flexWrap:"wrap","@supports not (gap: 1px)":{"& > *":{marginBlockEnd:e.marginXS,marginInlineEnd:e.marginXS}},"@supports (gap: 1px)":{gap:e.marginXS},[`${s}-item-container`]:{display:"inline-block",width:c,height:c,verticalAlign:"top"},"&::after":{display:"none"},"&::before":{display:"none"},[o]:{height:"100%",margin:0,"&::before":{position:"absolute",zIndex:1,width:`calc(100% - ${Q(i(e.paddingXS).mul(2).equal())})`,height:`calc(100% - ${Q(i(e.paddingXS).mul(2).equal())})`,backgroundColor:e.colorBgMask,opacity:0,transition:`all ${e.motionDurationSlow}`,content:'" "'}},[`${o}:hover`]:{[`&::before, ${o}-actions`]:{opacity:1}},[`${o}-actions`]:{position:"absolute",insetInlineStart:0,zIndex:10,width:"100%",whiteSpace:"nowrap",textAlign:"center",opacity:0,transition:`all ${e.motionDurationSlow}`,[`
            ${r}-eye,
            ${r}-download,
            ${r}-delete
          `]:{zIndex:10,width:n,margin:`0 ${Q(e.marginXXS)}`,fontSize:n,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,color:a,"&:hover":{color:a},svg:{verticalAlign:"baseline"}}},[`${o}-thumbnail, ${o}-thumbnail img`]:{position:"static",display:"block",width:"100%",height:"100%",objectFit:"contain"},[`${o}-name`]:{display:"none",textAlign:"center"},[`${o}-file + ${o}-name`]:{position:"absolute",bottom:e.margin,display:"block",width:`calc(100% - ${Q(i(e.paddingXS).mul(2).equal())})`},[`${o}-uploading`]:{[`&${o}`]:{backgroundColor:e.colorFillAlter},[`&::before, ${r}-eye, ${r}-download, ${r}-delete`]:{display:"none"}},[`${o}-progress`]:{bottom:e.marginXL,width:`calc(100% - ${Q(i(e.paddingXS).mul(2).equal())})`,paddingInlineStart:0}}}),[`${t}-wrapper${t}-picture-circle-wrapper`]:{[`${t}${t}-select`]:{borderRadius:"50%"}}}},Vr=e=>{const{componentCls:t}=e;return{[`${t}-rtl`]:{direction:"rtl"}}},Gr=e=>{const{componentCls:t,colorTextDisabled:r}=e;return{[`${t}-wrapper`]:Object.assign(Object.assign({},at(e)),{[t]:{outline:0,"input[type='file']":{cursor:"pointer"}},[`${t}-select`]:{display:"inline-block"},[`${t}-hidden`]:{display:"none"},[`${t}-disabled`]:{color:r,cursor:"not-allowed"}})}},Kr=e=>({actionsColor:e.colorIcon}),Jr=nt("Upload",e=>{const{fontSizeHeading3:t,fontHeight:r,lineWidth:n,controlHeightLG:a,calc:i}=e,s=ot(e,{uploadThumbnailSize:i(t).mul(2).equal(),uploadProgressOffset:i(i(r).div(2)).add(n).equal(),uploadPicCardSize:i(a).mul(2.55).equal()});return[Gr(s),zr(s),Hr(s),qr(s),Br(s),Xr(s),Vr(s),Wt(s)]},Kr);function we(e){return Object.assign(Object.assign({},e),{lastModified:e.lastModified,lastModifiedDate:e.lastModifiedDate,name:e.name,size:e.size,type:e.type,uid:e.uid,percent:0,originFileObj:e})}function Ce(e,t){const r=oe(t),n=r.findIndex(a=>{let{uid:i}=a;return i===e.uid});return n===-1?r.push(e):r[n]=e,r}function ke(e,t){const r=e.uid!==void 0?"uid":"name";return t.filter(n=>n[r]===e[r])[0]}function Yr(e,t){const r=e.uid!==void 0?"uid":"name",n=t.filter(a=>a[r]!==e[r]);return n.length===t.length?null:n}const Zr=function(){const t=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:"").split("/"),n=t[t.length-1].split(/#|\?/)[0];return(/\.[^./\\]*$/.exec(n)||[""])[0]},ht=e=>e.indexOf("image/")===0,Qr=e=>{if(e.type&&!e.thumbUrl)return ht(e.type);const t=e.thumbUrl||e.url||"",r=Zr(t);return/^data:image\//.test(t)||/(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico|heic|heif)$/i.test(r)?!0:!(/^data:/.test(t)||r)},ue=200;function en(e){return new Promise(t=>{if(!e.type||!ht(e.type)){t("");return}const r=document.createElement("canvas");r.width=ue,r.height=ue,r.style.cssText=`position: fixed; left: 0; top: 0; width: ${ue}px; height: ${ue}px; z-index: 9999; display: none;`,document.body.appendChild(r);const n=r.getContext("2d"),a=new Image;if(a.onload=()=>{const{width:i,height:s}=a;let o=ue,c=ue,u=0,d=0;i>s?(c=s*(ue/i),d=-(c-o)/2):(o=i*(ue/s),u=-(o-c)/2),n.drawImage(a,u,d,o,c);const f=r.toDataURL();document.body.removeChild(r),window.URL.revokeObjectURL(a.src),t(f)},a.crossOrigin="anonymous",e.type.startsWith("image/svg+xml")){const i=new FileReader;i.onload=()=>{i.result&&typeof i.result=="string"&&(a.src=i.result)},i.readAsDataURL(e)}else if(e.type.startsWith("image/gif")){const i=new FileReader;i.onload=()=>{i.result&&t(i.result)},i.readAsDataURL(e)}else a.src=window.URL.createObjectURL(e)})}const tn=l.forwardRef((e,t)=>{let{prefixCls:r,className:n,style:a,locale:i,listType:s,file:o,items:c,progress:u,iconRender:d,actionIconRender:f,itemRender:m,isImgUrl:b,showPreviewIcon:v,showRemoveIcon:y,showDownloadIcon:w,previewIcon:h,removeIcon:g,downloadIcon:x,extra:$,onPreview:p,onDownload:P,onClose:S}=e;var j,M;const{status:E}=o,[U,ee]=l.useState(E);l.useEffect(()=>{E!=="removed"&&ee(E)},[E]);const[G,K]=l.useState(!1);l.useEffect(()=>{const k=setTimeout(()=>{K(!0)},300);return()=>{clearTimeout(k)}},[]);const J=d(o);let T=l.createElement("div",{className:`${r}-icon`},J);if(s==="picture"||s==="picture-card"||s==="picture-circle")if(U==="uploading"||!o.thumbUrl&&!o.url){const k=B(`${r}-list-item-thumbnail`,{[`${r}-list-item-file`]:U!=="uploading"});T=l.createElement("div",{className:k},J)}else{const k=b!=null&&b(o)?l.createElement("img",{src:o.thumbUrl||o.url,alt:o.name,className:`${r}-list-item-image`,crossOrigin:o.crossOrigin}):J,N=B(`${r}-list-item-thumbnail`,{[`${r}-list-item-file`]:b&&!b(o)});T=l.createElement("a",{className:N,onClick:le=>p(o,le),href:o.url||o.thumbUrl,target:"_blank",rel:"noopener noreferrer"},k)}const F=B(`${r}-list-item`,`${r}-list-item-${U}`),te=typeof o.linkProps=="string"?JSON.parse(o.linkProps):o.linkProps,H=(typeof y=="function"?y(o):y)?f((typeof g=="function"?g(o):g)||l.createElement(qt,null),()=>S(o),r,i.removeFile,!0):null,W=(typeof w=="function"?w(o):w)&&U==="done"?f((typeof x=="function"?x(o):x)||l.createElement(Ir,null),()=>P(o),r,i.downloadFile):null,_=s!=="picture-card"&&s!=="picture-circle"&&l.createElement("span",{key:"download-delete",className:B(`${r}-list-item-actions`,{picture:s==="picture"})},W,H),q=typeof $=="function"?$(o):$,C=q&&l.createElement("span",{className:`${r}-list-item-extra`},q),R=B(`${r}-list-item-name`),V=o.url?l.createElement("a",Object.assign({key:"view",target:"_blank",rel:"noopener noreferrer",className:R,title:o.name},te,{href:o.url,onClick:k=>p(o,k)}),o.name,C):l.createElement("span",{key:"view",className:R,onClick:k=>p(o,k),title:o.name},o.name,C),z=(typeof v=="function"?v(o):v)&&(o.url||o.thumbUrl)?l.createElement("a",{href:o.url||o.thumbUrl,target:"_blank",rel:"noopener noreferrer",onClick:k=>p(o,k),title:i.previewFile},typeof h=="function"?h(o):h||l.createElement(Gt,null)):null,re=(s==="picture-card"||s==="picture-circle")&&U!=="uploading"&&l.createElement("span",{className:`${r}-list-item-actions`},z,U==="done"&&W,H),{getPrefixCls:ae}=l.useContext(Ie),ce=ae(),ie=l.createElement("div",{className:F},T,V,_,re,G&&l.createElement(pt,{motionName:`${ce}-fade`,visible:U==="uploading",motionDeadline:2e3},k=>{let{className:N}=k;const le="percent"in o?l.createElement(Cr,Object.assign({},u,{type:"line",percent:o.percent,"aria-label":o["aria-label"],"aria-labelledby":o["aria-labelledby"]})):null;return l.createElement("div",{className:B(`${r}-list-item-progress`,N)},le)})),se=o.response&&typeof o.response=="string"?o.response:((j=o.error)===null||j===void 0?void 0:j.statusText)||((M=o.error)===null||M===void 0?void 0:M.message)||i.uploadError,be=U==="error"?l.createElement(ft,{title:se,getPopupContainer:k=>k.parentNode},ie):ie;return l.createElement("div",{className:B(`${r}-list-item-container`,n),style:a,ref:t},m?m(be,o,c,{download:P.bind(null,o),preview:p.bind(null,o),remove:S.bind(null,o)}):be)}),rn=(e,t)=>{const{listType:r="text",previewFile:n=en,onPreview:a,onDownload:i,onRemove:s,locale:o,iconRender:c,isImageUrl:u=Qr,prefixCls:d,items:f=[],showPreviewIcon:m=!0,showRemoveIcon:b=!0,showDownloadIcon:v=!1,removeIcon:y,previewIcon:w,downloadIcon:h,extra:g,progress:x={size:[-1,2],showInfo:!1},appendAction:$,appendActionVisible:p=!0,itemRender:P,disabled:S}=e,j=zt(),[M,E]=l.useState(!1),U=["picture-card","picture-circle"].includes(r);l.useEffect(()=>{r.startsWith("picture")&&(f||[]).forEach(C=>{!(C.originFileObj instanceof File||C.originFileObj instanceof Blob)||C.thumbUrl!==void 0||(C.thumbUrl="",n==null||n(C.originFileObj).then(R=>{C.thumbUrl=R||"",j()}))})},[r,f,n]),l.useEffect(()=>{E(!0)},[]);const ee=(C,R)=>{if(a)return R==null||R.preventDefault(),a(C)},G=C=>{typeof i=="function"?i(C):C.url&&window.open(C.url)},K=C=>{s==null||s(C)},J=C=>{if(c)return c(C,r);const R=C.status==="uploading";if(r.startsWith("picture")){const V=r==="picture"?l.createElement(Ve,null):o.uploading,z=u!=null&&u(C)?l.createElement(Nr,null):l.createElement(Or,null);return R?V:z}return R?l.createElement(Ve,null):l.createElement(Rr,null)},T=(C,R,V,z,re)=>{const ae={type:"text",size:"small",title:z,onClick:ce=>{var ie,se;R(),l.isValidElement(C)&&((se=(ie=C.props).onClick)===null||se===void 0||se.call(ie,ce))},className:`${V}-list-item-action`,disabled:re?S:!1};return l.isValidElement(C)?l.createElement(Ke,Object.assign({},ae,{icon:qe(C,Object.assign(Object.assign({},C.props),{onClick:()=>{}}))})):l.createElement(Ke,Object.assign({},ae),l.createElement("span",null,C))};l.useImperativeHandle(t,()=>({handlePreview:ee,handleDownload:G}));const{getPrefixCls:F}=l.useContext(Ie),te=F("upload",d),H=F(),W=B(`${te}-list`,`${te}-list-${r}`),_=l.useMemo(()=>mt(Bt(H),["onAppearEnd","onEnterEnd","onLeaveEnd"]),[H]),q=Object.assign(Object.assign({},U?{}:_),{motionDeadline:2e3,motionName:`${te}-${U?"animate-inline":"animate"}`,keys:oe(f.map(C=>({key:C.uid,file:C}))),motionAppear:M});return l.createElement("div",{className:W},l.createElement(Tt,Object.assign({},q,{component:!1}),C=>{let{key:R,file:V,className:z,style:re}=C;return l.createElement(tn,{key:R,locale:o,prefixCls:te,className:z,style:re,file:V,items:f,progress:x,listType:r,isImgUrl:u,showPreviewIcon:m,showRemoveIcon:b,showDownloadIcon:v,removeIcon:y,previewIcon:w,downloadIcon:h,extra:g,iconRender:J,actionIconRender:T,itemRender:P,onPreview:ee,onDownload:G,onClose:K})}),$&&l.createElement(pt,Object.assign({},q,{visible:p,forceRender:!0}),C=>{let{className:R,style:V}=C;return qe($,z=>({className:B(z.className,R),style:Object.assign(Object.assign(Object.assign({},V),{pointerEvents:R?"none":void 0}),z.style)}))}))},nn=l.forwardRef(rn);var on=function(e,t,r,n){function a(i){return i instanceof r?i:new r(function(s){s(i)})}return new(r||(r=Promise))(function(i,s){function o(d){try{u(n.next(d))}catch(f){s(f)}}function c(d){try{u(n.throw(d))}catch(f){s(f)}}function u(d){d.done?i(d.value):a(d.value).then(o,c)}u((n=n.apply(e,[])).next())})};const $e=`__LIST_IGNORE_${Date.now()}__`,an=(e,t)=>{const{fileList:r,defaultFileList:n,onRemove:a,showUploadList:i=!0,listType:s="text",onPreview:o,onDownload:c,onChange:u,onDrop:d,previewFile:f,disabled:m,locale:b,iconRender:v,isImageUrl:y,progress:w,prefixCls:h,className:g,type:x="select",children:$,style:p,itemRender:P,maxCount:S,data:j={},multiple:M=!1,hasControlInside:E=!0,action:U="",accept:ee="",supportServerRender:G=!0,rootClassName:K}=e,J=l.useContext(Ut),T=m??J,[F,te]=Xt(n||[],{value:r,postState:I=>I??[]}),[H,W]=l.useState("drop"),_=l.useRef(null),q=l.useRef(null);l.useMemo(()=>{const I=Date.now();(r||[]).forEach((D,A)=>{!D.uid&&!Object.isFrozen(D)&&(D.uid=`__AUTO__${I}_${A}__`)})},[r]);const C=(I,D,A)=>{let O=oe(D),L=!1;S===1?O=O.slice(-1):S&&(L=O.length>S,O=O.slice(0,S)),Ge.flushSync(()=>{te(O)});const Y={file:I,fileList:O};A&&(Y.event=A),(!L||I.status==="removed"||O.some(fe=>fe.uid===I.uid))&&Ge.flushSync(()=>{u==null||u(Y)})},R=(I,D)=>on(void 0,void 0,void 0,function*(){const{beforeUpload:A,transformFile:O}=e;let L=I;if(A){const Y=yield A(I,D);if(Y===!1)return!1;if(delete I[$e],Y===$e)return Object.defineProperty(I,$e,{value:!0,configurable:!0}),!1;typeof Y=="object"&&Y&&(L=Y)}return O&&(L=yield O(L)),L}),V=I=>{const D=I.filter(L=>!L.file[$e]);if(!D.length)return;const A=D.map(L=>we(L.file));let O=oe(F);A.forEach(L=>{O=Ce(L,O)}),A.forEach((L,Y)=>{let fe=L;if(D[Y].parsedFile)L.status="uploading";else{const{originFileObj:ge}=L;let me;try{me=new File([ge],ge.name,{type:ge.type})}catch{me=new Blob([ge],{type:ge.type}),me.name=ge.name,me.lastModifiedDate=new Date,me.lastModified=new Date().getTime()}me.uid=L.uid,fe=me}C(fe,O)})},z=(I,D,A)=>{try{typeof I=="string"&&(I=JSON.parse(I))}catch{}if(!ke(D,F))return;const O=we(D);O.status="done",O.percent=100,O.response=I,O.xhr=A;const L=Ce(O,F);C(O,L)},re=(I,D)=>{if(!ke(D,F))return;const A=we(D);A.status="uploading",A.percent=I.percent;const O=Ce(A,F);C(A,O,I)},ae=(I,D,A)=>{if(!ke(A,F))return;const O=we(A);O.error=I,O.response=D,O.status="error";const L=Ce(O,F);C(O,L)},ce=I=>{let D;Promise.resolve(typeof a=="function"?a(I):a).then(A=>{var O;if(A===!1)return;const L=Yr(I,F);L&&(D=Object.assign(Object.assign({},I),{status:"removed"}),F==null||F.forEach(Y=>{const fe=D.uid!==void 0?"uid":"name";Y[fe]===D[fe]&&!Object.isFrozen(Y)&&(Y.status="removed")}),(O=_.current)===null||O===void 0||O.abort(D),C(D,L))})},ie=I=>{W(I.type),I.type==="drop"&&(d==null||d(I))};l.useImperativeHandle(t,()=>({onBatchStart:V,onSuccess:z,onProgress:re,onError:ae,fileList:F,upload:_.current,nativeElement:q.current}));const{getPrefixCls:se,direction:be,upload:k}=l.useContext(Ie),N=se("upload",h),le=Object.assign(Object.assign({onBatchStart:V,onError:ae,onProgress:re,onSuccess:z},e),{data:j,multiple:M,action:U,accept:ee,supportServerRender:G,prefixCls:N,disabled:T,beforeUpload:R,onChange:void 0,hasControlInside:E});delete le.className,delete le.style,(!$||T)&&delete le.id;const We=`${N}-wrapper`,[Oe,ze,yt]=Jr(N,We),[$t]=Kt("Upload",_t.Upload),{showRemoveIcon:Be,showPreviewIcon:wt,showDownloadIcon:Ct,removeIcon:St,previewIcon:Et,downloadIcon:It,extra:xt}=typeof i=="boolean"?{}:i,Pt=typeof Be>"u"?!T:Be,De=(I,D)=>i?l.createElement(nn,{prefixCls:N,listType:s,items:F,previewFile:f,onPreview:o,onDownload:c,onRemove:ce,showRemoveIcon:Pt,showPreviewIcon:wt,showDownloadIcon:Ct,removeIcon:St,previewIcon:Et,downloadIcon:It,iconRender:v,extra:xt,locale:Object.assign(Object.assign({},$t),b),isImageUrl:y,progress:w,appendAction:I,appendActionVisible:D,itemRender:P,disabled:T}):I,je=B(We,g,K,ze,yt,k==null?void 0:k.className,{[`${N}-rtl`]:be==="rtl",[`${N}-picture-card-wrapper`]:s==="picture-card",[`${N}-picture-circle-wrapper`]:s==="picture-circle"}),Ot=Object.assign(Object.assign({},k==null?void 0:k.style),p);if(x==="drag"){const I=B(ze,N,`${N}-drag`,{[`${N}-drag-uploading`]:F.some(D=>D.status==="uploading"),[`${N}-drag-hover`]:H==="dragover",[`${N}-disabled`]:T,[`${N}-rtl`]:be==="rtl"});return Oe(l.createElement("span",{className:je,ref:q},l.createElement("div",{className:I,style:Ot,onDrop:ie,onDragOver:ie,onDragLeave:ie},l.createElement(_e,Object.assign({},le,{ref:_,className:`${N}-btn`}),l.createElement("div",{className:`${N}-drag-container`},$))),De()))}const Dt=B(N,`${N}-select`,{[`${N}-disabled`]:T,[`${N}-hidden`]:!$}),Xe=l.createElement("div",{className:Dt},l.createElement(_e,Object.assign({},le,{ref:_})));return Oe(s==="picture-card"||s==="picture-circle"?l.createElement("span",{className:je,ref:q},De(Xe,!!$)):l.createElement("span",{className:je,ref:q},Xe,De()))},vt=l.forwardRef(an);var sn=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};const ln=l.forwardRef((e,t)=>{var{style:r,height:n,hasControlInside:a=!1}=e,i=sn(e,["style","height","hasControlInside"]);return l.createElement(vt,Object.assign({ref:t,hasControlInside:a},i,{type:"drag",style:Object.assign(Object.assign({},r),{height:n})}))}),bt=vt;bt.Dragger=ln;bt.LIST_IGNORE=$e;export{Rr as R,bt as U};
