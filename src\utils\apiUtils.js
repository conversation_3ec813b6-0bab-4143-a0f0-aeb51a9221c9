/**
 * Transform parameters to handle arrays properly for API calls
 * Converts arrays to multiple parameters with the same key name
 * Example: { languages: [0, 1, 2] } becomes { languages: [0, 1, 2] } but gets serialized as languages=0&languages=1&languages=2
 */
export const transformParamsForAPI = (params) => {
  if (!params || typeof params !== 'object') {
    return params;
  }

  const transformedParams = { ...params };

  // Handle arrays by creating a custom URLSearchParams object
  Object.entries(transformedParams).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      // Keep the array as is - we'll handle it in the URL building
      transformedParams[key] = value;
    }
  });

  return transformedParams;
};

/**
 * Build query string that properly handles arrays
 * Arrays are converted to multiple parameters with the same key
 */
export const buildQueryString = (params) => {
  if (!params || typeof params !== 'object') {
    return '';
  }

  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        // Add each array item as a separate parameter
        value.forEach(item => {
          searchParams.append(key, item);
        });
      } else {
        searchParams.append(key, value);
      }
    }
  });

  return searchParams.toString();
};

export default {
  transformParamsForAPI,
  buildQueryString,
};