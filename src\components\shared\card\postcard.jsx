import React from "react";
import {
  LikeOutlined,
  MessageOutlined,
  ShareAltOutlined,
} from "@ant-design/icons";
import BaseInput from "@/components/shared/inputs";
import CommentBox from "@/components/shared/box/commentbox";
import InteractionBox from "@/components/shared/box/interactionbox";

const PostCard = ({
  user,
  time,
  question,
  body,
  likes,
  comments,
  shares,
  reactions,
  onClick
}) => {
  return (
    <div className="post-card p-3 rounded border bg-white" onClick={onClick}> 
      {/* Header */}
      
      <div className="d-flex justify-content-between align-items-start mb-2">
        <div className="d-flex align-items-center">
          <img
            src={user.avatar}
            alt="avatar"
            className="rounded-circle"
            width={40}
            height={40}
          />
          <div className="ms-2">
            <h6 className="mb-0">{user.name}</h6>
            <small className="text-muted">{time}</small>
          </div>
        </div>
        <div>⋮</div>
      </div>

      {/* Question */}
      <strong>{question}</strong>
      <p className="text-muted mt-2 mb-3">{body}</p>

      {/* Interaction Stats */}
      <InteractionBox reactions={reactions} comments={comments} likes={likes} />

      {/* Reactions Avatars */}

      {/* Comment Box */}
      <CommentBox
        avatar={user.avatar}
        type="text"
        className="form-control w-100 "
        placeholder="Add your comments"
      />
    </div>
  );
};

export default PostCard;
