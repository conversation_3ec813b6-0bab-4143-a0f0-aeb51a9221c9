import React from "react";
import MessageBubble from "./msgbubble";
import PostInput from "@/components/shared/inputs/postinput";
import ChatHeader from "./chatheader";


const messages = [
  {
    id: 1,
    text: "Hey.. <PERSON>, nice to meet you!",
    time: "9hr ago",
    type: "text",
    isMyMessage: true,
  },
  {
    id: 2,
    text: "Hope you’re doing fine.",
    time: "9hr ago",
    type: "text",
    isMyMessage: true,
  },
  {
    id: 3,
    text: "Distinctio architecto debitis...",
    time: "9hr ago",
    type: "text",
    isMyMessage: false,
  },
];
const ChatDetail = ({ messages ,selectedUser }) => {
    
  return (
    <div className="chat-detail">
         <ChatHeader user={selectedUser} />
      <div className="chat-area">
        {messages.map((msg, idx) => (
          <MessageBubble key={idx} msg={msg} />
        ))}
      </div>
      <div className="chat-send-area">
        <PostInput button={<img src="/assets/img/chat-send-icon.png" />} />
      </div>
    </div>
  );
};

export default ChatDetail;
