import React from "react";
import { Checkbox, Form } from 'antd';

const CheckBoxInput = ({
  name,
  label,
  rules,
  handlecheckbox,
  placeholder,
  disabled,
  checked,
  className,
  options = [],
  ...props
}) => {
  // If options are provided, render multiple checkboxes
  if (options && options.length > 0) {
    return (
      <div>
        {label && (
          <div className="form-item-label">
            <label>{label}</label>
          </div>
        )}
        <Form.Item name={name} rules={rules} valuePropName="checked">
          <Checkbox.Group>
            {options.map((option, index) => (
              <Checkbox 
                key={index} 
                value={option.value}
                disabled={disabled}
                className={className}
              >
                {option.label}
              </Checkbox>
            ))}
          </Checkbox.Group>
        </Form.Item>
      </div>
    );
  }

  // Single checkbox
  return (
    <div>
      {label && (
        <div className="form-item-label">
          <label>{label}</label>
        </div>
      )}
      <Form.Item name={name} rules={rules} valuePropName="checked">
        <Checkbox
          disabled={disabled}
          className={className}
          onChange={handlecheckbox}
          {...props}
        >
          {placeholder}
        </Checkbox>
      </Form.Item>
    </div>
  );
};

export default CheckBoxInput;