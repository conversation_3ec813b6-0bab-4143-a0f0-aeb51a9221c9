import React from "react";
import { Badge } from "antd";

const ChatHeader = ({ user = {} }) => {
  const {
    name = "Alene",
    avatar = "/assets/img/avatar-1.png",
    statusText = "Offline",
    onlineStatus = "offline",
  } = user;

  return (
    <div className="detail-header d-flex align-items-center justify-content-between">
      <div className="d-flex align-items-center">
        <div>
          <img src="/assets/img/chat-toogle.png" alt="Toggle" />
        </div>
        <div className="d-flex align-items-center ms-3">
          <div className="chat-user-img">
            <Badge
              color={
                onlineStatus === "online"
                  ? "green"
                  : onlineStatus === "offline"
                  ? "gray"
                  : "red"
              }
              dot
            >
              <img src={avatar} alt="User Avatar" />
            </Badge>
          </div>
          <div className="ms-2">
            <p>{name}</p>
            <p className="color-light">{statusText}</p>
          </div>
        </div>
      </div>
      <div className="d-flex align-items-center">
        {["chat-1", "chat-2", "chat-3", "chat-4"].map((icon, i) => (
          <div className="ms-3" key={i}>
            <img src={`/assets/img/${icon}.png`} alt={`Icon ${i}`} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default ChatHeader;
