/* Payment Form Styles */
.payment-form-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 30px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.payment-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.payment-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.selected-plan-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-top: 15px;
}

.selected-plan-info p {
  margin: 5px 0;
  font-size: 14px;
  color: #666;
}

/* Form Sections */
.billing-section,
.card-section,
.address-section {
  margin-bottom: 30px;
}

.billing-section h4,
.card-section h4,
.address-section h4 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

/* Stripe Card Element Styles */
.stripe-card-element {
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  transition: border-color 0.3s ease;
  min-height: 50px;
  display: flex;
  align-items: center;
}

.stripe-card-element:hover {
  border-color: #40a9ff;
}

.stripe-card-element:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Form Actions */
.form-actions {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #d9d9d9;
  width: 100%;

}

.cancel-btn:hover {
  background-color: #e6e6e6;
  border-color: #bfbfbf;
}

.payment-btn {
  background-color: #52c41a;
  color: white;
  border: none;
  width: 100%;
}

.payment-btn:hover {
  background-color: #389e0d;
}

.payment-btn:disabled {
  background-color: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-form-container {
    padding: 20px;
    margin: 10px;
  }
  
  .form-actions .col-12 {
    margin-bottom: 10px;
  }
  
  .cancel-btn,
  .payment-btn {
    width: 100%;
  }
}

/* Security Badge */
.security-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  padding: 15px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  font-size: 12px;
  color: #52c41a;
}

.security-info::before {
  content: "🔒";
  margin-right: 8px;
}

/* Error States */
.ant-form-item-has-error .stripe-card-element {
  border-color: #ff4d4f;
}

.ant-form-item-has-error .stripe-card-element:focus-within {
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* Loading State */
.payment-form.loading {
  pointer-events: none;
  opacity: 0.7;
}

/* Success Animation */
@keyframes checkmark {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.payment-success {
  text-align: center;
  padding: 40px;
}

.payment-success .checkmark {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #52c41a;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 24px;
  animation: checkmark 0.6s ease-in-out;
}