import React from "react";
import { Badge } from "antd";
import PropTypes from "prop-types";

const getStatusColor = (status) => {
  switch (status) {
    case "online":
      return "#52c41a"; // green
    case "offline":
      return "#d9d9d9"; // gray
    case "away":
      return "#f5222d"; // red
    default:
      return "#d9d9d9";
  }
};

const getMessageIcon = (status) => {
  switch (status) {
    case "sent":
      return <img src="/assets/img/send-icon.png" alt="sent" />;
    case "viewed":
      return <img src="/assets/img/viewed-msg.png" alt="viewed" />;
    case "received":
      return <p className="msg-receive"></p>;
    default:
      return null;
  }
};

const ChatUserList = ({ name, message, isTyping, msgTime, msgStatus, onlineStatus, avatar }) => {
  return (
    <div className="chat-user">
      <div className="chat-user-img">
        <Badge 
          color={getStatusColor(onlineStatus)} 
          dot
        >
          <img src={avatar} alt={name} />
        </Badge>
      </div>

      <div className="chat-user-msg-area ms-3 d-flex justify-content-between w-100">
        <div>
          <p className="font-16 color-black">{name}</p>
          <p className="color-light font-14">
            {isTyping ? <em>Typing...</em> : message}
          </p>
        </div>

        <div className="text-end">
          <p className="font-12 color-gray">{msgTime}</p>
          <div className="text-end">{getMessageIcon(msgStatus)}</div>
        </div>
      </div>
    </div>
  );
};

ChatUserList.propTypes = {
  name: PropTypes.string.isRequired,
  message: PropTypes.string,
  isTyping: PropTypes.bool,
  msgTime: PropTypes.string,
  msgStatus: PropTypes.oneOf(["sent", "received", "viewed"]),
  onlineStatus: PropTypes.oneOf(["online", "offline", "away"]),
  avatar: PropTypes.string.isRequired
};

ChatUserList.defaultProps = {
  message: "",
  isTyping: false,
  msgTime: "",
  msgStatus: "sent",
  onlineStatus: "offline"
};

export default ChatUserList;
