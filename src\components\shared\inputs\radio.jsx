import React from "react";
import { Form, Radio } from "antd";

const RadioInput = ({
  name,
  label,
  rules,
  options = [],
  className,
  disabled,
  onChange,
  initialValue, // Add initialValue prop
}) => {
  return (
    <Form.Item
      label={label}
      name={name}
      rules={rules}
      className="d-block"
      validateTrigger="onBlur"
      initialValue={initialValue} // Use initialValue instead of defaultValue
    >
      <Radio.Group
        className={className}
        disabled={disabled}
        onChange={onChange}
        options={options}
        // Remove defaultValue - let Form.Item handle initial values
      />
    </Form.Item>
  );
};

export default RadioInput;
