import{j as o}from"./index-3mE9H3a0.js";import{C as i}from"./contentblock-BFnsW9IO.js";import{I as r}from"./index-C-I6oq0X.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";import"./index-BUUUOhAK.js";const p=()=>{const e=["What is a privacy policy?","A privacy policy is a legal document where you disclose what data you collect from users...","Is the privacy policy generator free to use?","The Privacy Policy Generator (privacypolicygenerator.info) is a free generator of privacy policies for websites, apps & Facebook pages/app. You can use our free generator to create the privacy policy for your business.","Why is a privacy policy important?","The most important thing to remember is that a privacy policy is required by law if you collect data from users, either directly or indirectly. For example, if you have a contact form on your website you need a privacy policy. But you will also need a privacy policy if you use analytics tools such as Google Analytics.","Where do I put my privacy policy?","Usually, you can find privacy policies in the footer of a website. We recommend that you place your privacy policy in easy to find locations on your website."];return o.jsx(r,{children:o.jsx("div",{className:"container-fluid mt-4",children:o.jsx(i,{title:"Terms And Conditions",content:e})})})};export{p as default};
