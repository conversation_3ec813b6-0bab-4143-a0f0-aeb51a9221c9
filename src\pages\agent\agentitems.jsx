import React from "react";
import SparePost from "@/components/partial/spareit/sparepost";
import PropertyCard from "@/components/shared/card/propertycard";

const posts = [
  {
    id: 1,
    user: {
      name: "Anonymous 1",
      avatar: "/assets/img/avatar-1.png",
      time: "12 hours ago",
    },

    body: "Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmo tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
    images: [
      "/assets/img/image-1.png",
      "/assets/img/image-2.png",
      "/assets/img/image-3.png",
      "/assets/img/image-4.png",
      "/assets/img/image-5.png",
      "/assets/img/image-6.png",
      "/assets/img/image-7.png",
    ],

    likes: 256,
    repost: 10,
    shares: 10,
    comments: 30,
    //    user_comment: [
    //       {
    //         isReply: false,
    //         name: "Anonymous 2",
    //         avatar: "/assets/img/avatar-1.png",
    //         time: "12 hours ago",
    //         mycomment:
    //           "Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
    //         commentlink: "www.loremisume.com",
    //         likes: 500,
    //         shares: 500,
    //       },
    //       {
    //         isReply: true,
    //         name: "Anonymous 3",
    //         avatar: "/assets/img/avatar-1.png",
    //         time: "10 hours ago",
    //         mycomment:
    //           "This is a reply to the previous comment. Great post!",
    //         commentlink: "www.replylink.com",
    //         likes: 50,
    //         shares: 5,
    //       },
    //        {
    //         isReply: false,
    //         name: "Anonymous 2",
    //         avatar: "/assets/img/avatar-1.png",
    //         time: "12 hours ago",
    //         mycomment:
    //           "Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
    //         commentlink: "www.loremisume.com",
    //         likes: 500,
    //         shares: 500,
    //       },
    //     ],

    reactions: [
      "/assets/img/avatar-1.png",
      "/assets/img/avatar-1.png",
      "/assets/img/avatar-1.png",
      "/assets/img/avatar-1.png",
      "/assets/img/avatar-1.png",
    ],
  },
];
const dummyProperties = [
  {
    title: "Modern 2-Bedroom |",
    location: "Downtown, Dubai",
    price: "$3,500,000",
    detail: "Stunning view with spacious layout. ",
    bath_tub: 2,
    bed_room: 2,
    square_feet: "220 Sq. Yd",
  },
  {
    title: "Luxury Villa |",
    location: "Palm Jumeirah",
    price: "$8,900,000",
    detail: "Exclusive beachfront property. ",
    bath_tub: 4,
    bed_room: 5,
    square_feet: "500 Sq. Yd",
  },
  {
    title: "Modern 2-Bedroom |",
    location: "Downtown, Dubai",
    price: "$3,500,000",
    detail: "Stunning view with spacious layout. ",
    bath_tub: 2,
    bed_room: 2,
    square_feet: "220 Sq. Yd",
  },
  {
    title: "Luxury Villa |",
    location: "Palm Jumeirah",
    price: "$8,900,000",
    detail: "Exclusive beachfront property. ",
    bath_tub: 4,
    bed_room: 5,
    square_feet: "500 Sq. Yd",
  },
  // Add more as needed
];
const AgentItems = ({ type }) => {
  return (
    <div className="">
      {type === "posts" && (
        <>
          {posts.map((items) => (
            <div key={items.id} className="col-12 mt-4">
              <SparePost {...items} />
            </div>
          ))}
        </>
      )}
      {type === "listing" && (
        <div className="row mt-4">
          {dummyProperties.map((item, index) => (
            <div key={index} className="col-12 col-sm-6 col-md-4 col-lg-3">
              <PropertyCard {...item} />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AgentItems;
