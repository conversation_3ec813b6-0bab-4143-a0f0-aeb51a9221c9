import{r as y,u as P,R as M,j as e,_ as A}from"./index-3mE9H3a0.js";import{I as F,u as E}from"./index-C-I6oq0X.js";import{P as k}from"./propertycard-DcHEKJZy.js";import{S as z}from"./searchbar-Dn-5q6-d.js";import{I as $}from"./iconbutton-DFqB2BBL.js";import{C as D}from"./index-kHmX87X2.js";import{B as s,a as f}from"./index-Cd-Wothc.js";import{F as S}from"./flatbutton-Yo0mdDJ8.js";import{u as I,F as T,E as L,R}from"./useSearchFilterPagination-BF2wgsiQ.js";import{u as q}from"./useMutation-lvneVzbk.js";import{S as O}from"./Skeleton-BqfVCYaM.js";import{F as Y}from"./react-stripe.esm-CaXK0k-R.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";import"./index-BUUUOhAK.js";import"./DeleteOutlined-DCDN0YWx.js";import"./index-KeJPQTWG.js";import"./index-DX-6A722.js";import"./fade-yKzH711D.js";import"./useLocationData-QmC5yoKM.js";import"./useQuery-Bzo2W4ue.js";import"./languageUtils-BKYM3hOY.js";const H=(l,a=2,t=8)=>{if(!l)return"No description available";const r=l.split(" "),i=a*t;return r.length<=i?l:r.slice(0,i).join(" ")+"..."},o=l=>{if(!l)return l;const a=parseFloat(l);return isNaN(a)?l:Math.floor(a)},U=(l,a={})=>{var x,j,h,p,d,g;if(!l)return null;const{showActions:t=!1,onEdit:r=null,onDelete:i=null}=a,n=window.user||{},u=l.user_id===n.id||((x=l.user)==null?void 0:x.id)===n.id||l.created_by===n.id,m=t&&u,N=l.images&&l.images.length>0?l.images[0].url:"/assets/img/property-placeholder.png",v=H(l.description,2,8);return{id:l.id,slug:l.slug,title:l.name||`${o(l.bed)} Bed ${o(l.bath)} Bath ${((j=l.type)==null?void 0:j.name)||"Property"}`,location:`${l.city}, ${l.state}`,price:`${o(l.price).toLocaleString()}`,detail:v,bath_tub:o(l.bath),bed_room:o(l.bed),square_feet:`${o(l.size)} ${l.size_type||"sqft"}`,src:`/listing/detail/${l.id}`,image:N,garage:o(l.garage),year_built:o(l.year_built),basement:l.basement,hoa:l.hoa?l.hoa_price?o(l.hoa_price):!0:!1,mls_id:l.mls_id,property_type:(h=l.type)==null?void 0:h.name,home_style:(p=l.home_style)==null?void 0:p.name,agent:(d=l.user)==null?void 0:d.name,agent_phone:l.mobile_no,is_favorite:l.is_favorite,showActions:m,onEdit:m?r:null,onDelete:m?i:null,isOwner:u,currentUserId:n.id,propertyUserId:l.user_id||((g=l.user)==null?void 0:g.id)||l.created_by}},G=(l,a={})=>Array.isArray(l)?l.map(t=>U(t,a)).filter(Boolean):[],je=()=>{const[l,a]=y.useState(!1),t=P(),{showAlert:r}=E(),{data:i,isLoading:n,pagination:u,handlePageChange:m,handleFilterClick:N}=I("properties",{pageSize:12,staleTime:0}),{mutate:v}=q("deleteProperty",{showSuccessNotification:!0,invalidateQueries:[{queryKey:["properties"]},{queryKey:["getProperty"]},{queryKey:["getUser"]}],onSuccess:async()=>{console.log("Delete property success, invalidating queries...")}}),x=c=>{t(`/listing/add/${c.id}`)},j=async c=>{(await r({title:"Are you sure?",text:"Do you want to delete this property",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",cancelButtonText:"Cancel"})).isConfirmed&&v({slug:c.id,data:""})},h=M.useMemo(()=>i!=null&&i.data?G(i.data,{showActions:!0,onEdit:x,onDelete:j}):[],[i]),p=[{name:"state",label:"State",type:"select",placeholder:"Select State"},{name:"city",label:"City",type:"select",placeholder:"Select City"},{name:"type_id",label:"Property Type",type:"select",placeholder:"Select Property Type"},{name:"home_style_id",label:"Home Style",type:"select",placeholder:"Select Home Style"},{name:"min_price",label:"Min Price",type:"input",placeholder:"Min Price"},{name:"max_price",label:"Max Price",type:"input",placeholder:"Max Price"},{name:"bed",label:"Bedrooms",type:"select",placeholder:"Select Bedrooms"},{name:"bath",label:"Bathrooms",type:"select",placeholder:"Select Bathrooms"},{name:"basement",label:"Basement",type:"radio"}],d=()=>{a(!1)},g=()=>{a(!1)},[w,C]=y.useState(null),_=c=>{console.log("working")},B=c=>{C(c.target.value),console.log("Selected:",c.target.value)};return e.jsxs(F,{children:[e.jsxs("div",{className:"container-fluid",children:[e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12",children:e.jsx(z,{onFilterClick:N})}),e.jsx("div",{className:"col-12 mt-3",children:e.jsx($,{icon:e.jsx("img",{src:"/assets/img/addlist-icon.png"}),title:"Add Listing",className:"blue-btn",onClick:()=>t("/listing/add")})})]}),e.jsx(T,{fields:p}),e.jsx("div",{className:"row mt-3",children:n?Array.from({length:12}).map((c,b)=>e.jsx("div",{className:"col-12 col-sm-6 col-md-4 col-lg-3",children:e.jsx(O,{active:!0,paragraph:{rows:4}})},b)):A.isEmpty(h)?e.jsx(L,{title:"No properties found",description:"No properties available at the moment"}):h.map((c,b)=>e.jsx("div",{className:"col-12 col-sm-6 col-md-4 col-lg-3",children:e.jsx(k,{...c})},c.id||b))}),e.jsx(R,{pagination:u,handlePageChange:m,isLoading:n,itemName:"properties",pageSizeOptions:["12","24","48"]})]}),e.jsx(D,{title:"Search Listings",open:l,onOk:d,onCancel:g,width:1100,footer:!1,children:e.jsxs(Y,{name:"login",layout:"vertical",onFinish:_,initialValues:{remember:!0},autoComplete:"off",className:"add-listing",children:[e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(s,{name:"State",placeholder:"Select State",label:"State",type:"select"})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(s,{name:"City",placeholder:"Select City",label:"City",type:"select"})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(s,{name:"size",placeholder:"Min",label:"Lot Size"})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(s,{name:"",placeholder:"Max. ",label:" "})})]})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(s,{name:"minbed",placeholder:"Min Beds",label:"Beds"})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(s,{name:"Beds",placeholder:"Max. Beds",label:" "})})]})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(s,{name:"price",placeholder:"Min Price",label:"Price"})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(s,{name:"maxprice",placeholder:"Max. Price",label:" "})})]})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(s,{name:"Style",placeholder:"Choose Home Style",label:"Home Style",type:"select"})}),e.jsxs("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:[e.jsx("label",{className:"d-block mt-3 mb-2",children:"Basement"}),e.jsxs(f.Group,{onChange:B,value:w,children:[e.jsx(f,{value:!0,children:"Yes"}),e.jsx(f,{value:!1,children:"No"})]})]}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(s,{name:"baths",placeholder:"Min Baths",label:"Baths"})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(s,{name:"Baths",placeholder:"Max. Baths",label:" "})})]})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(s,{name:"Type",placeholder:"Enter Property Type",label:"Property Type",type:"select"})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(s,{name:"Built",placeholder:"Min Year",label:"Year Built"})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(s,{name:"yeare",placeholder:"Max. Year",label:" "})})]})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(s,{name:"Property",placeholder:"Min Size",label:"Property Size"})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(s,{name:"Size",placeholder:"Max. Size",label:" "})})]})}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(s,{name:"Code",placeholder:"Enter Zip Code",label:"Zip Code"})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(s,{name:"Garage",placeholder:"Min Cars",label:"Garage"})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(s,{name:"Cars",placeholder:"Max. Cars",label:" "})})]})})]})]}),e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12 mt-3 mb-3 text-end",children:[e.jsx(S,{title:"Cancel",className:"gray-btn",onClick:d}),e.jsx(S,{title:"Apply",className:"blue-btn ms-3 ",onClick:d})]})})]})})]})};export{je as default};
