button:active{
    border: 1px solid transparent !important;
    background-color: transparent;
    color: #000 !important;
}
button{
   width: 100%;
  border: 0;
  background-color:transparent;
  font-size: 16px;
  position: relative;
  z-index: 1;
  overflow: hidden;
  cursor: pointer;  
}
button::before {
  position: absolute;
  content: "";
  background-color: rgba(12, 92, 171, 1);
  left: 0;
  top: 0;
  width: 0;
  height: 100%;
  border-radius: 8px;
  z-index: 0;
  transition: width 0.4s ease;
}

button:hover::before {
  width: 100%;
  color: #fff !important;
   border: 0;
}
button:hover{
    border: 1px solid transparent !important;
    background-color: transparent;
}
button:hover span{
    color: #fff !important;
}
button span {
  position: relative;
  z-index: 1;
  
}
/* Remove custom effects only from specific Ant Design buttons */
.ant-picker-header-next-btn,
.ant-picker-header-super-prev-btn,
.ant-picker-header-super-next-btn,
.ant-picker-month-btn,
.ant-picker-year-btn {
  all: unset;
  cursor: pointer; /* optional: keep pointer cursor */
}

/* Remove pseudo effect */
.ant-picker-header-next-btn::before,
.ant-picker-header-super-prev-btn::before,
.ant-picker-header-super-next-btn::before,
.ant-picker-month-btn::before,
.ant-picker-year-btn::before {
  display: none !important;
}

/* Reset core styles */
.ant-picker-header-next-btn,
.ant-picker-header-super-prev-btn,
.ant-picker-header-super-next-btn,
.ant-picker-month-btn,
.ant-picker-year-btn {
  background-color: transparent !important;
  border: none !important;
  color: inherit !important;
  width: auto;
  font-size: inherit;
  z-index: auto;
  position: static;
}

.EmojiPickerReact button::before{
  content: none !important;
}
.post-btn{
    width: fit-content;
    background-color: #3883E2 !important;
    padding: 20px 20px;
    color: #fff;
}
.gray-btn{
  background-color: transparent !important;
    border: 1px solid #D9D9D9;
    width: fit-content;
    padding: 10px 20px;
    color: #262626 !important;
    height: 38px;
}
.blue-btn{
    background-color: #3883E2 !important;
    border: 1px solid #3883E2;
    width: fit-content;
    padding: 10px 20px;
    color: #fff !important;
    height: 38px; 
}
.browse-file{
    background-color: transparent !important;
    border: 1px solid #3883E2;
    width: fit-content;
    padding: 10px 20px;
    color: #fff !important;
    height: 38px; 
    color: #3883E2 !important;
}