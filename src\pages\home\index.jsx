import React, { memo } from "react";
import InnerLayout from "@/components/shared/layout/innerlayout";
import BaseInput from "@/components/shared/inputs";
import IconButton from "@/components/shared/button/iconbutton";
import { SearchOutlined } from "@ant-design/icons";
import PropertyCard from "@/components/shared/card/propertycard";
import PostCard from "@/components/shared/card/postcard";
import { Link } from "react-router-dom";

const dummyProperties = [
  {
    title: "Modern 2-Bedroom |",
    location: "Downtown, Dubai",
    price: "$3,500,000",
    detail: "Stunning view with spacious layout. ",
    bath_tub: 2,
    bed_room: 2,
    square_feet: "220 Sq. Yd",
  },
  {
    title: "Luxury Villa |",
    location: "Palm Jumeirah",
    price: "$8,900,000",
    detail: "Exclusive beachfront property. ",
    bath_tub: 4,
    bed_room: 5,
    square_feet: "500 Sq. Yd",
  },
  {
    title: "Modern 2-Bedroom |",
    location: "Downtown, Dubai",
    price: "$3,500,000",
    detail: "Stunning view with spacious layout. ",
    bath_tub: 2,
    bed_room: 2,
    square_feet: "220 Sq. Yd",
  },
  {
    title: "Luxury Villa |",
    location: "Palm Jumeirah",
    price: "$8,900,000",
    detail: "Exclusive beachfront property. ",
    bath_tub: 4,
    bed_room: 5,
    square_feet: "500 Sq. Yd",
  },
  // Add more as needed
];

const posts = [
  {
    id: 1,
    user: { name: "Anonymous 1", avatar: "/assets/img/avatar-1.png" },
    time: "12 hours ago",
    question: "Do buildings have windows?",
    body: "I'm like really really confused. like do buildings have windows been trying to figure out for sooo long...",
    likes: 256,
    comments: 10,
    shares: 10,
    reactions: [
      "/assets/img/avatar-1.png",
      "/assets/img/avatar-1.png",
      "/assets/img/avatar-1.png",
      "/assets/img/avatar-1.png",
      "/assets/img/avatar-1.png",
    ],
  },
  {
    id: 2,
    user: { name: "User 2", avatar: "/assets/img/avatar-1.png" },
    time: "1 day ago",
    question: "Why is the sky blue?",
    body: "I've always wondered this. Anyone knows the science behind it?",
    likes: 120,
    comments: 5,
    shares: 3,
    reactions: [
      "/assets/img/avatar1-.png",
      "/assets/img/avatar-1.png",
      "/assets/img/avatar-1.png",
    ],
  },
  {
    id: 3,
    user: { name: "User 2", avatar: "/assets/img/avatar-1.png" },
    time: "1 day ago",
    question: "Why is the sky blue?",
    body: "I've always wondered this. Anyone knows the science behind it?",
    likes: 120,
    comments: 5,
    shares: 3,
    reactions: [
      "/assets/img/avatar1-.png",
      "/assets/img/avatar-1.png",
      "/assets/img/avatar-1.png",
    ],
  },
  {
    id: 4,
    user: { name: "User 2", avatar: "/assets/img/avatar-1.png" },
    time: "1 day ago",
    question: "Why is the sky blue?",
    body: "I've always wondered this. Anyone knows the science behind it?",
    likes: 120,
    comments: 5,
    shares: 3,
    reactions: [
      "/assets/img/avatar1-.png",
      "/assets/img/avatar-1.png",
      "/assets/img/avatar-1.png",
    ],
  },
  // Add more posts as needed...
];

const Home = () => {
  return (
    <InnerLayout>
      <div className="container-fluid mt-4">
        <div className="row justify-content-center">
          <div className="col-12 col-md-10 col-lg-10">
            <div className="inputs-area">
              <div className="row justify-content-center">
                <div className="col-12 col-md-8 col-lg-8 col-xl-9">
                  <div className="sreach">
                    <BaseInput
                      name="name"
                      placeholder="e.g John Doe"
                      label=""
                      className="search-input"
                      required
                      suffix={
                        <IconButton
                          title="Find Property"
                          icon={<SearchOutlined />}
                        />
                      }
                    />
                    <div></div>
                  </div>
                </div>
              </div>
              <div className="bg-blue">
                <div className="row">
                  <div className="col">
                    <BaseInput
                      name="name"
                      placeholder="Location"
                      label=""
                      className="search-input"
                      required
                      type="select"
                      prefix={<img src="/assets/img/location-icon.png" />}
                    />
                  </div>
                  <div className="col">
                    <BaseInput
                      name="name"
                      placeholder="Property Type"
                      label=""
                      className="search-input"
                      required
                      type="select"
                      prefix={<img src="/assets/img/property-icon.png" />}
                    />
                  </div>
                  <div className="col">
                    <BaseInput
                      name="name"
                      placeholder="Pricing Range"
                      label=""
                      className="search-input"
                      required
                      type="select"
                      prefix={<img src="/assets/img/price-icon.png" />}
                    />
                  </div>
                  <div className="col">
                    <BaseInput
                      name="name"
                      placeholder="Property Size"
                      label=""
                      className="search-input"
                      required
                      type="select"
                      prefix={<img src="/assets/img/box-icon.png" />}
                    />
                  </div>
                  <div className="col">
                    <BaseInput
                      name="name"
                      placeholder="Build Year"
                      label=""
                      className="search-input"
                      required
                      type="select"
                      prefix={<img src="/assets/img/calender-icon.png" />}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="row mt-5">
          <div className="col-12 w-100">
            <img
              src="/assets/img/banner-img.png"
              alt=""
              className="img-fluid w-100"
            />
          </div>
        </div>

        <div className="row">
          <div className="col-12">
            <div className="d-flex align-items-center justify-content-between">
              <div>
                <h4 className="mt-4 mb-3">Recent Posts</h4>
              </div>
              <div>
                <Link to="/sphare-it" className="font-18">
                  See More
                </Link>
              </div>
            </div>
          </div>

          {dummyProperties.map((item, index) => (
            <div key={index} className="col-12 col-sm-6 col-md-4 col-lg-3">
              <PropertyCard {...item} />
            </div>
          ))}
        </div>
        <div className="row">
          <div className="col-12">
            <div className="d-flex align-items-center justify-content-between">
              <div>
                <h4 className="mt-4 mb-3">Recent Properties</h4>
              </div>
              <div>
                <Link to="/listing" className="font-18">
                  See More
                </Link>
              </div>
            </div>
          </div>

          {dummyProperties.map((item, index) => (
            <div key={index} className="col-12 col-sm-6 col-md-4 col-lg-3">
              <PropertyCard {...item} />
            </div>
          ))}
        </div>
        <div className="row">
          <div className="col-12">
            <h4 className="mt-4 mb-3">Recent Questions</h4>
          </div>

          {posts.map((items) => (
            <div
              key={items.id}
              className="col-12 col-md-6 col-lg-4 col-xl-3 mb-4"
            >
              <PostCard {...items} />
            </div>
          ))}
        </div>
        <div className="row mt-4 mb-5">
          <div className="col-12 w-100">
            <img
              src="/assets/img/home-img.png"
              alt=""
              className="img-fluid w-100"
            />
          </div>
        </div>
      </div>
    </InnerLayout>
  );
};

export default memo(Home);
