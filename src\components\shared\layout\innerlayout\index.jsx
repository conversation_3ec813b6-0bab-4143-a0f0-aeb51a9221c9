import React from "react";
import { Layout, theme } from "antd";
const { Content } = Layout;
import InnerHeader from "./innerheader";

const InnerLayout = ({ children }) => {
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  return (
    <Layout>
      <Layout>
        <InnerHeader />
        <Content>{children}</Content>
      </Layout>
    </Layout>
  );
};

export default InnerLayout;
