import React, { memo, useState, useEffect } from "react";
import AuthLayout from "@/components/shared/layout/authlayout";
import BaseInput from "@/components/shared/inputs/index";
import FlatButton from "@/components/shared/button/flatbutton";
import { Checkbox, Form } from "antd";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useMutation } from "@/hooks/reactQuery";
import { combineRules, validations } from "@/config/rules";
import Helper from "@/helpers";

const Login = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [form] = Form.useForm();
  const [rememberMe, setRememberMe] = useState(false);
  const [initialValues, setInitialValues] = useState({});

  useEffect(() => {
    const loadSavedCredentials = async () => {
      try {
        const savedCredentials = await Helper.getStorageData(
          "rememberedCredentials"
        );

        if (savedCredentials && savedCredentials.email) {
          const values = {
            email: savedCredentials.email,
            password: savedCredentials.password || "",
          };

          setInitialValues(values);
          form.setFieldsValue(values);
          setRememberMe(true);
        }
      } catch (error) {
      }
    };

    loadSavedCredentials();
  }, [form]);

  const { mutate, isPending } = useMutation("login", {
    useFormData: false,
    onSuccess: async (data) => {
      if (data) {
        await Helper.setStorageData("session", data.data);
        window.user = data.data;

        const formValues = form.getFieldsValue();
        if (rememberMe) {
          await Helper.setStorageData("rememberedCredentials", {
            email: formValues.email,
            password: formValues.password,
            rememberMe: true,
            savedAt: Date.now(),
          });
        } else {
          localStorage.removeItem("rememberedCredentials");
        }

        navigate("/home", { replace: true });
      }
    },
  });

  const onFinish = (values) => {
    const transformedData = {
      ...values,
      device: "web",
      device_token: "web-token-" + Date.now(),
    };
    mutate(transformedData);
  };

  const onRememberMeChange = (e) => {
    const checked = e.target.checked;
    setRememberMe(checked);

    if (!checked) {
      localStorage.removeItem("rememberedCredentials");
    }
  };

  return (
    <AuthLayout showSidebar={false}>
      <div className="text-center logo mb-3">
        <img src="/assets/img/suscribtion-img.png" alt="Auth Logo" />
      </div>
      <div className="col-12 text-center">
        <h1 className="font-46 color-black mb-1">SPHERE</h1>
        <h1 className="font-46 color-black mb-1">Real Estate Network</h1>
        <p>Log in to continue using your account</p>
      </div>
      <Form
        form={form}
        name="login"
        layout="vertical"
        onFinish={onFinish}
        initialValues={initialValues}
        autoComplete="off"
      >
        <BaseInput
          name="email"
          placeholder="Email"
          label="Email Address"
          rules={combineRules("email", validations.required, validations.email)}
        />
        <BaseInput
          type="password"
          name="password"
          placeholder="Password"
          label="Password"
          rules={combineRules(
            "password",
            validations.required,
            validations.password
          )}
        />
        <div className="d-flex align-items-center justify-content-between mt-0">
          <div className="mt-2 check-item">
            <Checkbox checked={rememberMe} onChange={onRememberMeChange}>
              Remember me
            </Checkbox>
          </div>
          <div>
            <Link
              to="/forgetpassword"
              className="mt-1 font-600 font-14 d-block color-blue "
            >
              Forgot Password?
            </Link>
          </div>
        </div>
        <div>
          <FlatButton
            title={isPending ? "Sign In..." : "Sign In"}
            className="mx-auto mt-4 signin-btn mt-5"
            htmlType="submit"
            loading={isPending}
            disabled={isPending}
          />
        </div>
        <div className="login-p">
          <p>Or login with</p>
        </div>
        <div className="socail-login">
          <div>
            <img src="/assets/img/google.png" alt="" />
          </div>
          <div>
            <p>Sign in with Google</p>
          </div>
        </div>
        <div>
          <p className="signup-text">
            Don't have an account?
            <Link to="/signup" className="color-blue font-600 font-16 ms-1">
              Sign up
            </Link>
          </p>
        </div>
      </Form>
    </AuthLayout>
  );
};

export default memo(Login);