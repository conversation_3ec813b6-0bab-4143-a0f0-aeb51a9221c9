import React from "react";
import { useParams } from "react-router-dom";
import InnerLayout from "../../components/shared/layout/innerlayout";
import SubscriptionScreen from "../../components/partial/subscription/subscriptionscreen";

const Subscription = () => {
  const { token } = useParams(); // Get token from URL

  // If there's a token in the URL, don't show the inner layout (standalone subscription page)
  if (token) {
    return (
      <div className="container-fluid">
        <div className="row gx-0">
          <div className="col-12">
            <SubscriptionScreen />
          </div>
        </div>
      </div>
    );
  }

  // If no token, show with inner layout (authenticated user accessing subscription page)
  return (
    <InnerLayout>
      <div className="container-fluid">
        <div className="row gx-0">
          <div className="col-12">
            <SubscriptionScreen />
          </div>
        </div>
      </div>
    </InnerLayout>
  );
};

export default Subscription;
