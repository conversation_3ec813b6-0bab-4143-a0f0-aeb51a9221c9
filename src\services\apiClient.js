import api from "./api";
import { notification } from "antd";
import Swal from "sweetalert2";
import "@/hooks/sweetalert.css";

const apiClient = {
  async fetchApi(url, options = {}, endpoint = "", authToken = null) {
    const {
      data,
      useFormData = false,
      customHeaders = {},
      ...fetchOptions
    } = options;

    const headers = new Headers();

    headers.append("Accept", "application/json");

    if (!useFormData && options.method !== "GET") {
      headers.append("Content-Type", "application/json");
    }

    const publicEndpoints = ["login", "signup", "forgotPassword"];

    const subscriptionEndpoints = [
      "createSubscription",
      "getCurrentSubscription",
      "cancelSubscription",
      "getSubscriptionProduct",
      "getUser",
    ];

    let tokenToUse = authToken;

    if (!tokenToUse && subscriptionEndpoints.includes(endpoint)) {
      const currentPath = window.location.pathname;
      const pathSegments = currentPath.split("/");

      if (pathSegments[1] === "subscription" && pathSegments[2]) {
        tokenToUse = pathSegments[2];
      }
    }

    if (tokenToUse && !publicEndpoints.includes(endpoint)) {
      headers.append("Authorization", `Bearer ${tokenToUse}`);
    } else if (
      window.user &&
      window.user.api_token &&
      !publicEndpoints.includes(endpoint)
    ) {
      headers.append("Authorization", `Bearer ${window.user.api_token}`);
    }

    Object.entries(customHeaders).forEach(([key, value]) => {
      headers.append(key, value);
    });

    const requestOptions = {
      ...fetchOptions,
      headers,
    };

    if (data && options.method !== "GET" && options.method !== "DELETE") {
      if (useFormData) {
        if (data instanceof FormData) {
          requestOptions.body = data;
        } else {
          const formData = new FormData();
          Object.entries(data).forEach(([key, value]) => {
            formData.append(key, value);
          });
          requestOptions.body = formData;
        }
      } else {
        requestOptions.body = JSON.stringify(data);
      }
    }

    return fetch(url, requestOptions);
  },

  async handleResponse(
    response,
    showSuccessNotification = false,
    endpoint = "",
    method = ""
  ) {
    const headers = {};
    for (const [key, value] of response.headers.entries()) {
      headers[key.toLowerCase()] = value;
    }
    const pagination = {
      currentPage: parseInt(headers["x-current-page"]),
      pageLimit: parseInt(headers["x-page-limit"]),
      totalCount: parseInt(headers["x-total-count"]),
      totalPages: parseInt(headers["x-total-pages"]),
    };

    const data = await response.json();

    if (response.ok) {
      const tokenUpdateEndpoints = ["signup", "login", "updateUser"];

      if (tokenUpdateEndpoints.includes(endpoint)) {
        const accessToken =
          headers["access_token"] ||
          headers["access-token"] ||
          headers["authorization"];

        if (accessToken) {
          const cleanToken = accessToken.replace(/^Bearer\s+/i, "");

          if (!data.access_token && !data.api_token) {
            data.api_token = cleanToken;
          }

          if (window.user) {
            window.user.api_token = cleanToken;

            if (window.helper && window.helper.setStorageData) {
              try {
                await window.helper.setStorageData("session", window.user);
              } catch (error) {}
            }
          }
        }
      }

      if (
        (method === "POST" ||
          method === "PUT" ||
          method === "PATCH" ||
          method === "DELETE") &&
        showSuccessNotification
      ) {
        const statusMessage = headers["x-status-message"] || data.message;

        if (statusMessage) {
          notification.success({
            message: "",
            description: statusMessage,
            duration: 4,
          });
        }
      }
      return { data, pagination };
    }

    if (response.status === 401) {
      const swalWithBootstrapButtons = Swal.mixin({
        customClass: {
          confirmButton: "custom-swal-confirm-btn",
          cancelButton: "custom-swal-cancel-btn",
          popup: "custom-swal-popup",
          title: "custom-swal-title",
          content: "custom-swal-content",
        },
      });

      await swalWithBootstrapButtons
        .fire({
          title: "Session Expired",
          text: "Your session has expired. Please log in again.",
          icon: "warning",
          confirmButtonText: "OK",
          allowOutsideClick: false,
          allowEscapeKey: false,
        })
        .then(() => {
          localStorage.removeItem("session");
          window.user = {};
          window.location.replace("/login");
        });
    } else if (response.status === 400 || response.status === 422) {
      if (data.message && Array.isArray(data.message)) {
        data.message.forEach((errorMessage, index) => {
          notification.error({
            message: "",
            description: errorMessage,
            duration: 4,
            key: `validation-error-${index}`,
          });
        });
      } else if (data.errors && typeof data.errors === "object") {
        Object.entries(data.errors).forEach(([field, message]) => {
          notification.error({
            message: "",
            description: `${field}: ${
              Array.isArray(message) ? message[0] : message
            }`,
            duration: 4,
          });
        });
      } else if (data.message && typeof data.message === "string") {
        notification.error({
          message: "",
          description: data.message,
          duration: 4,
        });
      } else if (Array.isArray(data)) {
        data.forEach((errorMessage, index) => {
          notification.error({
            message: "",
            description: errorMessage,
            duration: 4,
          });
        });
      }
    } else if (data.data) {
      if (typeof data.data === "object" && !Array.isArray(data.data)) {
        Object.entries(data.data).forEach(([field, message]) => {
          if (typeof message === "string") {
            notification.error({
              message: `Validation Errorr`,
              description: message,
              duration: 4,
            });
          } else if (Array.isArray(message)) {
            notification.error({
              message: `Error: ${field}`,
              description: message[0],
              duration: 4,
            });
          } else if (typeof message === "object") {
            notification.error({
              message: `Error: ${field}`,
              description: JSON.stringify(message),
              duration: 4,
            });
          }
        });
      } else if (typeof data.data === "string") {
        notification.error({
          message: "Error",
          description: data.data,
          duration: 4,
        });
      }
    } else {
      notification.error({
        message: "Error",
        description: data.message || "Something went wrong",
        duration: 4,
      });
    }

    throw { status: response.status, ...data };
  },

  getUrl(endpoint, params = {}, slug = "") {
    if (!api[endpoint]) {
      throw new Error(`API endpoint "${endpoint}" not found`);
    }

    const { url } = api[endpoint];
    const baseUrl =
      window.constants?.api_base_url ||
      "https://sphere-qa-89d7.up.railway.app/api/";

    let fullUrl = `${baseUrl}${url}`;

    if (slug) {
      fullUrl = `${fullUrl}/${slug}`;
    }

    if (params && Object.keys(params).length > 0) {
      const queryString = new URLSearchParams(params).toString();
      fullUrl += fullUrl.includes("?") ? `&${queryString}` : `?${queryString}`;
    }

    return fullUrl;
  },

  async request(endpoint, options = {}) {
    if (!api[endpoint]) {
      throw new Error(`API endpoint "${endpoint}" not found`);
    }

    const {
      params,
      slug,
      method: customMethod,
      showSuccessNotification = false,
      token: requestToken,
      ...fetchOptions
    } = options;
    const method = customMethod || api[endpoint].method;

    try {
      const url = this.getUrl(endpoint, params, slug);
      const response = await this.fetchApi(
        url,
        { method, ...fetchOptions },
        endpoint,
        requestToken
      );

      return this.handleResponse(
        response,
        showSuccessNotification,
        endpoint,
        method
      );
    } catch (error) {
      if (!error.status) {
        notification.error({
          message: "Network Error",
          description:
            error.message ||
            "Failed to connect to the server. Please check your internet connection.",
          duration: 6,
        });
      }

      throw error;
    }
  },
};

export default apiClient;
