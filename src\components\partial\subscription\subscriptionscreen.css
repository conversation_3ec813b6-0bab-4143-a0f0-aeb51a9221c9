.subscription-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.header .color-blue {
  text-decoration: none;
  color: #007bff;
  font-weight: 500;
}

.header .color-blue:hover {
  color: #0056b3;
  text-decoration: underline;
}

.header .logo {
  margin: 20px 0;
}

.header .logo img {
  max-width: 200px;
  height: auto;
}

.header h1 {
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
  margin: 20px 0 10px 0;
}

.header p {
  color: #6c757d;
  font-size: 16px;
  margin: 0;
}

.plan-options {
  margin-bottom: 40px;
}

.plan-options h2 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 30px;
  font-weight: 600;
}

.plans-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.plan-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.plan-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateY(-2px);
}

.subscription-status {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.status-label {
  background: #28a745;
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.plan-header h3 {
  color: #2c3e50;
  font-size: 24px;
  margin: 0 0 15px 0;
}

.plan-price {
  margin-bottom: 20px;
}

.plan-price .price {
  color: #007bff;
  font-size: 36px;
  font-weight: 700;
}

.plan-price .period {
  color: #6c757d;
  font-size: 16px;
  margin-left: 5px;
}

.plan-description {
  color: #6c757d;
  font-size: 14px;
  margin: 15px 0;
  line-height: 1.5;
}

.auto-renew-note {
  color: #6c757d;
  font-size: 12px;
  margin: 10px 0 20px 0;
  font-style: italic;
}

.signin-btn {
  background: #007bff !important;
  border-color: #007bff !important;
  color: white !important;
  width: 100%;
  height: 45px;
  font-weight: 600;
  border-radius: 6px;
}

.subscribe-btn:hover {
  background: #0056b3 !important;
  border-color: #004085 !important;
}

.cancel-btn {
  background: #dc3545 !important;
  border-color: #dc3545 !important;
  color: white !important;
  width: 100%;
  height: 45px;
  font-weight: 600;
  border-radius: 6px;
}

.cancel-subscription-btn:hover {
  background: #c82333 !important;
  border-color: #bd2130 !important;
}

.signin-btn:disabled,
.cancel-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.subscription-offer {
  text-align: center;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-top: 30px;
}

.subscription-offer p {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
}

.subscription-offer strong {
  color: #007bff;
}

/* Loading skeleton styles */
.plan-card .ant-skeleton {
  padding: 20px 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .subscription-container {
    padding: 15px;
  }
  
  .plans-container {
    grid-template-columns: 1fr;
  }
  
  .plan-card {
    padding: 20px;
  }
  
  .header h1 {
    font-size: 24px;
  }
  
  .plan-price .price {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .header .logo img {
    max-width: 150px;
  }
  
  .header h1 {
    font-size: 20px;
  }
  
  .plan-options h2 {
    font-size: 20px;
  }
}