import{u as n,c as m,r,j as s}from"./index-3mE9H3a0.js";import{I as o}from"./index-C-I6oq0X.js";import{I as a}from"./iconbutton-DFqB2BBL.js";import{B as d}from"./index-Cd-Wothc.js";import{F as l}from"./flatbutton-Yo0mdDJ8.js";import{A as x}from"./agentitems-pVIvr7TQ.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";import"./index-BUUUOhAK.js";import"./react-stripe.esm-CaXK0k-R.js";import"./sparepost-tyxuku36.js";import"./interactionbox-B9SrINfg.js";import"./index-kHmX87X2.js";import"./index-KeJPQTWG.js";import"./index-DX-6A722.js";import"./fade-yKzH711D.js";import"./Skeleton-BqfVCYaM.js";import"./propertycard-DcHEKJZy.js";import"./DeleteOutlined-DCDN0YWx.js";const L=()=>{const t=n(),{type:e}=m();r.useEffect(()=>{e||t("/agent/detail/posts",{replace:!0})},[e,t]);const i=c=>{t(`/agent/detail/${c}`)};return s.jsx(o,{children:s.jsx("div",{className:"container-fluid",children:s.jsxs("div",{className:"row",children:[s.jsx("div",{className:"col-12",children:s.jsxs("div",{className:"agent-box mt-5",children:[s.jsx("div",{className:"agent-header",children:s.jsx("img",{src:"/assets/img/home-img.png",alt:""})}),s.jsxs("div",{className:"agent-body d-flex align-items-center justify-content-between",children:[s.jsxs("div",{className:"d-flex align-items-center",children:[s.jsx("div",{className:"agent-profile",children:s.jsx("img",{src:"/assets/img/avatar-2.png",alt:""})}),s.jsxs("div",{className:"ms-3",children:[s.jsxs("div",{className:"d-flex",children:[s.jsx("p",{className:"me-2 font-600",children:"Stebin Ben"}),s.jsx("img",{src:"/assets/img/badge.png",alt:"",className:"img-fluid"})]}),s.jsx("p",{className:"color-light",children:"Real Estate Broker"}),s.jsxs("div",{className:"d-flex",children:[s.jsx("div",{children:s.jsx("img",{src:"/assets/img/location_on.png",alt:""})}),s.jsx("div",{children:s.jsx("p",{children:"Miami"})})]})]})]}),s.jsxs("div",{className:"d-flex align-items-center",children:[s.jsx("div",{className:"me-3",children:s.jsx(a,{icon:s.jsx("img",{src:"/assets/img/call-icon.png"}),title:"Call",className:"gray-btn"})}),s.jsx("div",{className:"me-3",children:s.jsx(a,{icon:s.jsx("img",{src:"/assets/img/message-icon.png"}),title:"Message",className:"gray-btn"})}),s.jsx("div",{className:"me-3",children:s.jsx(a,{icon:s.jsx("img",{src:"/assets/img/mail-icon.png"}),title:"Mail",className:"blue-btn"})}),s.jsx("div",{className:"agent-select w-50",children:s.jsx(d,{type:"select",placeholder:"Agents",className:"w-50"})})]})]})]})}),s.jsx("div",{className:"col-12 mt-5 ",children:s.jsxs("div",{className:"text-center",children:[s.jsx(l,{title:"Posts",className:e==="posts"?"active-tab-button":"post-tab-button",onClick:()=>i("posts")}),s.jsx(l,{title:"Listings",className:e==="listing"?"active-tab-button":"post-tab-button",onClick:()=>i("listing")})]})}),s.jsx(x,{type:e})]})})})};export{L as default};
