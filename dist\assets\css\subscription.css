.subscription-container {
  
  margin: 90px auto;
  /* padding: 20px; */
  /* font-family: Arial, sans-serif; */
}
.subscription-container .logo img {
    width: 130px;
}
.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  font-size: 40px;
  margin-bottom: 10px;
}

.header p {
  font-size: 24px;
  color: #666;
}

.plan-options {
  margin-bottom: 30px;
}

.plan-options h2 {
  font-size: 30px;
  margin-bottom: 30px;
}

.plans-container {
  display: flex;
  flex-direction: row;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.plan-card {
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 50px 25px;
  flex: 1;
  min-width: 550px;
  max-width: 600px;
  text-align: center;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.plan-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.plan-header {
  margin-bottom: 20px;
}

.plan-card h3 {
  margin: 0 0 15px 0;
  font-size: 20px;
  color: #333;
}

.plan-price {
  margin-bottom: 15px;
}

.plan-price .price {
  font-size: 32px;
  font-weight: bold;
  color: #007bff;
}

.plan-price .period {
  font-size: 16px;
  color: #666;
  margin-left: 2px;
}

.plan-description {
  margin: 15px 0;
  color: #555;
  font-size: 14px;
  line-height: 1.5;
}

.auto-renew-note {
  margin: 15px 0;
  color: #888;
  font-size: 12px;
  font-style: italic;
}

.subscribe-btn {
  width: 100%;
  margin-top: 15px;
  padding: 12px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.subscribe-btn:hover {
  background-color: #0056b3;
}

/* Subscription Status */
.subscription-status {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.status-label {
  background-color: #52c41a;
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

/* Cancel Subscription Button */
.cancel-subscription-btn {
  width: 100%;
  margin-top: 15px;
  padding: 12px 20px;
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cancel-subscription-btn:hover {
  background-color: #d9363e;
}

.cancel-subscription-btn:disabled {
  background-color: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
}

/* Subscribed Plan Card Styling */
.plan-card:has(.subscription-status) {
  border-color: #52c41a;
  background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);
}

.plan-card:has(.subscription-status):hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(82, 196, 26, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .plans-container {
    flex-direction: column;
    align-items: center;
  }
  
  .plan-card {
    max-width: 100%;
    width: 100%;
  }
  
  .header h1 {
    font-size: 28px;
  }
  
  .plan-options h2 {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .subscription-container {
    padding: 15px;
  }
  
  .plan-card {
    padding: 20px;
  }
  
  .plan-price .price {
    font-size: 28px;
  }
  
  .header h1 {
    font-size: 24px;
  }
}

.subscription-offer {
  margin: 20px 0;
  text-align: center;
}

.subscription-offer h3 {
  font-size: 16px;
  margin: 10px 0;
}

.subscription-offer p {
  font-size: 14px;
  margin: 5px 0;
}

.subscription-history {
  margin-top: 30px;
}

.subscription-history h3 {
  font-size: 16px;
  margin-bottom: 10px;
}

.subscription-history ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.subscription-history li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

hr {
  border: 0;
  height: 1px;
  background-color: #ddd;
  margin: 20px 0;
}
.suscription-box{
  border: 1px solid #CCCCCC;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 12px;
  margin-bottom: 10px;
}

/* Payment Form Styles */
.payment-form-container {
  padding: 20px 0;
}

.payment-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.payment-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.selected-plan-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-top: 15px;
}

.selected-plan-info p {
  margin: 5px 0;
  font-size: 14px;
  color: #666;
}

/* Form Sections */
.billing-section,
.card-section,
.address-section {
  margin-bottom: 30px;
}

.billing-section h4,
.card-section h4,
.address-section h4 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

/* Stripe Card Element Styles */
.stripe-card-element {
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  transition: border-color 0.3s ease;
  min-height: 50px;
  display: flex;
  align-items: center;
}

.stripe-card-element:hover {
  border-color: #40a9ff;
}

.stripe-card-element:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Form Actions */
.form-actions {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #d9d9d9;
  width: 100%;

}

.cancel-btn:hover {
  background-color: #e6e6e6;
  border-color: #bfbfbf;
}

.payment-btn {
  background-color: #52c41a;
  color: white;
  border: none;
  width: 100%;
}

.payment-btn:hover {
  background-color: #389e0d;
}

.payment-btn:disabled {
  background-color: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
}

/* Error States */
.ant-form-item-has-error .stripe-card-element {
  border-color: #ff4d4f;
}

.ant-form-item-has-error .stripe-card-element:focus-within {
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* New Subscription Status Styles */
.subscription-status {
  margin-bottom: 15px;
}

.pending-status {
  color: #faad14 !important;
  font-weight: 600;
  margin-bottom: 5px;
}

.canceled-status {
  color: #ff4d4f !important;
  font-weight: 600;
  margin-bottom: 5px;
}

.status-description {
  font-size: 12px;
  color: #666;
  margin: 0;
  font-style: italic;
}

/* .cancellation-message {
  font-size: 12px;
  color: #ff4d4f;
  margin: 5px 0 0 0;
  padding: 8px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  font-style: italic;
} */

/* Button Styles for Different States */
.pending-btn {
  background-color: #faad14 !important;
  color: white !important;
  border: none !important;
  cursor: not-allowed !important;
  opacity: 0.7;
}

.pending-btn:hover {
  background-color: #faad14 !important;
}

/* Plan Card Status Variations */
.plan-card:has(.pending-status) {
  border-color: #faad14;
  background: linear-gradient(135deg, #fffbe6 0%, #ffffff 100%);
}

.plan-card:has(.canceled-status) {
  border-color: #ff4d4f;
  background: linear-gradient(135deg, #fff2f0 0%, #ffffff 100%);
}

.plan-card:has(.pending-status):hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(250, 173, 20, 0.2);
}

.plan-card:has(.canceled-status):hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(255, 77, 79, 0.2);
}

/* Active Subscription Styles */
.active-subscription {
  border: 2px solid #52c41a !important;
  background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%) !important;
  position: relative;
}

.active-subscription .subscription-status {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
}

.status-badge {
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-badge.active {
  background-color: #52c41a;
  color: white;
}

.status-badge.cancelling {
  background-color: #faad14;
  color: white;
}

.renewal-date {
  font-size: 14px;
  color: #666;
  margin: 10px 0;
  font-style: italic;
}

.cancel-btn {
  background-color: #ff4d4f !important;
  color: white !important;
  border: none !important;
  width: 100%;

  padding: 12px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cancel-btn:hover {
  background-color: #d9363e !important;
}

.cancel-btn:disabled {
  background-color: #f5f5f5 !important;
  color: #bfbfbf !important;
  cursor: not-allowed !important;
}

.active-subscription:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(82, 196, 26, 0.2);
}

/* Cancellation Notice Styles */
/* .cancellation-notice {
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  text-align: center;
} */

/* .cancellation-message {
  font-size: 16px;
  color: #d46b08;
  margin: 0 0 10px 0;
  font-weight: 600;
} */

.cancellation-info {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
  font-style: italic;
}

/* Plan card styling for scheduled cancellation */
.plan-card:has(.status-badge.cancelling) {
  border-color: #faad14;
  background: linear-gradient(135deg, #fffbe6 0%, #ffffff 100%);
}

.plan-card:has(.status-badge.cancelling):hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(250, 173, 20, 0.2);
}