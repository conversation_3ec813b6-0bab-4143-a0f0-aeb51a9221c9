import React from "react";
import { Skeleton } from "antd";

const PostSkeleton = () => {
  return (
    <div className="post-card sphere-card p-3 rounded border bg-white" style={{ minHeight: '420px' }}>
      {/* Header - User Profile */}
      <div className="d-flex align-items-center pb-2 mb-3">
        <Skeleton.Avatar size={40} />
        <div className="ms-2 flex-grow-1">
          <Skeleton.Input style={{ width: 120, height: 16 }} active size="small" />
          <div className="mt-1">
            <Skeleton.Input style={{ width: 80, height: 12 }} active size="small" />
          </div>
        </div>
      </div>

      {/* Post Image/Video Placeholder */}
      <div className="sphere-post mb-3 mt-2">
        <Skeleton.Image 
          style={{ 
            width: "100%", 
            height: "180px", 
            borderRadius: "12px" 
          }} 
          active 
        />
      </div>

      {/* Content Text Placeholder */}
      <div className="mb-3">
        <Skeleton active paragraph={{ rows: 2, width: ['100%', '60%'] }} title={false} />
      </div>

      {/* Engagement Counts Placeholder */}
      <div className="d-flex align-items-center justify-content-start mb-2">
        <Skeleton.Input style={{ width: 60, height: 14 }} active size="small" />
        <Skeleton.Input style={{ width: 80, height: 14, marginLeft: 16 }} active size="small" />
        <Skeleton.Input style={{ width: 60, height: 14, marginLeft: 16 }} active size="small" />
      </div>

      {/* Divider */}
      <hr className="my-2" style={{ borderColor: '#e9ecef', margin: '8px 0' }} />

      {/* Action Icons Placeholder */}
      <div className="d-flex align-items-center justify-content-around">
        <div className="d-flex align-items-center">
          <Skeleton.Input style={{ width: 20, height: 20 }} active size="small" />
          <Skeleton.Input style={{ width: 40, height: 14, marginLeft: 8 }} active size="small" />
        </div>
        <div className="d-flex align-items-center">
          <Skeleton.Input style={{ width: 20, height: 20 }} active size="small" />
          <Skeleton.Input style={{ width: 60, height: 14, marginLeft: 8 }} active size="small" />
        </div>
        <div className="d-flex align-items-center">
          <Skeleton.Input style={{ width: 20, height: 20 }} active size="small" />
          <Skeleton.Input style={{ width: 40, height: 14, marginLeft: 8 }} active size="small" />
        </div>
      </div>
    </div>
  );
};

export default PostSkeleton;