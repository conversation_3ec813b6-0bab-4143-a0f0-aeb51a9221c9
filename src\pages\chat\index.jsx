import React from "react";
import InnerLayout from "@/components/shared/layout/innerlayout";
import { Badge, Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import SearchBar from "@/components/shared/inputs/searchbar";
import BaseInput from "@/components/shared/inputs";
import ChatUserList from "@/components/shared/list/chatuserlist";
import PostInput from "@/components/shared/inputs/postinput";
import ChatList from "./chatlist";
import ChatDetail from "./chatdetail";

const chatUsers = [
  {
    name: "<PERSON><PERSON>",
    message: "Hey! I need help to setup the M...",
    isTyping: false,
    msgTime: "Now",
    msgStatus: "sent",
    onlineStatus: "online",
    avatar: "/assets/img/avatar-1.png",
  },
  {
    name: "<PERSON>",
    message: "Sure, let me check it...",
    isTyping: false,
    msgTime: "2 min ago",
    msgStatus: "received",
    onlineStatus: "offline",
    avatar: "/assets/img/avatar-2.png",
  },
  {
    name: "<PERSON><PERSON>",
    message: "",
    isTyping: true,
    msgTime: "Typing...",
    msgStatus: null,
    onlineStatus: "away",
    avatar: "/assets/img/avatar-3.png",
  },
  {
    name: "David",
    message: "Thanks for the update!",
    isTyping: false,
    msgTime: "5 min ago",
    msgStatus: "viewed",
    onlineStatus: "online",
    avatar: "/assets/img/avatar-4.png",
  },
];

const messages = [
  {
    from: "user",
    text: "Hey.. Bill, nice to meet you!",
    time: "9hr ago",
    avatar: "/assets/img/avatar-1.png",
    onlineStatus: "online",
  },
  {
    from: "me",
    text: "Hope you’re doing fine.",
    time: "9hr ago",
    avatar: "/assets/img/avatar-1.png",
  },
  {
    from: "user",
    text: "Distinctio architecto debitis...",
    images: [
      "/assets/img/image-6.png",
      "/assets/img/image-6.png",
      "/assets/img/image-6.png",
    ],
    time: "9hr ago",
    avatar: "/assets/img/avatar-1.png",
    onlineStatus: "away",
  },
];
const selectedUser = {
  name: "Stebin Ben",
  statusText: "Active 1 h ago",
  avatar: "/assets/img/avatar-1.png",
  onlineStatus: "online"
};
const Inbox = () => {
  return (
    <InnerLayout>
      <div className="container-fluid mt-5">
        <div className="row">
          <div className="col-12">
            <div className="chat-box">
              <div className="chat-listing">
                <div className="listing-header d-flex justify-content-between">
                  <div className="d-flex">
                    <p className="color-black font-18 me-2">Messages</p>
                    <p className="msg-numbers">9</p>
                  </div>
                </div>
                <div>
                  <BaseInput
                    placeholder="Search in Messenger"
                    icon={<SearchOutlined style={{ color: "gray" }} />}
                    className="chat-search"
                  />
                </div>
                <ChatList users={chatUsers}  selectedUser={selectedUser}  />
              </div>
              <ChatDetail messages={messages} />
            </div>
          </div>
        </div>
      </div>
    </InnerLayout>
  );
};

export default Inbox;
