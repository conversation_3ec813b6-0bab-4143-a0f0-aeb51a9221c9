export const authUtils = {
  logout: async (clearRememberMe = false) => {
    try {
      localStorage.removeItem('session');

      if (clearRememberMe) {
        localStorage.removeItem('rememberedCredentials');
      }

      window.location.replace('/login');
    } catch (error) {
      window.location.replace('/login');
    }
  },

  isAuthenticated: () => {
    return !!(window.user && Object.keys(window.user).length > 0);
  },

  hasRememberMe: async () => {
    try {
      const credentials = await window.helper.getStorageData('rememberedCredentials');
      return !!(credentials && credentials.email);
    } catch (error) {
      return false;
    }
  },

  getSessionInfo: async () => {
    try {
      const session = await window.helper.getStorageData('session');
      const hasRememberMe = await authUtils.hasRememberMe();
      
      return {
        isAuthenticated: authUtils.isAuthenticated(),
        hasRememberMe,
        user: session || {},
      };
    } catch (error) {
      return {
        isAuthenticated: false,
        hasRememberMe: false,
        user: {},
      };
    }
  },

  extendPersistentSession: async (days = 30) => {
    try {
      const credentials = await window.helper.getStorageData('rememberedCredentials');
      if (credentials) {
        credentials.expiresAt = Date.now() + (days * 24 * 60 * 60 * 1000);
        await window.helper.setStorageData('rememberedCredentials', credentials);
      }
    } catch (error) {
    }
  },

  cleanupExpiredSessions: async () => {
    try {
      const credentials = await window.helper.getStorageData('rememberedCredentials');
      if (credentials && credentials.expiresAt && credentials.expiresAt <= Date.now()) {
        localStorage.removeItem('rememberedCredentials');
      }
    } catch (error) {
    }
  }
};