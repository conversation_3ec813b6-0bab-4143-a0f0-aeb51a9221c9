const api = {
  signup: { method: "POST", url: "user" },
  login: { method: "POST", url: "login" },
  forgotPassword: { method: "POST", url: "forgot-password" },
  resetPassword: { method: "POST", url: "reset-password" },
  updateUser: { method: "PUT", url: "user" },
  deleteUser: { method: "DELETE", url: "user" },
  deactivateUser: { method: "POST", url: "user/deactivate" },
  getUser: { method: "GET", url: "user" },
  startupData: { method: "GET", url: "general/startup-data" },
  cities: { method: "GET", url: "cities" },

  fileUpload: { method: "POST", url: "files/upload" },
  createSubscription: { method: "POST", url: "subscriptions" },
  getCurrentSubscription: { method: "GET", url: "subscriptions/current" },
  cancelSubscription: { method: "DELETE", url: "subscriptions/current" },
  getSubscriptionProduct: { method: "GET", url: "subscriptions/product" },
  getSubscriptionProducts: { method: "GET", url: "subscription/products" },
  properties: { method: "GET", url: "properties" },
  addProperty: { method: "POST", url: "properties" },
  updateProperty: { method: "PUT", url: "properties" },
  deleteProperty: { method: "DELETE", url: "properties" },
  postItem: { method: "GET", url: "posts" },
  addPost: { method: "POST", url: "posts" },
  deletePost: { method: "DELETE", url: "posts" },
  updatePost: { method: "PATCH", url: "posts" },
  likePost: { method: "POST", url: "likes" },
  comments: { method: "POST", url: "comments" },
};

export default api;
