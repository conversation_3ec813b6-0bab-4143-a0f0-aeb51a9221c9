import React from "react";
import { Pagination, Skeleton } from "antd";
import _ from "lodash";

const GlobalPagination = ({
  pagination,
  handlePageChange,
  isLoading,
  pageSizeOptions = ["10", "20", "50"],
}) => {
  if (isLoading) {
    return (
      <div className="row mt-4">
        <div className="col-12 d-flex justify-content-end">
          <Skeleton.Input style={{ width: 300 }} active />
        </div>
      </div>
    );
  }

  if (!pagination || pagination.totalPages <= 1) {
    return null;
  }

  return (
    <div className="row mt-4">
      <div className="col-12 d-flex justify-content-end">
        <Pagination
          current={pagination.currentPage}
          pageSize={pagination.pageLimit}
          total={pagination.totalCount}
          onChange={handlePageChange}
          onShowSizeChange={handlePageChange}
          showSizeChanger={true}
          pageSizeOptions={pageSizeOptions}
        />
      </div>
    </div>
  );
};

export default GlobalPagination;
