import { useQuery } from "./useQuery";

/**
 * Global startup data hook - ensures data is fetched only once
 * and cached aggressively across the entire application
 */
export function useStartupData() {
  return useQuery("startupData", {
    // CRITICAL: Aggressive caching to prevent multiple API calls
    staleTime: Infinity, // Data never goes stale - only refetch manually
    gcTime: Infinity, // Keep in cache forever
    refetchOnMount: false, // Never refetch when component mounts
    refetchOnWindowFocus: false, // Never refetch on window focus
    refetchOnReconnect: false, // Never refetch on network reconnect
    retry: false, // Don't retry failed requests

    // FIXED: Match server response structure - return the full response
    // The server likely returns the data directly, not nested in a 'data' property
    select: (response) => {
      // Log the actual server response to understand the structure
      if (process.env.NODE_ENV === "development") {
      }

      // Return the response as-is first, then we can adjust based on actual structure
      return response;
    },

    // Minimize re-renders
    notifyOnChangeProps: ["data", "error", "isLoading"],

    // Enable this query by default
    enabled: true,
  });
}

export default useStartupData;
