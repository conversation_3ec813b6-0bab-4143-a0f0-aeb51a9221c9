import React from "react";
import UserAvatar from "@/components/shared/avatar/useravatar";
import { LikeOutlined, MessageOutlined } from "@ant-design/icons";
import { Link } from "react-router-dom";

const MyComment = ({
  isReply,
  mycomment,
  commentlink,
  likes,
  shares,
  avatar,
}) => {
  return (
    <>
      <div className={isReply ? "mycomment comment-reply ms-4" : "mycomment"}>
        {avatar}
        <div className={isReply ? "reply-bg ms-5" : "ms-5"}>
          <p className="text-muted mt-2 ">{mycomment}</p>
          <Link className="color-blue  mb-3">{commentlink}</Link>
        </div>
      </div>
      <div className="ms-4 mb-4"> 
        <span className="me-2">
          <LikeOutlined /> <span className="font-14">{likes}</span>
        </span>
        <span className="me-2">
          <img src="/assets/img/share-icon.png" alt="" />
          <span className="font-14"> {shares} </span>
        </span>
      </div>{" "}
    </>
  );
};

export default MyComment;
