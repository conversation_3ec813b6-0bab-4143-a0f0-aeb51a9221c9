import {
  LikeOutlined,
  MessageOutlined,
  RetweetOutlined,
} from "@ant-design/icons";

const InteractionBox = ({
  likes = 0,
  comments = 0,
  reactions = [],
  isSpareCard = false,
  repost = 0,
}) => {
  return (
    <div
      className="d-flex align-items-center justify-content-between text-muted"
      style={{ fontSize: 14 }}
    >
      <div className="d-flex align-items-center">
        <span className="me-3 like-icon">
          <LikeOutlined /> <span className="font-14 ms-1">{likes} Likes</span>
        </span>
        <span className="me-3">
          <MessageOutlined />{" "}
          <span className="font-14 ms-1">{comments} Comments</span>
        </span>
        <span className="me-2">
          <RetweetOutlined />{" "}
          <span className="font-14 ms-1">{repost} Shares</span>
        </span>
      </div>

      <div className="d-flex align-items-center ms-2">
        {reactions &&
          reactions.length > 0 &&
          reactions.slice(0, 5).map((avatar, idx) => (
            <img
              key={idx}
              src={avatar}
              alt="react"
              className="rounded-circle"
              width={28}
              height={28}
              style={{
                marginLeft: idx > 0 ? -8 : 0,
                border: "2px solid #fff",
              }}
            />
          ))}
        {reactions && reactions.length > 5 && (
          <span className="ms-2 text-muted">+{reactions.length - 5}</span>
        )}
      </div>
    </div>
  );
};

export default InteractionBox;
