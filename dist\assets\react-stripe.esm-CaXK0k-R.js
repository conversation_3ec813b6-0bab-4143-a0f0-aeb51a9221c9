import{x as be,a as _t,g as Tt,z as ut,y as _e,i as De,A as kt,V as Lt,B as ft,D as le,E as At,q as ve,T as Wt,N as dt,G as Vt,L as qt,W as Dt,H as Ht,I as Bt}from"./index-B2p2olBm.js";import{r as i,aX as zt,V as Me,l as Te,m as mt,o as oe,N as xe,g as ae,n as pt,$ as ke,M as ne,G as Gt,aI as Xt,aY as Ut,aJ as Yt,w as Kt,aZ as Jt,Z as Qt,a_ as Zt,au as He,a$ as en,aO as tn,t as nn,b0 as Be,b1 as rn,aQ as gt,I as on,e as an,C as sn,a6 as ln,a5 as cn,a7 as un,b2 as fn,U as dn,b3 as mn,P as pn,aT as gn,R as k}from"./index-3mE9H3a0.js";import{u as hn,t as yn,i as bn,o as vn}from"./button-CMBVME-6.js";const Cn=(e,t)=>{const n=i.useContext(zt),r=i.useMemo(()=>{var a;const s=t||Me[e],c=(a=n==null?void 0:n[e])!==null&&a!==void 0?a:{};return Object.assign(Object.assign({},typeof s=="function"?s():s),c||{})},[e,t,n]),o=i.useMemo(()=>{const a=n==null?void 0:n.locale;return n!=null&&n.exist&&!a?Me.locale:a},[n]);return[r,o]},ze=e=>typeof e=="object"&&e!=null&&e.nodeType===1,Ge=(e,t)=>(!t||e!=="hidden")&&e!=="visible"&&e!=="clip",ge=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){const n=getComputedStyle(e,null);return Ge(n.overflowY,t)||Ge(n.overflowX,t)||(r=>{const o=(a=>{if(!a.ownerDocument||!a.ownerDocument.defaultView)return null;try{return a.ownerDocument.defaultView.frameElement}catch{return null}})(r);return!!o&&(o.clientHeight<r.scrollHeight||o.clientWidth<r.scrollWidth)})(e)}return!1},he=(e,t,n,r,o,a,s,c)=>a<e&&s>t||a>e&&s<t?0:a<=e&&c<=n||s>=t&&c>=n?a-e-r:s>t&&c<n||a<e&&c>n?s-t+o:0,xn=e=>{const t=e.parentElement;return t??(e.getRootNode().host||null)},Xe=(e,t)=>{var n,r,o,a;if(typeof document>"u")return[];const{scrollMode:s,block:c,inline:l,boundary:u,skipOverflowHiddenElements:b}=t,g=typeof u=="function"?u:D=>D!==u;if(!ze(e))throw new TypeError("Invalid target");const p=document.scrollingElement||document.documentElement,w=[];let $=e;for(;ze($)&&g($);){if($=xn($),$===p){w.push($);break}$!=null&&$===document.body&&ge($)&&!ge(document.documentElement)||$!=null&&ge($,b)&&w.push($)}const S=(r=(n=window.visualViewport)==null?void 0:n.width)!=null?r:innerWidth,v=(a=(o=window.visualViewport)==null?void 0:o.height)!=null?a:innerHeight,{scrollX:m,scrollY:R}=window,{height:f,width:C,top:y,right:E,bottom:P,left:O}=e.getBoundingClientRect(),{top:x,right:h,bottom:F,left:N}=(D=>{const d=window.getComputedStyle(D);return{top:parseFloat(d.scrollMarginTop)||0,right:parseFloat(d.scrollMarginRight)||0,bottom:parseFloat(d.scrollMarginBottom)||0,left:parseFloat(d.scrollMarginLeft)||0}})(e);let A=c==="start"||c==="nearest"?y-x:c==="end"?P+F:y+f/2-x+F,I=l==="center"?O+C/2-N+h:l==="end"?E+h:O-N;const j=[];for(let D=0;D<w.length;D++){const d=w[D],{height:V,width:M,top:U,right:_,bottom:K,left:Q}=d.getBoundingClientRect();if(s==="if-needed"&&y>=0&&O>=0&&P<=v&&E<=S&&(d===p&&!ge(d)||y>=U&&P<=K&&O>=Q&&E<=_))return j;const ce=getComputedStyle(d),J=parseInt(ce.borderLeftWidth,10),Z=parseInt(ce.borderTopWidth,10),L=parseInt(ce.borderRightWidth,10),H=parseInt(ce.borderBottomWidth,10);let T=0,X=0;const W="offsetWidth"in d?d.offsetWidth-d.clientWidth-J-L:0,Y="offsetHeight"in d?d.offsetHeight-d.clientHeight-Z-H:0,te="offsetWidth"in d?d.offsetWidth===0?0:M/d.offsetWidth:0,se="offsetHeight"in d?d.offsetHeight===0?0:V/d.offsetHeight:0;if(p===d)T=c==="start"?A:c==="end"?A-v:c==="nearest"?he(R,R+v,v,Z,H,R+A,R+A+f,f):A-v/2,X=l==="start"?I:l==="center"?I-S/2:l==="end"?I-S:he(m,m+S,S,J,L,m+I,m+I+C,C),T=Math.max(0,T+R),X=Math.max(0,X+m);else{T=c==="start"?A-U-Z:c==="end"?A-K+H+Y:c==="nearest"?he(U,K,V,Z,H+Y,A,A+f,f):A-(U+V/2)+Y/2,X=l==="start"?I-Q-J:l==="center"?I-(Q+M/2)+W/2:l==="end"?I-_+L+W:he(Q,_,M,J,L+W,I,I+C,C);const{scrollLeft:B,scrollTop:ie}=d;T=se===0?0:Math.max(0,Math.min(ie+T/se,d.scrollHeight-V/se+Y)),X=te===0?0:Math.max(0,Math.min(B+X/te,d.scrollWidth-M/te+W)),A+=ie-T,I+=B-X}j.push({el:d,top:T,left:X})}return j},$n=e=>e===!1?{block:"end",inline:"nearest"}:(t=>t===Object(t)&&Object.keys(t).length!==0)(e)?e:{block:"start",inline:"nearest"};function Sn(e,t){if(!e.isConnected||!(o=>{let a=o;for(;a&&a.parentNode;){if(a.parentNode===document)return!0;a=a.parentNode instanceof ShadowRoot?a.parentNode.host:a.parentNode}return!1})(e))return;const n=(o=>{const a=window.getComputedStyle(o);return{top:parseFloat(a.scrollMarginTop)||0,right:parseFloat(a.scrollMarginRight)||0,bottom:parseFloat(a.scrollMarginBottom)||0,left:parseFloat(a.scrollMarginLeft)||0}})(e);if((o=>typeof o=="object"&&typeof o.behavior=="function")(t))return t.behavior(Xe(e,t));const r=typeof t=="boolean"||t==null?void 0:t.behavior;for(const{el:o,top:a,left:s}of Xe(e,$n(t))){const c=a-n.top+n.bottom,l=s-n.left+n.right;o.scroll({top:c,left:l,behavior:r})}}const En=e=>{const{componentCls:t}=e;return{[t]:{display:"flex",flexFlow:"row wrap",minWidth:0,"&::before, &::after":{display:"flex"},"&-no-wrap":{flexWrap:"nowrap"},"&-start":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"flex-end"},"&-space-between":{justifyContent:"space-between"},"&-space-around":{justifyContent:"space-around"},"&-space-evenly":{justifyContent:"space-evenly"},"&-top":{alignItems:"flex-start"},"&-middle":{alignItems:"center"},"&-bottom":{alignItems:"flex-end"}}}},On=e=>{const{componentCls:t}=e;return{[t]:{position:"relative",maxWidth:"100%",minHeight:1}}},wn=(e,t)=>{const{prefixCls:n,componentCls:r,gridColumns:o}=e,a={};for(let s=o;s>=0;s--)s===0?(a[`${r}${t}-${s}`]={display:"none"},a[`${r}-push-${s}`]={insetInlineStart:"auto"},a[`${r}-pull-${s}`]={insetInlineEnd:"auto"},a[`${r}${t}-push-${s}`]={insetInlineStart:"auto"},a[`${r}${t}-pull-${s}`]={insetInlineEnd:"auto"},a[`${r}${t}-offset-${s}`]={marginInlineStart:0},a[`${r}${t}-order-${s}`]={order:0}):(a[`${r}${t}-${s}`]=[{"--ant-display":"block",display:"block"},{display:"var(--ant-display)",flex:`0 0 ${s/o*100}%`,maxWidth:`${s/o*100}%`}],a[`${r}${t}-push-${s}`]={insetInlineStart:`${s/o*100}%`},a[`${r}${t}-pull-${s}`]={insetInlineEnd:`${s/o*100}%`},a[`${r}${t}-offset-${s}`]={marginInlineStart:`${s/o*100}%`},a[`${r}${t}-order-${s}`]={order:s});return a[`${r}${t}-flex`]={flex:`var(--${n}${t}-flex)`},a},Fe=(e,t)=>wn(e,t),In=(e,t,n)=>({[`@media (min-width: ${oe(t)})`]:Object.assign({},Fe(e,n))}),jn=()=>({}),Pn=()=>({}),Mn=Te("Grid",En,jn),Fn=e=>({xs:e.screenXSMin,sm:e.screenSMMin,md:e.screenMDMin,lg:e.screenLGMin,xl:e.screenXLMin,xxl:e.screenXXLMin}),Rn=Te("Grid",e=>{const t=mt(e,{gridColumns:24}),n=Fn(t);return delete n.xs,[On(t),Fe(t,""),Fe(t,"-xs"),Object.keys(n).map(r=>In(t,n[r],`-${r}`)).reduce((r,o)=>Object.assign(Object.assign({},r),o),{})]},Pn),ht=i.createContext({});var Nn=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function Ue(e){return typeof e=="number"?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}const _n=["xs","sm","md","lg","xl","xxl"],yt=i.forwardRef((e,t)=>{const{getPrefixCls:n,direction:r}=i.useContext(xe),{gutter:o,wrap:a}=i.useContext(ht),{prefixCls:s,span:c,order:l,offset:u,push:b,pull:g,className:p,children:w,flex:$,style:S}=e,v=Nn(e,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),m=n("col",s),[R,f,C]=Rn(m),y={};let E={};_n.forEach(x=>{let h={};const F=e[x];typeof F=="number"?h.span=F:typeof F=="object"&&(h=F||{}),delete v[x],E=Object.assign(Object.assign({},E),{[`${m}-${x}-${h.span}`]:h.span!==void 0,[`${m}-${x}-order-${h.order}`]:h.order||h.order===0,[`${m}-${x}-offset-${h.offset}`]:h.offset||h.offset===0,[`${m}-${x}-push-${h.push}`]:h.push||h.push===0,[`${m}-${x}-pull-${h.pull}`]:h.pull||h.pull===0,[`${m}-rtl`]:r==="rtl"}),h.flex&&(E[`${m}-${x}-flex`]=!0,y[`--${m}-${x}-flex`]=Ue(h.flex))});const P=ae(m,{[`${m}-${c}`]:c!==void 0,[`${m}-order-${l}`]:l,[`${m}-offset-${u}`]:u,[`${m}-push-${b}`]:b,[`${m}-pull-${g}`]:g},p,E,f,C),O={};if(o&&o[0]>0){const x=o[0]/2;O.paddingLeft=x,O.paddingRight=x}return $&&(O.flex=Ue($),a===!1&&!O.minWidth&&(O.minWidth=0)),R(i.createElement("div",Object.assign({},v,{style:Object.assign(Object.assign(Object.assign({},O),S),y),className:P,ref:t}),w))});function Tn(e,t){const n=[void 0,void 0],r=Array.isArray(e)?e:[e,void 0],o=t||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return r.forEach((a,s)=>{if(typeof a=="object"&&a!==null)for(let c=0;c<be.length;c++){const l=be[c];if(o[l]&&a[l]!==void 0){n[s]=a[l];break}}else n[s]=a}),n}var kn=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function Ye(e,t){const[n,r]=i.useState(typeof e=="string"?e:""),o=()=>{if(typeof e=="string"&&r(e),typeof e=="object")for(let a=0;a<be.length;a++){const s=be[a];if(!t||!t[s])continue;const c=e[s];if(c!==void 0){r(c);return}}};return i.useEffect(()=>{o()},[JSON.stringify(e),t]),n}const Ln=i.forwardRef((e,t)=>{const{prefixCls:n,justify:r,align:o,className:a,style:s,children:c,gutter:l=0,wrap:u}=e,b=kn(e,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:g,direction:p}=i.useContext(xe),w=_t(!0,null),$=Ye(o,w),S=Ye(r,w),v=g("row",n),[m,R,f]=Mn(v),C=Tn(l,w),y=ae(v,{[`${v}-no-wrap`]:u===!1,[`${v}-${S}`]:S,[`${v}-${$}`]:$,[`${v}-rtl`]:p==="rtl"},a,R,f),E={},P=C[0]!=null&&C[0]>0?C[0]/-2:void 0;P&&(E.marginLeft=P,E.marginRight=P);const[O,x]=C;E.rowGap=x;const h=i.useMemo(()=>({gutter:[O,x],wrap:u}),[O,x,u]);return m(i.createElement(ht.Provider,{value:h},i.createElement("div",Object.assign({},b,{className:y,style:Object.assign(Object.assign({},E),s),ref:t}),c)))});function An(e){return e==null?null:typeof e=="object"&&!i.isValidElement(e)?e:{title:e}}function Ce(e){const[t,n]=i.useState(e);return i.useEffect(()=>{const r=setTimeout(()=>{n(e)},e.length?0:10);return()=>{clearTimeout(r)}},[e]),t}const Wn=e=>{const{componentCls:t}=e,n=`${t}-show-help`,r=`${t}-show-help-item`;return{[n]:{transition:`opacity ${e.motionDurationFast} ${e.motionEaseInOut}`,"&-appear, &-enter":{opacity:0,"&-active":{opacity:1}},"&-leave":{opacity:1,"&-active":{opacity:0}},[r]:{overflow:"hidden",transition:`height ${e.motionDurationFast} ${e.motionEaseInOut},
                     opacity ${e.motionDurationFast} ${e.motionEaseInOut},
                     transform ${e.motionDurationFast} ${e.motionEaseInOut} !important`,[`&${r}-appear, &${r}-enter`]:{transform:"translateY(-5px)",opacity:0,"&-active":{transform:"translateY(0)",opacity:1}},[`&${r}-leave-active`]:{transform:"translateY(-5px)"}}}}},Vn=e=>({legend:{display:"block",width:"100%",marginBottom:e.marginLG,padding:0,color:e.colorTextDescription,fontSize:e.fontSizeLG,lineHeight:"inherit",border:0,borderBottom:`${oe(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},'input[type="search"]':{boxSizing:"border-box"},'input[type="radio"], input[type="checkbox"]':{lineHeight:"normal"},'input[type="file"]':{display:"block"},'input[type="range"]':{display:"block",width:"100%"},"select[multiple], select[size]":{height:"auto"},"input[type='file']:focus,\n  input[type='radio']:focus,\n  input[type='checkbox']:focus":{outline:0,boxShadow:`0 0 0 ${oe(e.controlOutlineWidth)} ${e.controlOutline}`},output:{display:"block",paddingTop:15,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight}}),Ke=(e,t)=>{const{formItemCls:n}=e;return{[n]:{[`${n}-label > label`]:{height:t},[`${n}-control-input`]:{minHeight:t}}}},qn=e=>{const{componentCls:t}=e;return{[e.componentCls]:Object.assign(Object.assign(Object.assign({},pt(e)),Vn(e)),{[`${t}-text`]:{display:"inline-block",paddingInlineEnd:e.paddingSM},"&-small":Object.assign({},Ke(e,e.controlHeightSM)),"&-large":Object.assign({},Ke(e,e.controlHeightLG))})}},Dn=e=>{const{formItemCls:t,iconCls:n,rootPrefixCls:r,antCls:o,labelRequiredMarkColor:a,labelColor:s,labelFontSize:c,labelHeight:l,labelColonMarginInlineStart:u,labelColonMarginInlineEnd:b,itemMarginBottom:g}=e;return{[t]:Object.assign(Object.assign({},pt(e)),{marginBottom:g,verticalAlign:"top","&-with-help":{transition:"none"},[`&-hidden,
        &-hidden${o}-row`]:{display:"none"},"&-has-warning":{[`${t}-split`]:{color:e.colorError}},"&-has-error":{[`${t}-split`]:{color:e.colorWarning}},[`${t}-label`]:{flexGrow:0,overflow:"hidden",whiteSpace:"nowrap",textAlign:"end",verticalAlign:"middle","&-left":{textAlign:"start"},"&-wrap":{overflow:"unset",lineHeight:e.lineHeight,whiteSpace:"unset","> label":{verticalAlign:"middle",textWrap:"balance"}},"> label":{position:"relative",display:"inline-flex",alignItems:"center",maxWidth:"100%",height:l,color:s,fontSize:c,[`> ${n}`]:{fontSize:e.fontSize,verticalAlign:"top"},[`&${t}-required`]:{"&::before":{display:"inline-block",marginInlineEnd:e.marginXXS,color:a,fontSize:e.fontSize,fontFamily:"SimSun, sans-serif",lineHeight:1,content:'"*"'},[`&${t}-required-mark-hidden, &${t}-required-mark-optional`]:{"&::before":{display:"none"}}},[`${t}-optional`]:{display:"inline-block",marginInlineStart:e.marginXXS,color:e.colorTextDescription,[`&${t}-required-mark-hidden`]:{display:"none"}},[`${t}-tooltip`]:{color:e.colorTextDescription,cursor:"help",writingMode:"horizontal-tb",marginInlineStart:e.marginXXS},"&::after":{content:'":"',position:"relative",marginBlock:0,marginInlineStart:u,marginInlineEnd:b},[`&${t}-no-colon::after`]:{content:'"\\a0"'}}},[`${t}-control`]:{"--ant-display":"flex",flexDirection:"column",flexGrow:1,[`&:first-child:not([class^="'${r}-col-'"]):not([class*="' ${r}-col-'"])`]:{width:"100%"},"&-input":{position:"relative",display:"flex",alignItems:"center",minHeight:e.controlHeight,"&-content":{flex:"auto",maxWidth:"100%"}}},[t]:{"&-additional":{display:"flex",flexDirection:"column"},"&-explain, &-extra":{clear:"both",color:e.colorTextDescription,fontSize:e.fontSize,lineHeight:e.lineHeight},"&-explain-connected":{width:"100%"},"&-extra":{minHeight:e.controlHeightSM,transition:`color ${e.motionDurationMid} ${e.motionEaseOut}`},"&-explain":{"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning}}},[`&-with-help ${t}-explain`]:{height:"auto",opacity:1},[`${t}-feedback-icon`]:{fontSize:e.fontSize,textAlign:"center",visibility:"visible",animationName:ut,animationDuration:e.motionDurationMid,animationTimingFunction:e.motionEaseOutBack,pointerEvents:"none","&-success":{color:e.colorSuccess},"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning},"&-validating":{color:e.colorPrimary}}})}},Je=(e,t)=>{const{formItemCls:n}=e;return{[`${t}-horizontal`]:{[`${n}-label`]:{flexGrow:0},[`${n}-control`]:{flex:"1 1 0",minWidth:0},[`${n}-label[class$='-24'], ${n}-label[class*='-24 ']`]:{[`& + ${n}-control`]:{minWidth:"unset"}}}}},Hn=e=>{const{componentCls:t,formItemCls:n,inlineItemMarginBottom:r}=e;return{[`${t}-inline`]:{display:"flex",flexWrap:"wrap",[n]:{flex:"none",marginInlineEnd:e.margin,marginBottom:r,"&-row":{flexWrap:"nowrap"},[`> ${n}-label,
        > ${n}-control`]:{display:"inline-block",verticalAlign:"top"},[`> ${n}-label`]:{flex:"none"},[`${t}-text`]:{display:"inline-block"},[`${n}-has-feedback`]:{display:"inline-block"}}}}},re=e=>({padding:e.verticalLabelPadding,margin:e.verticalLabelMargin,whiteSpace:"initial",textAlign:"start","> label":{margin:0,"&::after":{visibility:"hidden"}}}),bt=e=>{const{componentCls:t,formItemCls:n,rootPrefixCls:r}=e;return{[`${n} ${n}-label`]:re(e),[`${t}:not(${t}-inline)`]:{[n]:{flexWrap:"wrap",[`${n}-label, ${n}-control`]:{[`&:not([class*=" ${r}-col-xs"])`]:{flex:"0 0 100%",maxWidth:"100%"}}}}}},Bn=e=>{const{componentCls:t,formItemCls:n,antCls:r}=e;return{[`${t}-vertical`]:{[`${n}:not(${n}-horizontal)`]:{[`${n}-row`]:{flexDirection:"column"},[`${n}-label > label`]:{height:"auto"},[`${n}-control`]:{width:"100%"},[`${n}-label,
        ${r}-col-24${n}-label,
        ${r}-col-xl-24${n}-label`]:re(e)}},[`@media (max-width: ${oe(e.screenXSMax)})`]:[bt(e),{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-xs-24${n}-label`]:re(e)}}}],[`@media (max-width: ${oe(e.screenSMMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-sm-24${n}-label`]:re(e)}}},[`@media (max-width: ${oe(e.screenMDMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-md-24${n}-label`]:re(e)}}},[`@media (max-width: ${oe(e.screenLGMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-lg-24${n}-label`]:re(e)}}}}},zn=e=>{const{formItemCls:t,antCls:n}=e;return{[`${t}-vertical`]:{[`${t}-row`]:{flexDirection:"column"},[`${t}-label > label`]:{height:"auto"},[`${t}-control`]:{width:"100%"}},[`${t}-vertical ${t}-label,
      ${n}-col-24${t}-label,
      ${n}-col-xl-24${t}-label`]:re(e),[`@media (max-width: ${oe(e.screenXSMax)})`]:[bt(e),{[t]:{[`${n}-col-xs-24${t}-label`]:re(e)}}],[`@media (max-width: ${oe(e.screenSMMax)})`]:{[t]:{[`${n}-col-sm-24${t}-label`]:re(e)}},[`@media (max-width: ${oe(e.screenMDMax)})`]:{[t]:{[`${n}-col-md-24${t}-label`]:re(e)}},[`@media (max-width: ${oe(e.screenLGMax)})`]:{[t]:{[`${n}-col-lg-24${t}-label`]:re(e)}}}},Gn=e=>({labelRequiredMarkColor:e.colorError,labelColor:e.colorTextHeading,labelFontSize:e.fontSize,labelHeight:e.controlHeight,labelColonMarginInlineStart:e.marginXXS/2,labelColonMarginInlineEnd:e.marginXS,itemMarginBottom:e.marginLG,verticalLabelPadding:`0 0 ${e.paddingXS}px`,verticalLabelMargin:0,inlineItemMarginBottom:0}),vt=(e,t)=>mt(e,{formItemCls:`${e.componentCls}-item`,rootPrefixCls:t}),Le=Te("Form",(e,t)=>{let{rootPrefixCls:n}=t;const r=vt(e,n);return[qn(r),Dn(r),Wn(r),Je(r,r.componentCls),Je(r,r.formItemCls),Hn(r),Bn(r),zn(r),Tt(r),ut]},Gn,{order:-1e3}),Qe=[];function we(e,t,n){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0;return{key:typeof e=="string"?e:`${t}-${r}`,error:e,errorStatus:n}}const Ct=e=>{let{help:t,helpStatus:n,errors:r=Qe,warnings:o=Qe,className:a,fieldId:s,onVisibleChanged:c}=e;const{prefixCls:l}=i.useContext(_e),u=`${l}-item-explain`,b=ke(l),[g,p,w]=Le(l,b),$=i.useMemo(()=>De(l),[l]),S=Ce(r),v=Ce(o),m=i.useMemo(()=>t!=null?[we(t,"help",n)]:[].concat(ne(S.map((C,y)=>we(C,"error","error",y))),ne(v.map((C,y)=>we(C,"warning","warning",y)))),[t,n,S,v]),R=i.useMemo(()=>{const C={};return m.forEach(y=>{let{key:E}=y;C[E]=(C[E]||0)+1}),m.map((y,E)=>Object.assign(Object.assign({},y),{key:C[y.key]>1?`${y.key}-fallback-${E}`:y.key}))},[m]),f={};return s&&(f.id=`${s}_help`),g(i.createElement(Gt,{motionDeadline:$.motionDeadline,motionName:`${l}-show-help`,visible:!!R.length,onVisibleChanged:c},C=>{const{className:y,style:E}=C;return i.createElement("div",Object.assign({},f,{className:ae(u,y,w,b,a,p),style:E}),i.createElement(Xt,Object.assign({keys:R},De(l),{motionName:`${l}-show-help-item`,component:!1}),P=>{const{key:O,error:x,errorStatus:h,className:F,style:N}=P;return i.createElement("div",{key:O,className:ae(F,{[`${u}-${h}`]:h}),style:N},x)}))}))},Xn=["parentNode"],Un="form_item";function me(e){return e===void 0||e===!1?[]:Array.isArray(e)?e:[e]}function xt(e,t){if(!e.length)return;const n=e.join("_");return t?`${t}_${n}`:Xn.includes(n)?`${Un}_${n}`:n}function $t(e,t,n,r,o,a){let s=r;return a!==void 0?s=a:n.validating?s="validating":e.length?s="error":t.length?s="warning":(n.touched||o&&n.validated)&&(s="success"),s}var Yn=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function Ze(e){return me(e).join("_")}function et(e,t){const n=t.getFieldInstance(e),r=Ut(n);if(r)return r;const o=xt(me(e),t.__INTERNAL__.name);if(o)return document.getElementById(o)}function St(e){const[t]=kt(),n=i.useRef({}),r=i.useMemo(()=>e??Object.assign(Object.assign({},t),{__INTERNAL__:{itemRef:o=>a=>{const s=Ze(o);a?n.current[s]=a:delete n.current[s]}},scrollToField:function(o){let a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{focus:s}=a,c=Yn(a,["focus"]),l=et(o,r);l&&(Sn(l,Object.assign({scrollMode:"if-needed",block:"nearest"},c)),s&&r.focusField(o))},focusField:o=>{var a,s;const c=r.getFieldInstance(o);typeof(c==null?void 0:c.focus)=="function"?c.focus():(s=(a=et(o,r))===null||a===void 0?void 0:a.focus)===null||s===void 0||s.call(a)},getFieldInstance:o=>{const a=Ze(o);return n.current[a]}}),[e,t]);return[r]}var Kn=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Jn=(e,t)=>{const n=i.useContext(Yt),{getPrefixCls:r,direction:o,requiredMark:a,colon:s,scrollToFirstError:c,className:l,style:u}=Kt("form"),{prefixCls:b,className:g,rootClassName:p,size:w,disabled:$=n,form:S,colon:v,labelAlign:m,labelWrap:R,labelCol:f,wrapperCol:C,hideRequiredMark:y,layout:E="horizontal",scrollToFirstError:P,requiredMark:O,onFinishFailed:x,name:h,style:F,feedbackIcons:N,variant:A}=e,I=Kn(e,["prefixCls","className","rootClassName","size","disabled","form","colon","labelAlign","labelWrap","labelCol","wrapperCol","hideRequiredMark","layout","scrollToFirstError","requiredMark","onFinishFailed","name","style","feedbackIcons","variant"]),j=hn(w),D=i.useContext(Jt),d=i.useMemo(()=>O!==void 0?O:y?!1:a!==void 0?a:!0,[y,O,a]),V=v??s,M=r("form",b),U=ke(M),[_,K,Q]=Le(M,U),ce=ae(M,`${M}-${E}`,{[`${M}-hide-required-mark`]:d===!1,[`${M}-rtl`]:o==="rtl",[`${M}-${j}`]:j},Q,U,K,l,g,p),[J]=St(S),{__INTERNAL__:Z}=J;Z.name=h;const L=i.useMemo(()=>({name:h,labelAlign:m,labelCol:f,labelWrap:R,wrapperCol:C,vertical:E==="vertical",colon:V,requiredMark:d,itemRef:Z.itemRef,form:J,feedbackIcons:N}),[h,m,f,C,E,V,d,J,N]),H=i.useRef(null);i.useImperativeHandle(t,()=>{var W;return Object.assign(Object.assign({},J),{nativeElement:(W=H.current)===null||W===void 0?void 0:W.nativeElement})});const T=(W,Y)=>{if(W){let te={block:"nearest"};typeof W=="object"&&(te=Object.assign(Object.assign({},te),W)),J.scrollToField(Y,te)}},X=W=>{if(x==null||x(W),W.errorFields.length){const Y=W.errorFields[0].name;if(P!==void 0){T(P,Y);return}c!==void 0&&T(c,Y)}};return _(i.createElement(Lt.Provider,{value:A},i.createElement(Qt,{disabled:$},i.createElement(Zt.Provider,{value:j},i.createElement(ft,{validateMessages:D},i.createElement(le.Provider,{value:L},i.createElement(At,Object.assign({id:h},I,{name:h,onFinishFailed:X,form:J,ref:H,style:Object.assign(Object.assign({},u),F),className:ce}))))))))},Qn=i.forwardRef(Jn);function Zn(e){if(typeof e=="function")return e;const t=yn(e);return t.length<=1?t[0]:t}const Et=()=>{const{status:e,errors:t=[],warnings:n=[]}=i.useContext(ve);return{status:e,errors:t,warnings:n}};Et.Context=ve;function er(e){const[t,n]=i.useState(e),r=i.useRef(null),o=i.useRef([]),a=i.useRef(!1);i.useEffect(()=>(a.current=!1,()=>{a.current=!0,He.cancel(r.current),r.current=null}),[]);function s(c){a.current||(r.current===null&&(o.current=[],r.current=He(()=>{r.current=null,n(l=>{let u=l;return o.current.forEach(b=>{u=b(u)}),u})})),o.current.push(c))}return[t,s]}function tr(){const{itemRef:e}=i.useContext(le),t=i.useRef({});function n(r,o){const a=o&&typeof o=="object"&&en(o),s=r.join("_");return(t.current.name!==s||t.current.originRef!==a)&&(t.current.name=s,t.current.originRef=a,t.current.ref=tn(e(r),a)),t.current.ref}return n}const nr=e=>{const{formItemCls:t}=e;return{"@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none)":{[`${t}-control`]:{display:"flex"}}}},rr=nn(["Form","item-item"],(e,t)=>{let{rootPrefixCls:n}=t;const r=vt(e,n);return[nr(r)]});var or=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const ar=24,ir=e=>{const{prefixCls:t,status:n,labelCol:r,wrapperCol:o,children:a,errors:s,warnings:c,_internalItemRender:l,extra:u,help:b,fieldId:g,marginBottom:p,onErrorVisibleChanged:w,label:$}=e,S=`${t}-item`,v=i.useContext(le),m=i.useMemo(()=>{let I=Object.assign({},o||v.wrapperCol||{});return $===null&&!r&&!o&&v.labelCol&&[void 0,"xs","sm","md","lg","xl","xxl"].forEach(D=>{const d=D?[D]:[],V=Be(v.labelCol,d),M=typeof V=="object"?V:{},U=Be(I,d),_=typeof U=="object"?U:{};"span"in M&&!("offset"in _)&&M.span<ar&&(I=rn(I,[].concat(d,["offset"]),M.span))}),I},[o,v]),R=ae(`${S}-control`,m.className),f=i.useMemo(()=>{const{labelCol:I,wrapperCol:j}=v;return or(v,["labelCol","wrapperCol"])},[v]),C=i.useRef(null),[y,E]=i.useState(0);gt(()=>{u&&C.current?E(C.current.clientHeight):E(0)},[u]);const P=i.createElement("div",{className:`${S}-control-input`},i.createElement("div",{className:`${S}-control-input-content`},a)),O=i.useMemo(()=>({prefixCls:t,status:n}),[t,n]),x=p!==null||s.length||c.length?i.createElement(_e.Provider,{value:O},i.createElement(Ct,{fieldId:g,errors:s,warnings:c,help:b,helpStatus:n,className:`${S}-explain-connected`,onVisibleChanged:w})):null,h={};g&&(h.id=`${g}_extra`);const F=u?i.createElement("div",Object.assign({},h,{className:`${S}-extra`,ref:C}),u):null,N=x||F?i.createElement("div",{className:`${S}-additional`,style:p?{minHeight:p+y}:{}},x,F):null,A=l&&l.mark==="pro_table_render"&&l.render?l.render(e,{input:P,errorList:x,extra:F}):i.createElement(i.Fragment,null,P,N);return i.createElement(le.Provider,{value:f},i.createElement(yt,Object.assign({},m,{className:R}),A),i.createElement(rr,{prefixCls:t}))};var sr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"question-circle",theme:"outlined"},lr=function(t,n){return i.createElement(on,an({},t,{ref:n,icon:sr}))},cr=i.forwardRef(lr),ur=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const fr=e=>{let{prefixCls:t,label:n,htmlFor:r,labelCol:o,labelAlign:a,colon:s,required:c,requiredMark:l,tooltip:u,vertical:b}=e;var g;const[p]=Cn("Form"),{labelAlign:w,labelCol:$,labelWrap:S,colon:v}=i.useContext(le);if(!n)return null;const m=o||$||{},R=a||w,f=`${t}-item-label`,C=ae(f,R==="left"&&`${f}-left`,m.className,{[`${f}-wrap`]:!!S});let y=n;const E=s===!0||v!==!1&&s!==!1;E&&!b&&typeof n=="string"&&n.trim()&&(y=n.replace(/[:|：]\s*$/,""));const O=An(u);if(O){const{icon:I=i.createElement(cr,null)}=O,j=ur(O,["icon"]),D=i.createElement(Wt,Object.assign({},j),i.cloneElement(I,{className:`${t}-item-tooltip`,title:"",onClick:d=>{d.preventDefault()},tabIndex:null}));y=i.createElement(i.Fragment,null,y,D)}const x=l==="optional",h=typeof l=="function",F=l===!1;h?y=l(y,{required:!!c}):x&&!c&&(y=i.createElement(i.Fragment,null,y,i.createElement("span",{className:`${t}-item-optional`,title:""},(p==null?void 0:p.optional)||((g=Me.Form)===null||g===void 0?void 0:g.optional))));let N;F?N="hidden":(x||h)&&(N="optional");const A=ae({[`${t}-item-required`]:c,[`${t}-item-required-mark-${N}`]:N,[`${t}-item-no-colon`]:!E});return i.createElement(yt,Object.assign({},m,{className:C}),i.createElement("label",{htmlFor:r,className:A,title:typeof n=="string"?n:""},y))},dr={success:un,warning:cn,error:ln,validating:sn};function Ot(e){let{children:t,errors:n,warnings:r,hasFeedback:o,validateStatus:a,prefixCls:s,meta:c,noStyle:l}=e;const u=`${s}-item`,{feedbackIcons:b}=i.useContext(le),g=$t(n,r,c,null,!!o,a),{isFormItemInput:p,status:w,hasFeedback:$,feedbackIcon:S}=i.useContext(ve),v=i.useMemo(()=>{var m;let R;if(o){const C=o!==!0&&o.icons||b,y=g&&((m=C==null?void 0:C({status:g,errors:n,warnings:r}))===null||m===void 0?void 0:m[g]),E=g&&dr[g];R=y!==!1&&E?i.createElement("span",{className:ae(`${u}-feedback-icon`,`${u}-feedback-icon-${g}`)},y||i.createElement(E,null)):null}const f={status:g||"",errors:n,warnings:r,hasFeedback:!!o,feedbackIcon:R,isFormItemInput:!0};return l&&(f.status=(g??w)||"",f.isFormItemInput=p,f.hasFeedback=!!(o??$),f.feedbackIcon=o!==void 0?f.feedbackIcon:S),f},[g,o,l,p,w]);return i.createElement(ve.Provider,{value:v},t)}var mr=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function pr(e){const{prefixCls:t,className:n,rootClassName:r,style:o,help:a,errors:s,warnings:c,validateStatus:l,meta:u,hasFeedback:b,hidden:g,children:p,fieldId:w,required:$,isRequired:S,onSubItemMetaChange:v,layout:m}=e,R=mr(e,["prefixCls","className","rootClassName","style","help","errors","warnings","validateStatus","meta","hasFeedback","hidden","children","fieldId","required","isRequired","onSubItemMetaChange","layout"]),f=`${t}-item`,{requiredMark:C,vertical:y}=i.useContext(le),E=y||m==="vertical",P=i.useRef(null),O=Ce(s),x=Ce(c),h=a!=null,F=!!(h||s.length||c.length),N=!!P.current&&bn(P.current),[A,I]=i.useState(null);gt(()=>{if(F&&P.current){const M=getComputedStyle(P.current);I(parseInt(M.marginBottom,10))}},[F,N]);const j=M=>{M||I(null)},d=function(){let M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;const U=M?O:u.errors,_=M?x:u.warnings;return $t(U,_,u,"",!!b,l)}(),V=ae(f,n,r,{[`${f}-with-help`]:h||O.length||x.length,[`${f}-has-feedback`]:d&&b,[`${f}-has-success`]:d==="success",[`${f}-has-warning`]:d==="warning",[`${f}-has-error`]:d==="error",[`${f}-is-validating`]:d==="validating",[`${f}-hidden`]:g,[`${f}-${m}`]:m});return i.createElement("div",{className:V,style:o,ref:P},i.createElement(Ln,Object.assign({className:`${f}-row`},vn(R,["_internalItemRender","colon","dependencies","extra","fieldKey","getValueFromEvent","getValueProps","htmlFor","id","initialValue","isListField","label","labelAlign","labelCol","labelWrap","messageVariables","name","normalize","noStyle","preserve","requiredMark","rules","shouldUpdate","trigger","tooltip","validateFirst","validateTrigger","valuePropName","wrapperCol","validateDebounce"])),i.createElement(fr,Object.assign({htmlFor:w},e,{requiredMark:C,required:$??S,prefixCls:t,vertical:E})),i.createElement(ir,Object.assign({},e,u,{errors:O,warnings:x,prefixCls:t,status:d,help:a,marginBottom:A,onErrorVisibleChanged:j}),i.createElement(dt.Provider,{value:v},i.createElement(Ot,{prefixCls:t,meta:u,errors:u.errors,warnings:u.warnings,hasFeedback:b,validateStatus:d},p)))),!!A&&i.createElement("div",{className:`${f}-margin-offset`,style:{marginBottom:-A}}))}const gr="__SPLIT__";function hr(e,t){const n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&n.every(o=>{const a=e[o],s=t[o];return a===s||typeof a=="function"||typeof s=="function"})}const yr=i.memo(e=>{let{children:t}=e;return t},(e,t)=>hr(e.control,t.control)&&e.update===t.update&&e.childProps.length===t.childProps.length&&e.childProps.every((n,r)=>n===t.childProps[r]));function tt(){return{errors:[],warnings:[],touched:!1,validating:!1,name:[],validated:!1}}function br(e){const{name:t,noStyle:n,className:r,dependencies:o,prefixCls:a,shouldUpdate:s,rules:c,children:l,required:u,label:b,messageVariables:g,trigger:p="onChange",validateTrigger:w,hidden:$,help:S,layout:v}=e,{getPrefixCls:m}=i.useContext(xe),{name:R}=i.useContext(le),f=Zn(l),C=typeof f=="function",y=i.useContext(dt),{validateTrigger:E}=i.useContext(Vt),P=w!==void 0?w:E,O=t!=null,x=m("form",a),h=ke(x),[F,N,A]=Le(x,h);fn();const I=i.useContext(qt),j=i.useRef(null),[D,d]=er({}),[V,M]=dn(()=>tt()),U=L=>{const H=I==null?void 0:I.getKey(L.name);if(M(L.destroy?tt():L,!0),n&&S!==!1&&y){let T=L.name;if(L.destroy)T=j.current||T;else if(H!==void 0){const[X,W]=H;T=[X].concat(ne(W)),j.current=T}y(L,T)}},_=(L,H)=>{d(T=>{const X=Object.assign({},T),Y=[].concat(ne(L.name.slice(0,-1)),ne(H)).join(gr);return L.destroy?delete X[Y]:X[Y]=L,X})},[K,Q]=i.useMemo(()=>{const L=ne(V.errors),H=ne(V.warnings);return Object.values(D).forEach(T=>{L.push.apply(L,ne(T.errors||[])),H.push.apply(H,ne(T.warnings||[]))}),[L,H]},[D,V.errors,V.warnings]),ce=tr();function J(L,H,T){return n&&!$?i.createElement(Ot,{prefixCls:x,hasFeedback:e.hasFeedback,validateStatus:e.validateStatus,meta:V,errors:K,warnings:Q,noStyle:!0},L):i.createElement(pr,Object.assign({key:"row"},e,{className:ae(r,A,h,N),prefixCls:x,fieldId:H,isRequired:T,errors:K,warnings:Q,meta:V,onSubItemMetaChange:_,layout:v}),L)}if(!O&&!C&&!o)return F(J(f));let Z={};return typeof b=="string"?Z.label=b:t&&(Z.label=String(t)),g&&(Z=Object.assign(Object.assign({},Z),g)),F(i.createElement(Dt,Object.assign({},e,{messageVariables:Z,trigger:p,validateTrigger:P,onMetaChange:U}),(L,H,T)=>{const X=me(t).length&&H?H.name:[],W=xt(X,R),Y=u!==void 0?u:!!(c!=null&&c.some(B=>{if(B&&typeof B=="object"&&B.required&&!B.warningOnly)return!0;if(typeof B=="function"){const ie=B(T);return(ie==null?void 0:ie.required)&&!(ie!=null&&ie.warningOnly)}return!1})),te=Object.assign({},L);let se=null;if(Array.isArray(f)&&O)se=f;else if(!(C&&(!(s||o)||O))){if(!(o&&!C&&!O))if(i.isValidElement(f)){const B=Object.assign(Object.assign({},f.props),te);if(B.id||(B.id=W),S||K.length>0||Q.length>0||e.extra){const fe=[];(S||K.length>0)&&fe.push(`${W}_help`),e.extra&&fe.push(`${W}_extra`),B["aria-describedby"]=fe.join(" ")}K.length>0&&(B["aria-invalid"]="true"),Y&&(B["aria-required"]="true"),mn(f)&&(B.ref=ce(X,f)),new Set([].concat(ne(me(p)),ne(me(P)))).forEach(fe=>{B[fe]=function(){for(var Ae,We,Se,Ve,Ee,qe=arguments.length,Oe=new Array(qe),pe=0;pe<qe;pe++)Oe[pe]=arguments[pe];(Se=te[fe])===null||Se===void 0||(Ae=Se).call.apply(Ae,[te].concat(Oe)),(Ee=(Ve=f.props)[fe])===null||Ee===void 0||(We=Ee).call.apply(We,[Ve].concat(Oe))}});const Nt=[B["aria-required"],B["aria-invalid"],B["aria-describedby"]];se=i.createElement(yr,{control:te,update:f,childProps:Nt},pn(f,B))}else C&&(s||o)&&!O?se=f(T):se=f}return J(se,W,Y)}))}const wt=br;wt.useStatus=Et;var vr=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Cr=e=>{var{prefixCls:t,children:n}=e,r=vr(e,["prefixCls","children"]);const{getPrefixCls:o}=i.useContext(xe),a=o("form",t),s=i.useMemo(()=>({prefixCls:a,status:"error"}),[a]);return i.createElement(Ht,Object.assign({},r),(c,l,u)=>i.createElement(_e.Provider,{value:s},n(c.map(b=>Object.assign(Object.assign({},b),{fieldKey:b.key})),l,{errors:u.errors,warnings:u.warnings})))};function xr(){const{form:e}=i.useContext(le);return e}const ue=Qn;ue.Item=wt;ue.List=Cr;ue.ErrorList=Ct;ue.useForm=St;ue.useFormInstance=xr;ue.useWatch=Bt;ue.Provider=ft;ue.create=()=>{};var Ie={exports:{}},je,nt;function $r(){if(nt)return je;nt=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return je=e,je}var Pe,rt;function Sr(){if(rt)return Pe;rt=1;var e=$r();function t(){}function n(){}return n.resetWarningCache=t,Pe=function(){function r(s,c,l,u,b,g){if(g!==e){var p=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw p.name="Invariant Violation",p}}r.isRequired=r;function o(){return r}var a={array:r,bigint:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:o,element:r,elementType:r,instanceOf:o,node:r,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:n,resetWarningCache:t};return a.PropTypes=a,a},Pe}var ot;function Er(){return ot||(ot=1,Ie.exports=Sr()()),Ie.exports}var Or=Er();const q=gn(Or);function at(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function it(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?at(Object(n),!0).forEach(function(r){It(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):at(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function ye(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?ye=function(t){return typeof t}:ye=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ye(e)}function It(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function wr(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,a;for(a=0;a<r.length;a++)o=r[a],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function Ir(e,t){if(e==null)return{};var n=wr(e,t),r,o;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)r=a[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function jt(e,t){return jr(e)||Pr(e,t)||Mr(e,t)||Fr()}function jr(e){if(Array.isArray(e))return e}function Pr(e,t){var n=e&&(typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"]);if(n!=null){var r=[],o=!0,a=!1,s,c;try{for(n=n.call(e);!(o=(s=n.next()).done)&&(r.push(s.value),!(t&&r.length===t));o=!0);}catch(l){a=!0,c=l}finally{try{!o&&n.return!=null&&n.return()}finally{if(a)throw c}}return r}}function Mr(e,t){if(e){if(typeof e=="string")return st(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return st(e,t)}}function st(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Fr(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var ee=function(t,n,r){var o=!!r,a=k.useRef(r);k.useEffect(function(){a.current=r},[r]),k.useEffect(function(){if(!o||!t)return function(){};var s=function(){a.current&&a.current.apply(a,arguments)};return t.on(n,s),function(){t.off(n,s)}},[o,n,t,a])},Re=function(t){var n=k.useRef(t);return k.useEffect(function(){n.current=t},[t]),n.current},de=function(t){return t!==null&&ye(t)==="object"},Rr=function(t){return de(t)&&typeof t.then=="function"},Nr=function(t){return de(t)&&typeof t.elements=="function"&&typeof t.createToken=="function"&&typeof t.createPaymentMethod=="function"&&typeof t.confirmCardPayment=="function"},lt="[object Object]",_r=function e(t,n){if(!de(t)||!de(n))return t===n;var r=Array.isArray(t),o=Array.isArray(n);if(r!==o)return!1;var a=Object.prototype.toString.call(t)===lt,s=Object.prototype.toString.call(n)===lt;if(a!==s)return!1;if(!a&&!r)return t===n;var c=Object.keys(t),l=Object.keys(n);if(c.length!==l.length)return!1;for(var u={},b=0;b<c.length;b+=1)u[c[b]]=!0;for(var g=0;g<l.length;g+=1)u[l[g]]=!0;var p=Object.keys(u);if(p.length!==c.length)return!1;var w=t,$=n,S=function(m){return e(w[m],$[m])};return p.every(S)},Pt=function(t,n,r){return de(t)?Object.keys(t).reduce(function(o,a){var s=!de(n)||!_r(t[a],n[a]);return r.includes(a)?(s&&console.warn("Unsupported prop change: options.".concat(a," is not a mutable property.")),o):s?it(it({},o||{}),{},It({},a,t[a])):o},null):null},Mt="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",ct=function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Mt;if(t===null||Nr(t))return t;throw new Error(n)},Tr=function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Mt;if(Rr(t))return{tag:"async",stripePromise:Promise.resolve(t).then(function(o){return ct(o,n)})};var r=ct(t,n);return r===null?{tag:"empty"}:{tag:"sync",stripe:r}},kr=function(t){!t||!t._registerWrapper||!t.registerAppInfo||(t._registerWrapper({name:"react-stripe-js",version:"3.7.0"}),t.registerAppInfo({name:"react-stripe-js",version:"3.7.0",url:"https://stripe.com/docs/stripe-js/react"}))},$e=k.createContext(null);$e.displayName="ElementsContext";var Ft=function(t,n){if(!t)throw new Error("Could not find Elements context; You need to wrap the part of your app that ".concat(n," in an <Elements> provider."));return t},Lr=function(t){var n=t.stripe,r=t.options,o=t.children,a=k.useMemo(function(){return Tr(n)},[n]),s=k.useState(function(){return{stripe:a.tag==="sync"?a.stripe:null,elements:a.tag==="sync"?a.stripe.elements(r):null}}),c=jt(s,2),l=c[0],u=c[1];k.useEffect(function(){var p=!0,w=function(S){u(function(v){return v.stripe?v:{stripe:S,elements:S.elements(r)}})};return a.tag==="async"&&!l.stripe?a.stripePromise.then(function($){$&&p&&w($)}):a.tag==="sync"&&!l.stripe&&w(a.stripe),function(){p=!1}},[a,l,r]);var b=Re(n);k.useEffect(function(){b!==null&&b!==n&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[b,n]);var g=Re(r);return k.useEffect(function(){if(l.elements){var p=Pt(r,g,["clientSecret","fonts"]);p&&l.elements.update(p)}},[r,g,l.elements]),k.useEffect(function(){kr(l.stripe)},[l.stripe]),k.createElement($e.Provider,{value:l},o)};Lr.propTypes={stripe:q.any,options:q.object};var Ar=function(t){var n=k.useContext($e);return Ft(n,t)},Xr=function(){var t=Ar("calls useElements()"),n=t.elements;return n};q.func.isRequired;var Rt=k.createContext(null);Rt.displayName="CheckoutSdkContext";var Wr=function(t,n){if(!t)throw new Error("Could not find CheckoutProvider context; You need to wrap the part of your app that ".concat(n," in an <CheckoutProvider> provider."));return t},Vr=k.createContext(null);Vr.displayName="CheckoutContext";q.any,q.shape({fetchClientSecret:q.func.isRequired,elementsOptions:q.object}).isRequired;var Ne=function(t){var n=k.useContext(Rt),r=k.useContext($e);if(n&&r)throw new Error("You cannot wrap the part of your app that ".concat(t," in both <CheckoutProvider> and <Elements> providers."));return n?Wr(n,t):Ft(r,t)},qr=["mode"],Dr=function(t){return t.charAt(0).toUpperCase()+t.slice(1)},z=function(t,n){var r="".concat(Dr(t),"Element"),o=function(l){var u=l.id,b=l.className,g=l.options,p=g===void 0?{}:g,w=l.onBlur,$=l.onFocus,S=l.onReady,v=l.onChange,m=l.onEscape,R=l.onClick,f=l.onLoadError,C=l.onLoaderStart,y=l.onNetworksChange,E=l.onConfirm,P=l.onCancel,O=l.onShippingAddressChange,x=l.onShippingRateChange,h=Ne("mounts <".concat(r,">")),F="elements"in h?h.elements:null,N="checkoutSdk"in h?h.checkoutSdk:null,A=k.useState(null),I=jt(A,2),j=I[0],D=I[1],d=k.useRef(null),V=k.useRef(null);ee(j,"blur",w),ee(j,"focus",$),ee(j,"escape",m),ee(j,"click",R),ee(j,"loaderror",f),ee(j,"loaderstart",C),ee(j,"networkschange",y),ee(j,"confirm",E),ee(j,"cancel",P),ee(j,"shippingaddresschange",O),ee(j,"shippingratechange",x),ee(j,"change",v);var M;S&&(t==="expressCheckout"?M=S:M=function(){S(j)}),ee(j,"ready",M),k.useLayoutEffect(function(){if(d.current===null&&V.current!==null&&(F||N)){var _=null;if(N)switch(t){case"payment":_=N.createPaymentElement(p);break;case"address":if("mode"in p){var K=p.mode,Q=Ir(p,qr);if(K==="shipping")_=N.createShippingAddressElement(Q);else if(K==="billing")_=N.createBillingAddressElement(Q);else throw new Error("Invalid options.mode. mode must be 'billing' or 'shipping'.")}else throw new Error("You must supply options.mode. mode must be 'billing' or 'shipping'.");break;case"expressCheckout":_=N.createExpressCheckoutElement(p);break;case"currencySelector":_=N.createCurrencySelectorElement();break;default:throw new Error("Invalid Element type ".concat(r,". You must use either the <PaymentElement />, <AddressElement options={{mode: 'shipping'}} />, <AddressElement options={{mode: 'billing'}} />, or <ExpressCheckoutElement />."))}else F&&(_=F.create(t,p));d.current=_,D(_),_&&_.mount(V.current)}},[F,N,p]);var U=Re(p);return k.useEffect(function(){if(d.current){var _=Pt(p,U,["paymentRequest"]);_&&"update"in d.current&&d.current.update(_)}},[p,U]),k.useLayoutEffect(function(){return function(){if(d.current&&typeof d.current.destroy=="function")try{d.current.destroy(),d.current=null}catch{}}},[]),k.createElement("div",{id:u,className:b,ref:V})},a=function(l){Ne("mounts <".concat(r,">"));var u=l.id,b=l.className;return k.createElement("div",{id:u,className:b})},s=n?a:o;return s.propTypes={id:q.string,className:q.string,onChange:q.func,onBlur:q.func,onFocus:q.func,onReady:q.func,onEscape:q.func,onClick:q.func,onLoadError:q.func,onLoaderStart:q.func,onNetworksChange:q.func,onConfirm:q.func,onCancel:q.func,onShippingAddressChange:q.func,onShippingRateChange:q.func,options:q.object},s.displayName=r,s.__elementType=t,s},G=typeof window>"u",Hr=k.createContext(null);Hr.displayName="EmbeddedCheckoutProviderContext";var Ur=function(){var t=Ne("calls useStripe()"),n=t.stripe;return n};z("auBankAccount",G);var Yr=z("card",G);z("cardNumber",G);z("cardExpiry",G);z("cardCvc",G);z("fpxBank",G);z("iban",G);z("idealBank",G);z("p24Bank",G);z("epsBank",G);var Kr=z("payment",G);z("expressCheckout",G);z("currencySelector",G);z("paymentRequestButton",G);z("linkAuthentication",G);z("address",G);z("shippingAddress",G);z("paymentMethodMessaging",G);z("affirmMessage",G);z("afterpayClearpayMessage",G);export{Yr as C,Lr as E,ue as F,Kr as P,Ur as a,Xr as b,q as c,Fn as g,Cn as u};
