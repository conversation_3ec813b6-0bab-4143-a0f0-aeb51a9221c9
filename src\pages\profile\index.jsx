import React, { useEffect } from "react";
import InnerLayout from "@/components/shared/layout/innerlayout";
import IconButton from "@/components/shared/button/iconbutton";
import BaseInput from "@/components/shared/inputs";
import FlatButton from "@/components/shared/button/flatbutton";
import { useNavigate, useParams } from "react-router-dom";
import AgentItems from "../agent/agentitems";

const Profile = () => {
  const navigate = useNavigate();
  const { type } = useParams();

  // ✅ Redirect to /agent/posts if no type is present
  useEffect(() => {
    if (!type) {
      navigate("/profile/posts", { replace: true });
    }
  }, [type, navigate]);

  const handleTabClick = (tabType) => {
    navigate(`/profile/${tabType}`);
  };
  return (
    <InnerLayout>
      <div className="container-fluid">
        <div className="row">
          <div className="col-12">
            <div className="agent-box mt-5">
              <div className="agent-header">
                <img src="/assets/img/home-img.png" alt="" />
              </div>
              <div className="agent-body d-flex align-items-center justify-content-between">
                <div className="d-flex align-items-center">
                  <div className="agent-profile">
                    <img src={window.user.image_url} alt={window.user.name} />
                  </div>
                  <div className="ms-3">
                    <div className="d-flex">
                      <p className="me-2 font-600">{window.user.name}</p>
                      <img
                        src="/assets/img/badge.png"
                        alt=""
                        className="img-fluid"
                      />
                    </div>
                    <p className="color-light">
                      {window.user.professional_types &&
                      Array.isArray(window.user.professional_types)
                        ? window.user.professional_types
                            .map((item) =>
                              window.helper.getLabel(
                                "professional_type",
                                item.name
                              )
                            )
                            .join(", ")
                        : window.helper.getLabel(
                            "professional_type",
                            window.user.professional_type
                          )}
                    </p>
                    <div className="d-flex">
                      <div>
                        <img src="/assets/img/location_on.png" alt="" />
                      </div>
                      <div>
                        <p> {window.user.state}</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="d-flex align-items-center">
                  <div className="me-3">
                    <FlatButton
                      title="Edit your Profile"
                      className="blue-btn"
                      onClick={() => navigate("/editprofile")}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-12 mt-5 ">
            <div className="text-center">
              <FlatButton
                title="Posts"
                className={
                  type === "posts" ? "active-tab-button" : "post-tab-button"
                }
                onClick={() => handleTabClick("posts")}
              />
              <FlatButton
                title="Listings"
                className={
                  type === "listing" ? "active-tab-button" : "post-tab-button"
                }
                onClick={() => handleTabClick("listing")}
              />
            </div>
          </div>

          <AgentItems type={type} />
        </div>
      </div>
    </InnerLayout>
  );
};

export default Profile;
