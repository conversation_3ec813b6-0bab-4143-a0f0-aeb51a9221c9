import { useState, useEffect } from 'react';
import Helper from '@/helpers';
import { createSecurePersistentSession, validateRememberMeSecurity } from '@/utils/securityUtils';

/**
 * Custom hook for managing "Remember Me" functionality
 * Handles saving/loading credentials and managing persistent sessions with security
 */
export const useRememberMe = () => {
  const [rememberMe, setRememberMe] = useState(false);
  const [savedCredentials, setSavedCredentials] = useState(null);

  // Load saved credentials on hook initialization
  useEffect(() => {
    const loadSavedCredentials = async () => {
      try {
        const credentials = await Helper.getStorageData("rememberedCredentials");
        const persistentSession = await Helper.getStorageData("persistentSession");
        
        // Validate security before loading credentials
        if (credentials && credentials.email && persistentSession) {
          const securityCheck = await validateRememberMeSecurity(persistentSession);
          
          if (securityCheck.isValid) {
            setSavedCredentials(credentials);
            setRememberMe(true);
          } else {
            // Clear invalid session data
            if (securityCheck.shouldClearSession) {
              localStorage.removeItem("rememberedCredentials");
              localStorage.removeItem("persistentSession");
            }
            console.warn("Remember Me security validation failed:", securityCheck.reason);
          }
        }
      } catch (error) {
        console.error("Error loading saved credentials:", error);
      }
    };

    loadSavedCredentials();
  }, []);

  /**
   * Save user credentials for future auto-login with enhanced security
   * @param {Object} credentials - User credentials {email, password}
   */
  const saveCredentials = async (credentials) => {
    try {
      await Helper.setStorageData("rememberedCredentials", {
        ...credentials,
        rememberMe: true,
        savedAt: Date.now(),
      });

      // Create secure persistent session
      const secureSession = createSecurePersistentSession(credentials);
      await Helper.setStorageData("persistentSession", secureSession);

      setSavedCredentials(credentials);
      console.log("Remember Me credentials saved securely");
    } catch (error) {
      console.error("Error saving credentials:", error);
    }
  };

  /**
   * Clear saved credentials and persistent session
   */
  const clearCredentials = () => {
    try {
      localStorage.removeItem("rememberedCredentials");
      localStorage.removeItem("persistentSession");
      setSavedCredentials(null);
      setRememberMe(false);
      console.log("Remember Me credentials cleared");
    } catch (error) {
      console.error("Error clearing credentials:", error);
    }
  };

  /**
   * Check if persistent session is still valid with security validation
   * @returns {boolean} - Whether the persistent session is valid
   */
  const isPersistentSessionValid = async () => {
    try {
      const persistentSession = await Helper.getStorageData("persistentSession");
      if (!persistentSession) return false;
      
      const securityCheck = await validateRememberMeSecurity(persistentSession);
      return securityCheck.isValid;
    } catch (error) {
      console.error("Error checking persistent session:", error);
      return false;
    }
  };

  /**
   * Update the remember me state and handle credential management
   * @param {boolean} checked - Whether remember me is checked
   */
  const handleRememberMeChange = (checked) => {
    setRememberMe(checked);
    if (!checked) {
      clearCredentials();
    }
  };

  /**
   * Get the remaining time for persistent session in days
   * @returns {number} - Days remaining for persistent session
   */
  const getPersistentSessionDaysRemaining = async () => {
    try {
      const persistentSession = await Helper.getStorageData("persistentSession");
      if (persistentSession && persistentSession.expiresAt) {
        const daysRemaining = Math.ceil((persistentSession.expiresAt - Date.now()) / (24 * 60 * 60 * 1000));
        return Math.max(0, daysRemaining);
      }
      return 0;
    } catch (error) {
      console.error("Error getting persistent session days:", error);
      return 0;
    }
  };

  /**
   * Get security status of the current Remember Me session
   * @returns {Object} - Security status information
   */
  const getSecurityStatus = async () => {
    try {
      const persistentSession = await Helper.getStorageData("persistentSession");
      if (!persistentSession) {
        return { isSecure: false, reason: "No persistent session" };
      }
      
      const securityCheck = await validateRememberMeSecurity(persistentSession);
      return {
        isSecure: securityCheck.isValid,
        reason: securityCheck.reason,
        deviceFingerprint: persistentSession.deviceFingerprint,
        lastActivity: persistentSession.lastActivity,
        failedAttempts: persistentSession.failedAttempts || 0,
      };
    } catch (error) {
      console.error("Error getting security status:", error);
      return { isSecure: false, reason: "Error checking security" };
    }
  };

  return {
    rememberMe,
    savedCredentials,
    saveCredentials,
    clearCredentials,
    handleRememberMeChange,
    isPersistentSessionValid,
    getPersistentSessionDaysRemaining,
    getSecurityStatus,
  };
};