import{u as m,j as e,H as c}from"./index-3mE9H3a0.js";import{I as g,u as w}from"./index-C-I6oq0X.js";import{O as p}from"./optionlist-BlkuFOca.js";import{u as s}from"./useMutation-lvneVzbk.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";import"./index-BUUUOhAK.js";const j=()=>{m();const{showAlert:o}=w(),{mutate:l,isPending:t}=s("deleteAccount",{onSuccess:async()=>{c.removeStorageData(),window.location.replace("/login")}}),{mutate:r,isPending:n}=s("disableAccount",{onSuccess:async()=>{c.removeStorageData(),window.location.replace("/login")}}),a=async i=>{i==="Deactivate account"?(await o({title:"Are you sure?",text:"Your account will be deactivated and you will be logged out!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, deactivate it!",cancelButtonText:"Cancel"})).isConfirmed&&r():i==="Delete Account"&&(await o({title:"Are you sure?",text:"This action cannot be undone. Your account will be permanently deleted and you will be logged out!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",cancelButtonText:"Cancel"})).isConfirmed&&l({slug:window.user.id,data:""})},u=[{label:"Change Password",link:"/change-password"},{label:n?"Deactivating...":"Deactivate account",action:()=>!n&&!t&&a("Deactivate account"),disabled:n||t},{label:t?"Deleting...":"Delete Account",action:()=>!n&&!t&&a("Delete Account"),disabled:n||t}];return e.jsx(g,{children:e.jsx("div",{className:"container-fluid",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 mt-4",children:e.jsx("p",{className:"font-36 color-black font-600",children:"Settings"})}),e.jsx("div",{className:"col-12",children:e.jsx(p,{title:"Settings",options:u})})]})})})};export{j as default};
