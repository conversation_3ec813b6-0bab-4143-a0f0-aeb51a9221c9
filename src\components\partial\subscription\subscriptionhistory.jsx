import React from "react";
import SubscriptionList from "../../shared/list/subscriptionlist";

const SubscriptionHistory = () => {
  const subscriptionData = [
    {
      date: "25 Jan 2024",
      duration: "$5.99 for a month",
      price: "5.99",
    },
    {
      date: "25 Feb 2024",
      duration: "$5.99 for a month",
      price: "5.99",
    },
    {
      date: "25 Mar 2024",
      duration: "$5.99 for a month",
      price: "5.99",
    },
  ];

  return (
    <div className="container-fluid">
      <div className="row">
        <div className="col-12">
          <div className="subscription-history">
            <h2 className="mt-3 mb-4">Subscription History</h2>

            {subscriptionData.map((item, index) => (
              <SubscriptionList
                key={index}
                date={item.date}
                duration={item.duration}
                price={item.price}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionHistory;
