import { Form, Radio, Space } from "antd";
import React, { useState } from "react";
import BaseInput from "../../shared/inputs";

const AssignFilterForm = ({ projectdetail }) => {
  const [value, setValue] = useState(1);
  const onChange = (e) => {
    setValue(e.target.value);
  };
  return (
    <Form
      name="filter"
      layout="vertical"
      onFinish={() => setIsFilterModalOpen(false)}
      initialValues={{
        remember: true,
      }}
      autoComplete="off"
    >
      {projectdetail !== "true" ? (
        <p className="font-18 color-black mb-2">Sort By Priority</p>
      ) : (
        ""
      )}
      <Radio.Group onChange={onChange} value={value}>
        <Space direction="vertical">
          <Radio value={1}>Most recently opened</Radio>
          <Radio value={2}>Alphabetically </Radio>
          <Radio value={3}>Newest to Oldest </Radio>
        </Space>
      </Radio.Group>
      {projectdetail !== "true" ? (
        <div className="row">
          <div className="col-12 col-md-6">
            <BaseInput
              name="startDate"
              type="datepiker"
              placeholder=""
              label="Start Date"
            />
          </div>
          <div className="col-12 col-md-6">
            <BaseInput
              name="dueDate"
              type="datepiker"
              placeholder=""
              label="Due Date"
            />
          </div>
        </div>
      ) : (
        ""
      )}
    </Form>
  );
};

export default AssignFilterForm;
