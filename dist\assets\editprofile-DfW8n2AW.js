import{u as V,R as F,r as k,H as w,d as t,j as s,ac as $}from"./index-3mE9H3a0.js";import{I as A}from"./index-C-I6oq0X.js";import{c as n,B as c,P as z,v as r}from"./index-Cd-Wothc.js";import{F as P}from"./flatbutton-Yo0mdDJ8.js";import{C as J}from"./index-Cypjjnzq.js";import{a as Q}from"./useQuery-Bzo2W4ue.js";import{u as W}from"./useMutation-lvneVzbk.js";import{u as Y,a as G}from"./useLocationData-QmC5yoKM.js";import{g as K,p as Z,t as D}from"./languageUtils-BKYM3hOY.js";import{F as L}from"./react-stripe.esm-CaXK0k-R.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";import"./index-BUUUOhAK.js";import"./index-CY48Knbi.js";import"./fade-yKzH711D.js";import"./DeleteOutlined-DCDN0YWx.js";import"./InboxOutlined-ERhPIBBD.js";const he=()=>{var N,v;const b=V(),[p]=L.useForm(),[f,x]=F.useState([]),{data:y}=Y(),d=y==null?void 0:y.data,{selectedState:S,stateOptions:E,cityOptions:C,statesLoading:q,citiesLoading:I,handleStateChange:B,updateSelectedState:j}=G(),{data:h,isLoading:g}=Q("getUser",{slug:(N=window.user)==null?void 0:N.id,staleTime:0,gcTime:0,enabled:!!((v=window.user)!=null&&v.id),refetchOnMount:!0,refetchOnWindowFocus:!0,refetchOnReconnect:!0,cacheTime:0,retry:!1}),e=h==null?void 0:h.data,O=l=>{if(!l)return"";const a=l.toString().replace(/\D/g,"");return a.length===10?`(${a.slice(0,3)}) ${a.slice(3,6)}-${a.slice(6)}`:l};k.useEffect(()=>{var l;if(e){const a={...e,api_token:(l=window.user)==null?void 0:l.api_token};window.user=a,w.setStorageData("session",a)}},[e]);const{mutate:X,isPending:u}=W("updateUser",{invalidateQueries:["getUser"],onSuccess:async l=>{l&&(await w.setStorageData("session",l.data),window.user=l.data,b("/profile/posts"))}}),U=F.useMemo(()=>K(d==null?void 0:d.languages),[d==null?void 0:d.languages]);k.useEffect(()=>{if(e&&!g){e!=null&&e.state&&j(e.state);let l=[];d!=null&&d.languages&&(e!=null&&e.languages)&&(l=Z(e.languages,d.languages));const a={name:(e==null?void 0:e.name)||"",email:(e==null?void 0:e.email)||"",mobile_no:O(e==null?void 0:e.mobile_no)||"",state:(e==null?void 0:e.state)||"",city:(e==null?void 0:e.city)||"",languages:l,multi_state_license:e==null?void 0:e.multi_state_license,professional_type:e!=null&&e.professional_types&&Array.isArray(e.professional_types)?e.professional_types.map(i=>i.name):e!=null&&e.professional_type?[e.professional_type]:[],broker_license_number:(()=>{if(e!=null&&e.professional_types&&Array.isArray(e.professional_types)){const i=e.professional_types.find(o=>o.name==="broker");return(i==null?void 0:i.license_number)||""}return(e==null?void 0:e.broker_license_number)||(e==null?void 0:e.license_number)||""})(),broker_license_expiry_date:(()=>{if(e!=null&&e.professional_types&&Array.isArray(e.professional_types)){const i=e.professional_types.find(o=>o.name==="broker");return i!=null&&i.license_expiry_date?t.formatForForm(i.license_expiry_date):null}return e!=null&&e.broker_license_expiry_date?t.formatForForm(e.broker_license_expiry_date):e!=null&&e.license_expiry_date?t.formatForForm(e.license_expiry_date):null})(),lender_license_number:(()=>{if(e!=null&&e.professional_types&&Array.isArray(e.professional_types)){const i=e.professional_types.find(o=>o.name==="lender");return(i==null?void 0:i.license_number)||""}return(e==null?void 0:e.lender_license_number)||""})(),lender_license_expiry_date:(()=>{if(e!=null&&e.professional_types&&Array.isArray(e.professional_types)){const i=e.professional_types.find(o=>o.name==="lender");return i!=null&&i.license_expiry_date?t.formatForForm(i.license_expiry_date):null}return e!=null&&e.lender_license_expiry_date?t.formatForForm(e.lender_license_expiry_date):null})(),commercial_license_number:(()=>{if(e!=null&&e.professional_types&&Array.isArray(e.professional_types)){const i=e.professional_types.find(o=>o.name==="commercial");return(i==null?void 0:i.license_number)||""}return(e==null?void 0:e.license_number)||""})(),commercial_license_expiry_date:(()=>{if(e!=null&&e.professional_types&&Array.isArray(e.professional_types)){const i=e.professional_types.find(o=>o.name==="commercial");return i!=null&&i.license_expiry_date?t.formatForForm(i.license_expiry_date):null}return e!=null&&e.commercial_license_expiry_date?t.formatForForm(e.commercial_license_expiry_date):null})(),image_url:(e==null?void 0:e.image_url)||null,username:(e==null?void 0:e.username)||"",dob:e!=null&&e.dob?t.formatForForm(e.dob):null,gender:(e==null?void 0:e.gender)||""};p.setFieldsValue(a),e!=null&&e.professional_types&&Array.isArray(e.professional_types)?x(e.professional_types.map(i=>i.name)):e!=null&&e.professional_type&&x([e.professional_type])}},[e,g,d,p,j]);const R=l=>{const a=[];l.professional_type&&l.professional_type.length>0&&l.professional_type.forEach(_=>{const m={name:_};_==="broker"&&l.broker_license_number?(m.license_number=l.broker_license_number,m.license_expiry_date=l.broker_license_expiry_date?t.formatForAPI(l.broker_license_expiry_date):""):_==="lender"&&l.lender_license_number?(m.license_number=l.lender_license_number,m.license_expiry_date=l.lender_license_expiry_date?t.formatForAPI(l.lender_license_expiry_date):""):_==="commercial"&&l.commercial_license_number&&(m.license_number=l.commercial_license_number,m.license_expiry_date=l.commercial_license_expiry_date?t.formatForAPI(l.commercial_license_expiry_date):""),a.push(m)});const i={...l,professional_types:JSON.stringify(a),dob:l.dob?t.formatForAPI(l.dob):"",device:"web",device_token:"web-token-"+Date.now()};delete i.professional_type,delete i.broker_license_number,delete i.broker_license_expiry_date,delete i.lender_license_number,delete i.lender_license_expiry_date,delete i.commercial_license_number,delete i.commercial_license_expiry_date,delete i.languages;const o=D(l.languages),H={...i,...o};X({slug:window.user.id,data:H})},M=l=>{B(l,p)},T=l=>{x(l)};return g?s.jsx(A,{children:s.jsx("div",{className:"container-fluid",children:s.jsx("div",{className:"row mt-5",children:s.jsx("div",{className:"col-12 text-center",children:s.jsx($,{size:"large",tip:"Loading user data..."})})})})}):s.jsx(A,{children:s.jsx("div",{className:"container-fluid",children:s.jsxs("div",{className:"row mt-5",children:[s.jsx("h2",{className:"color-black mb-3",children:"Profile"}),s.jsx("div",{className:"col-12 col-md-4 col-lg-4 col-xl-3",children:s.jsxs("div",{className:"profile-edit text-center",children:[s.jsx("div",{className:"text-end",children:s.jsx("img",{src:"/assets/img/more-icon.png",alt:""})}),s.jsx("div",{className:"agent-profile mt-4",children:s.jsx("img",{src:(e==null?void 0:e.image_url)||"/assets/img/default-avatar.png",alt:"Profile",style:{width:"100px",height:"100px",borderRadius:"50%",objectFit:"cover"}})}),s.jsx("p",{className:"mb-4",children:e==null?void 0:e.name}),s.jsxs("div",{className:"row",children:[s.jsxs("div",{className:"col-12 col-md-6",children:[s.jsx("p",{children:e==null?void 0:e.post_count}),s.jsx("p",{children:"Post"})]}),s.jsxs("div",{className:"col-12 col-md-6",children:[s.jsx("p",{children:e==null?void 0:e.property_count}),s.jsx("p",{children:"Listing"})]})]})]})}),s.jsx("div",{className:"col-12 col-md-8 col-lg-8 col-xl-9",children:s.jsxs("div",{className:"personal-border",children:[s.jsx("div",{className:"border-bottom p-4",children:s.jsx("p",{className:"font-18",children:"Edit Profile"})}),s.jsxs(L,{name:"editProfile",layout:"vertical",form:p,onFinish:R,scrollToFirstError:!0,autoComplete:"off",className:"p-4",children:[s.jsx("div",{className:"row",children:s.jsx("div",{className:"col-12 text-center mb-4",children:s.jsx(J,{static_img:e==null?void 0:e.image_url,fileType:"image",multiple:!1,maxSize:5,uploadAction:"",useFormItem:!0,formItemProps:{name:"image_url",rules:n("profile-image")},callback:l=>{p.setFieldsValue({image_url:l.fileObj||l.originFileObj})}})})}),s.jsxs("div",{className:"profile-information",children:[s.jsx("h4",{className:"mb-3",children:"Personal Information"}),s.jsxs("div",{className:"row",children:[s.jsx("div",{className:"col-12 col-md-6",children:s.jsx(c,{name:"name",placeholder:"e.g John Doe",label:"Full Name",rules:n("full-name",r.required,r.minLength(2),r.maxLength(25))})}),s.jsx("div",{className:"col-12 col-md-6",children:s.jsx(c,{name:"email",placeholder:"<EMAIL>",label:"Email Address",rules:n("email",r.required,r.email),disabled:!0})}),s.jsx("div",{className:"col-12 col-md-6",children:s.jsx(z,{name:"mobile_no",placeholder:"(XXX) XXX-XXXX",label:"Phone Number",rules:n("mobile-number",r.required,r.phone)})})]})]}),s.jsxs("div",{className:"professional-information",children:[s.jsx("h4",{className:"mt-4 mb-3",children:"Professional Information"}),s.jsxs("div",{className:"row",children:[s.jsx("div",{className:"col-12 col-md-6",children:s.jsxs("div",{className:"row",children:[s.jsx("div",{className:"col-6",children:s.jsx(c,{name:"state",placeholder:"State",label:"Location",type:"select",rules:n("state",r.required),options:E,loading:q,handlechange:M,showSearch:!0})}),s.jsx("div",{className:"col-6",children:s.jsx(c,{name:"city",placeholder:"City",label:" ",type:"select",options:C,rules:n("city",r.required),disabled:!S,loading:I,showSearch:!0})})]})}),s.jsx("div",{className:"col-12 col-md-6",children:s.jsx(c,{name:"languages",placeholder:"Select Language",label:"Languages Spoken",type:"select",rules:n("language",r.required),mode:"multiple",options:U})}),s.jsx("div",{className:"col-12 col-md-6",children:s.jsx("div",{className:"ant-form-item d-block",children:s.jsx("div",{className:"mt-2",children:s.jsx(c,{type:"radio",name:"multi_state_license",label:"Multi State License",options:[{value:!0,label:"Yes"},{value:!1,label:"No"}],rules:n("multi-state-license",r.required)})})})}),s.jsx("div",{className:"col-12",children:s.jsx(c,{type:"checkboxgroup",name:"professional_type",label:"Profession Type",options:[{value:"broker",label:"Real Estate Broker"},{value:"lender",label:"Lender Mortgage Broker"},{value:"commercial",label:"Commercial Agent"}],onChange:T,rules:n("profession-type",r.required)})}),f.includes("broker")&&s.jsxs("div",{className:"col-12",children:[s.jsx("h5",{className:"mt-3 font-18",children:"Real Estate Broker License"}),s.jsxs("div",{className:"row",children:[s.jsx("div",{className:"col-12 col-md-6",children:s.jsx(c,{name:"broker_license_number",placeholder:"1ABC234",label:"License No.",rules:n("license",r.required)})}),s.jsx("div",{className:"col-12 col-md-6",children:s.jsx(c,{name:"broker_license_expiry_date",placeholder:"Expiry Date",label:"Expiry Date",type:"datepicker",rules:n("expiry",r.required)})})]})]}),f.includes("lender")&&s.jsxs("div",{className:"col-12",children:[s.jsx("h5",{className:"mt-3 font-18",children:"Lender / mtg Broker License"}),s.jsxs("div",{className:"row",children:[s.jsx("div",{className:"col-12 col-md-6",children:s.jsx(c,{name:"lender_license_number",placeholder:"1ABC234",label:"License No.",rules:n("license",r.required)})}),s.jsx("div",{className:"col-12 col-md-6",children:s.jsx(c,{name:"lender_license_expiry_date",placeholder:"Expiry Date",label:"Expiry Date",type:"datepicker",rules:n("expiry",r.required)})})]})]}),f.includes("commercial")&&s.jsxs("div",{className:"col-12",children:[s.jsx("h5",{className:"mt-3 font-18",children:"Commercial Agent License"}),s.jsxs("div",{className:"row",children:[s.jsx("div",{className:"col-12 col-md-6",children:s.jsx(c,{name:"commercial_license_number",placeholder:"1ABC234",label:"License No.",rules:n("license",r.required)})}),s.jsx("div",{className:"col-12 col-md-6",children:s.jsx(c,{name:"commercial_license_expiry_date",placeholder:"Expiry Date",label:"Expiry Date",type:"datepicker",rules:n("expiry",r.required)})})]})]}),s.jsxs("div",{className:"col-12 mt-4 text-end",children:[s.jsx(P,{title:"Cancel",className:"gray-btn me-3",onClick:()=>b(-1)}),s.jsx(P,{title:u?"Updating Profile...":"Update Profile",className:"blue-btn",htmlType:"submit",loading:u,disabled:u})]})]})]})]})]})})]})})})};export{he as default};
