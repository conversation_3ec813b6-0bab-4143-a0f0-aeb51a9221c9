import{r as l,u as m,j as s}from"./index-3mE9H3a0.js";import{A as n}from"./index-CTEMBgeC.js";import{B as c,c as d,v as o}from"./index-Cd-Wothc.js";import{F as u}from"./flatbutton-Yo0mdDJ8.js";import{u as g}from"./useMutation-lvneVzbk.js";import{F as p}from"./react-stripe.esm-CaXK0k-R.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";const x=()=>{const t=m(),{mutate:i,isPending:e}=g("forgotPassword",{useFormData:!1,onSuccess:a=>{a&&t("/login")}}),r=a=>{i(a)};return s.jsxs("div",{className:"forgot-area",children:[s.jsx("div",{className:"text-center sign-up-logo",children:s.jsx("img",{src:"../assets/img/logo.png",alt:"Auth Logo"})}),s.jsxs(n,{showSidebar:!0,src:"/assets/img/forgot-img.png",pageType:"forgot",children:[s.jsx("div",{className:"row",children:s.jsxs("div",{className:"col-12",children:[s.jsx("h1",{className:"font-36 color-black mb-3",children:"Forgot Your Password?"}),s.jsx("p",{children:"Enter the email address associated with your account."})]})}),s.jsxs(p,{name:"ForgetPassword",layout:"vertical",onFinish:r,initialValues:{remember:!0},autoComplete:"off",children:[s.jsxs("div",{className:"row",children:[s.jsx("div",{className:"col-12 col-md-12 col-lg-10",children:s.jsx(c,{name:"email",placeholder:"Email Address",label:"Email",rules:d("email",o.required,o.email)})}),s.jsxs("div",{className:"col-12 col-md-12 col-lg-10",children:[s.jsx(u,{title:e?"Submiting...":"Submit",className:"mx-auto mt-4 signin-btn signup-btn w-100 mt-5",htmlType:"submit",loading:e,disabled:e}),s.jsx("p",{className:"signup-text",children:"We will email you a link to reset your password"})]})]}),s.jsx("div",{})]})]})]})},y=l.memo(x);export{y as default};
