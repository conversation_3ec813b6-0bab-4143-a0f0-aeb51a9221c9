import React from "react";
import { useNavigate } from "react-router-dom";
import InnerLayout from "@/components/shared/layout/innerlayout";
import OptionList from "@/components/shared/list/OptionList";
import useSweetAlert from "@/hooks/useSweetAlert";
import { useMutation } from "@/hooks/reactQuery";

const Setting = () => {
  const navigate = useNavigate();
  const { showAlert } = useSweetAlert();

  const { mutate: deleteAccount, isPending: isDeleting } = useMutation(
    "deleteAccount",
    {
      onSuccess: async () => {
        // For account deletion, clear everything including Remember Me
        localStorage.clear();
        window.user = {};
        window.location.replace("/login");
      },
    }
  );

  const { mutate: deactivateAccount, isPending: isDeactivating } = useMutation(
    "disableAccount",
    {
      onSuccess: async () => {
        // For account deactivation, clear everything including Remember Me
        localStorage.clear();
        window.user = {};
        window.location.replace("/login");
      },
    }
  );

  const handleOptionClick = async (label) => {
    if (label === "Deactivate account") {
      const result = await showAlert({
        title: "Are you sure?",
        text: "Your account will be deactivated and you will be logged out!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes, deactivate it!",
        cancelButtonText: "Cancel",
      });

      if (result.isConfirmed) {
        deactivateAccount();
      }
    } else if (label === "Delete Account") {
      const result = await showAlert({
        title: "Are you sure?",
        text: "This action cannot be undone. Your account will be permanently deleted and you will be logged out!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "Cancel",
      });

      if (result.isConfirmed) {
        deleteAccount({ slug: window.user.id, data: "" });
      }
    }
  };

  const settingsOptions = [
    { label: "Change Password", link: "/change-password" },
    {
      label: isDeactivating ? "Deactivating..." : "Deactivate account",
      action: () =>
        !isDeactivating &&
        !isDeleting &&
        handleOptionClick("Deactivate account"),
      disabled: isDeactivating || isDeleting,
    },
    {
      label: isDeleting ? "Deleting..." : "Delete Account",
      action: () =>
        !isDeactivating && !isDeleting && handleOptionClick("Delete Account"),
      disabled: isDeactivating || isDeleting,
    },
  ];

  return (
    <InnerLayout>
      <div className="container-fluid">
        <div className="row">
          <div className="col-12 mt-4">
            <p className="font-36 color-black font-600">Settings</p>
          </div>
          <div className="col-12">
            <OptionList title="Settings" options={settingsOptions} />
          </div>
        </div>
      </div>
    </InnerLayout>
  );
};

export default Setting;