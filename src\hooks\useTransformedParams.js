import { useMemo } from "react";

/**
 * Custom hook to transform API parameters, especially handling arrays
 * for multiple parameters with the same key name
 */
export const useTransformedParams = (searchKeyword, filters) => {
  return useMemo(() => {
    const params = {
      keyword: searchKeyword,
      ...filters,
    };

    // Handle languages array - convert to multiple parameters
    if (filters.languages && Array.isArray(filters.languages)) {
      // Remove the languages array and add individual language parameters
      delete params.languages;

      // Add each language as a separate parameter
      filters.languages.forEach((languageId, index) => {
        params[`languages[${index}]`] = languageId;
      });
    }

    // Remove empty values
    Object.keys(params).forEach((key) => {
      if (
        params[key] === undefined ||
        params[key] === null ||
        params[key] === ""
      ) {
        delete params[key];
      }
    });

    return params;
  }, [searchKeyword, filters]);
};

export default useTransformedParams;
