import { useQueryClient } from "@tanstack/react-query";
import { useCallback } from "react";

/**
 * Custom hook for managing favorite synchronization across all property queries
 * This ensures that when a property is favorited/unfavorited, all related caches are updated
 */
export const useFavoriteSync = () => {
  const queryClient = useQueryClient();

  /**
   * Update favorite status across all property caches
   * @param {string|number} propertyId - The property ID
   * @param {boolean} isFavorite - The new favorite status
   */
  const updateFavoriteInCache = useCallback(
    (propertyId, isFavorite) => {
      // Get all cached queries that start with "properties"
      const queryCache = queryClient.getQueryCache();
      const propertyQueries = queryCache.findAll({
        queryKey: ["properties"],
        exact: false,
      });

      // Update each cached query
      propertyQueries.forEach((query) => {
        const currentData = query.state.data;
        if (currentData?.data && Array.isArray(currentData.data)) {
          // Update the specific property in the cached data
          const updatedData = {
            ...currentData,
            data: currentData.data.map((property) =>
              property.id === propertyId || property.id === String(propertyId)
                ? { ...property, is_favorite: isFavorite }
                : property
            ),
          };

          // Set the updated data back to the cache
          queryClient.setQueryData(query.queryKey, updatedData);
        }
      });

      // Also update any individual property queries
      const individualPropertyQueries = queryCache.findAll({
        queryKey: ["getProperty"],
        exact: false,
      });

      individualPropertyQueries.forEach((query) => {
        const currentData = query.state.data;
        if (
          currentData?.data &&
          (currentData.data.id === propertyId ||
            currentData.data.id === String(propertyId))
        ) {
          const updatedData = {
            ...currentData,
            data: {
              ...currentData.data,
              is_favorite: isFavorite,
            },
          };
          queryClient.setQueryData(query.queryKey, updatedData);
        }
      });
    },
    [queryClient]
  );

  /**
   * Invalidate all property-related queries to force fresh data
   */
  const invalidateAllPropertyQueries = useCallback(() => {
    // Invalidate all properties queries (main listing, favorites, etc.)
    queryClient.invalidateQueries({
      queryKey: ["properties"],
      exact: false,
    });

    // Invalidate individual property queries
    queryClient.invalidateQueries({
      queryKey: ["getProperty"],
      exact: false,
    });
  }, [queryClient]);

  /**
   * Remove a property from favorites cache (useful when unfavoriting)
   * This specifically removes the property from the favorites list cache
   */
  const removeFromFavoritesCache = useCallback(
    (propertyId) => {
      const queryCache = queryClient.getQueryCache();
      const favoritesQueries = queryCache.findAll({
        queryKey: ["properties"],
        exact: false,
      });

      favoritesQueries.forEach((query) => {
        // Check if this is a favorites query (has is_favorite=true in params)
        const queryKeyString = JSON.stringify(query.queryKey);
        if (queryKeyString.includes('"is_favorite":true')) {
          const currentData = query.state.data;
          if (currentData?.data && Array.isArray(currentData.data)) {
            const updatedData = {
              ...currentData,
              data: currentData.data.filter(
                (property) =>
                  property.id !== propertyId &&
                  property.id !== String(propertyId)
              ),
            };
            queryClient.setQueryData(query.queryKey, updatedData);
          }
        }
      });
    },
    [queryClient]
  );

  return {
    updateFavoriteInCache,
    invalidateAllPropertyQueries,
    removeFromFavoritesCache,
  };
};

export default useFavoriteSync;