import React from 'react';
import BaseInput from '../inputs';
import IconButton from '../button/iconbutton';

const ChatFooter = () => {
  return (
    <div className="chat-footer d-flex align-items-center w-100">
      <BaseInput
        placeholder="Enter message here...."
        suffix={<img src="../admin/assets/img/input-icon.png" alt="input icon" />}
      />
      <IconButton
        title="Share"
        icon={<img src="../admin/assets/img/drop-icon.png" alt="drop icon" />}
        className="share-btn ms-2"
      />
    </div>
  );
};

export default ChatFooter;