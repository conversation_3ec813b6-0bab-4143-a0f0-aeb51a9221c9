import React from "react";
import InnerLayout from "../../components/shared/layout/innerlayout";
import PropertyCard from "../../components/shared/card/propertycard";
import SearchBar from "../../components/shared/inputs/searchbar";
import IconButton from "../../components/shared/button/iconbutton";
import BaseInput from "../../components/shared/inputs";

const dummyProperties = [
  {
    title: "2-Bedroom | Vacant | City View | Chiller on DEWA",
    location: "Orlando, Florida ",
    price: "$4,200,000",
    detail: "BuyOwn House Properties is delighted to present this large 7-bedroom villa plus",
    bath_tub: 2,
    bed_room: 2,
    square_feet: "220 Sq. Yd",
   
  },
   {
    title: "2-Bedroom | Vacant | City View | Chiller on DEWA",
    location: "Orlando, Florida ",
    price: "$4,200,000",
    detail: "BuyOwn House Properties is delighted to present this large 7-bedroom villa plus",
    bath_tub: 2,
    bed_room: 2,
    square_feet: "220 Sq. Yd",
  },
   {
    title: "2-Bedroom | Vacant | City View | Chiller on DEWA",
    location: "Orlando, Florida ", 
    price: "$4,200,000",
    detail: "BuyOwn House Properties is delighted to present this large 7-bedroom villa plus",
    bath_tub: 2,
    bed_room: 2,
    square_feet: "220 Sq. Yd",
  },
   {
    title: "2-Bedroom | Vacant | City View | Chiller on DEWA",
    location: "Orlando, Florida ",
    price: "$4,200,000",
    detail: "BuyOwn House Properties is delighted to present this large 7-bedroom villa plus",
    bath_tub: 2,
    bed_room: 2,
    square_feet: "220 Sq. Yd",
  },
   {
    title: "2-Bedroom | Vacant | City View | Chiller on DEWA",
    location: "Orlando, Florida ",
    price: "$4,200,000",
    detail: "BuyOwn House Properties is delighted to present this large 7-bedroom villa plus",
    bath_tub: 2,
    bed_room: 2,
    square_feet: "220 Sq. Yd",
  },
   {
    title: "2-Bedroom | Vacant | City View | Chiller on DEWA",
    location: "Orlando, Florida ",
    price: "$4,200,000",
    detail: "BuyOwn House Properties is delighted to present this large 7-bedroom villa plus",
    bath_tub: 2,
    bed_room: 2,
    square_feet: "220 Sq. Yd",
  },
   {
    title: "2-Bedroom | Vacant | City View | Chiller on DEWA",
    location: "Orlando, Florida ",
    price: "$4,200,000",
    detail: "BuyOwn House Properties is delighted to present this large 7-bedroom villa plus",
    bath_tub: 2,
    bed_room: 2,
    square_feet: "220 Sq. Yd",
  },
   {
    title: "2-Bedroom | Vacant | City View | Chiller on DEWA",
    location: "Orlando, Florida ",
    price: "$4,200,000",
    detail: "BuyOwn House Properties is delighted to present this large 7-bedroom villa plus",
    bath_tub: 2,
    bed_room: 2,
    square_feet: "220 Sq. Yd",
  },
   {
    title: "2-Bedroom | Vacant | City View | Chiller on DEWA",
    location: "Orlando, Florida ",
    price: "$4,200,000",
    detail: "BuyOwn House Properties is delighted to present this large 7-bedroom villa plus",
    bath_tub: 2,
    bed_room: 2,
    square_feet: "220 Sq. Yd",
  },
   {
    title: "2-Bedroom | Vacant | City View | Chiller on DEWA",
    location: "Orlando, Florida ",
    price: "$4,200,000",
    detail: "BuyOwn House Properties is delighted to present this large 7-bedroom villa plus",
    bath_tub: 2,
    bed_room: 2,
    square_feet: "220 Sq. Yd",
  },
   {
    title: "2-Bedroom | Vacant | City View | Chiller on DEWA",
    location: "Orlando, Florida ",
    price: "$4,200,000",
    detail: "BuyOwn House Properties is delighted to present this large 7-bedroom villa plus",
    bath_tub: 2,
    bed_room: 2,
    square_feet: "220 Sq. Yd",
  },
  

  // Add more as needed
];
const State = () => {
  return (
    <InnerLayout>
      <div className="container-fluid">
        <div className="row">
          <div className="col-12">
                <SearchBar />
          </div>
          <div className="col-12 mt-3">
            <div className="d-flex align-items-center justify-content-between">
                <div>
                    <p className="font-36 font-600">Jersey City</p>
                </div>
                <div className="d-flex align-items-center state-select-area">
                    <BaseInput type="select" placeholder="States" className="ms-3" />
                    <BaseInput type="select" placeholder="Cities"  />
                </div>
            </div>
          </div>
        </div>
        <div className="row mt-3">
          {dummyProperties.map((item, index) => (
            <div key={index} className="col-12 col-sm-6 col-md-4 col-lg-3">
              <PropertyCard {...item} />
            </div>
          ))}
        </div>
      </div>
    </InnerLayout>
  );
};

export default State;
