.inputs-area .sreach{
    background-color: #3883E2;
    padding: 1px 8px 0 8px;
    border-top-left-radius: 10px;
    border-top-right-radius: 12px;

}
.bg-blue{
    background-color: #3883E2;
    padding: 0 10px 6px 10px;
    border-radius: 12px;
}
.inputs-area .ant-form-item{
    margin-top: 6px;
}
.inputs-area  .ant-input-affix-wrapper{
    height: 65px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
.inputs-area .ant-select-arrow {
    background: #3883E2;
    width: 22px;
    height: 22px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    /* padding: 10px; */
    top: 41% !important;
}

.inputs-area  .ant-select-arrow svg {
    color: #fff;
    margin: 0 !important;
    padding: 0 !;
}

.property-card {
    margin-bottom: 20px;
}
.property-card .p-card-header {
    width: 100%;
    height: 210px;
    overflow: hidden;
    border-radius: 8px;
}

.property-card .p-card-header img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.p-card-body {
    padding: 10px 20px;
}

p.detail-para {
    color: #B2B6B5;
}

.p-card-footer {
    display: flex;
    align-items: center;
    justify-content: center;
}
.property-card .border{
    border: 1px solid #B2B6B5 !important;
    border-top: 0 !important;
}