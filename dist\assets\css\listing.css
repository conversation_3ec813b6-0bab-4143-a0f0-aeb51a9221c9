.single-img {
    width: 100%;
    height: 500px;
    border-radius: 12px;
    overflow: hidden;
}

.single-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 11px;
}


.show-more-img {
    background-image: url("../img/img-text.png");
    width: 100%;
    height: 240px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    border-radius: 12px;


}

.map-area>div {
    margin-top: 10px;
    height: 240px !important;
    border-radius: 12px;
}

.property-detail-area {
    background-color: #2679DF;
    box-shadow: 0px 4.6px 9.2px 0px #3883E280;
    border-radius: 8.63px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.property-detail-area p {
    color: #fff;
}

.item-border-bottom {
    border-bottom: 1px solid #7A747433;
}

.base-img img {
    width: 32px;
    height: 32px;
}

.fav-icon svg {
    font-size: 28px;
    color: #FF0000;
}

.add-listing input,
.add-listing textarea {
    background: transparent !important;
}

.add-listing select {
    background: transparent !important;
    padding: 10px;

}

.add-listing .ant-select-selector {
    background: transparent !important;
    padding: 10px;
}

.input-radio .ant-form-item {
    margin-top: 7px;
}

.or-line {
    position: relative;
}

.input-radio .ant-form-item {
    margin-top: 7px;
}

.or-line::before {
    content: "";
    position: absolute;
    width: 10%;
    height: 2px;
    background: #E7E7E7;
    right: 51%;
    top: 45%;
}


.or-line::after {
    content: "";
    position: absolute;
    width: 10%;
    height: 2px;
    background: #E7E7E7;
    left: 51%;
    top: 45%;
}

.ant-upload.ant-upload-select {
    width: 100%;
}

span.ant-upload {
    background: #fff;
}

.browse-file {
    border: 1px solid #3883E2;
    background-color: transparent !important;
    color: #3883E2;
}

.ant-upload-list-item {
    background: #fff;
    border: 1px solid transparent;
}

.custom-upload-item {
    background: #fff;
    margin-bottom: 10px;
    padding: 16px;
    border-radius: 12px;
}

.ant-upload-wrapper .ant-upload-drag {
    height: auto;
}

.show-more-img p,
.show-more-img img {
    display: none;
}

.sphere-post {
    height: 180px;
    width: 100%;
    border-radius: 12px;
    overflow: hidden;
}

.sphere-post img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
}

.sphere-card {
    min-height: 420px;
}

.add-listing .ant-input-outlined {
    background-color: transparent !important;
}

.profile-detail-area {
    background-color: #2679DF;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0px 8.07px 16.15px 0px #0000001F;

}

.profile-detail-area .profile {
    width: 72px;
    height: 72px;
    object-fit: cover;
}

.profile-detail-area .profile img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
}

.profile-detail-area .detail p {
    color: #fff;
}

.profile-detail-area .detail .white-light {
    color: rgba(241, 241, 241, 0.8);
}

.profile-detail-area .number-button {
    color: #fff !important;
    border: 1px solid #fff;
    background-color: transparent;
    width: fit-content;
    padding: 10px 20px;
    height: 40px;
    border-radius: 8px;
    font-weight: 600;

}

.number-button::before {

    background-color: #fff;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    border-radius: 8px;
    z-index: 0;
    transition: width 0.4s ease;

}

.number-button:hover::before {
    width: 100%;
    color: #fff !important;
    border: 1px solid #fff;
}

.number-button:hover {
    border: 1px solid transparent !important;
    background-color: transparent;
}

.number-button:hover span {
    color: #3883E2 !important;
}

.number-button:hover img {
    filter: brightness(0) saturate(100%) invert(31%) sepia(97%) saturate(442%) hue-rotate(184deg) brightness(97%) contrast(89%);
}

.number-button img {
    filter: brightness(0) invert(1);
}

.ant-popover .ant-popover-inner {
    background: transparent !important;
    box-shadow: none !important;
}