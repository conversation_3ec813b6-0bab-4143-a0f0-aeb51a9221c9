import InnerLayout from "../../components/shared/layout/innerlayout";
import OptionList from "../../components/shared/list/OptionList";

const About = () => {
 
   const aboutOptions = [
    { label: "Terms & Conditions", link: "/about/terms" },
    { label: "Privacy Policy", link: "/about/privacy" },
    { label: "FAQs", link: "/about/faq" },
  ];
  return (
    <InnerLayout>
      <div className="container-fluid">
        <div className="row">
          <div className="col-12 mt-4">
            <p className="font-36 font-600 color-black">About</p>
          </div>
          <div className="col-12">
            <OptionList options={aboutOptions} />
          </div>
        </div>
      </div>
    </InnerLayout>
  );
};

export default About;
