import React from "react";
import { Form } from "antd";
import { CardElement } from "@stripe/react-stripe-js";

const StripeCardField = ({
  name,
  rules,
  label,
  className,
  disabled,
  onChange,
}) => {
  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
        fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
        fontSmoothing: 'antialiased',
      },
      invalid: {
        color: '#9e2146',
      },
    },
    hidePostalCode: false,
  };

  return (
    <Form.Item 
      label={label} 
      name={name} 
      rules={rules} 
      validateTrigger="onBlur"
    >
      <div className={`stripe-card-element ${className || ''}`}>
        <CardElement
          options={cardElementOptions}
          onChange={onChange}
          disabled={disabled}
        />
      </div>
    </Form.Item>
  );
};

export default StripeCardField;