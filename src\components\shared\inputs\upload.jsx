import React, { useState } from 'react';
import { InboxOutlined } from '@ant-design/icons';
import { Upload, message } from 'antd';

const { Dragger } = Upload;

const App = () => {
  const [fileList, setFileList] = useState([]);

  const props = {
    name: 'file',
    multiple: true,
    action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
    showUploadList: false, // Prevent list from showing inside the dragger
    onChange(info) {
      let newList = [...info.fileList];

      setFileList(newList);

      const { status } = info.file;
      if (status === 'done') {
        message.success(`${info.file.name} uploaded successfully.`);
      } else if (status === 'error') {
        message.error(`${info.file.name} upload failed.`);
      }
    },
    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };

  return (
    <>
      <Dragger {...props} style={{ height: 200 }}>
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">
          Click or drag file to this area to upload
        </p>
        <p className="ant-upload-hint">
          File list will appear below. This area won’t resize.
        </p>
      </Dragger>

      {/* Render file list below */}
      <div style={{ marginTop: 20 }}>
        {fileList.map((file) => (
          <div
            key={file.uid}
            style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: 8,
              border: '1px solid #ddd',
              padding: 10,
              borderRadius: 4,
            }}
          >
            {file.thumbUrl ? (
              <img
                src={file.thumbUrl}
                alt={file.name}
                style={{ width: 40, height: 40, marginRight: 10 }}
              />
            ) : (
              <InboxOutlined style={{ fontSize: 32, marginRight: 10 }} />
            )}
            <div>
              <div>{file.name}</div>
              <div style={{ fontSize: 12, color: '#999' }}>
                {file.status === 'uploading' && `${file.percent || 0}% uploading`}
                {file.status === 'done' && 'Uploaded'}
                {file.status === 'error' && 'Error'}
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default App;
