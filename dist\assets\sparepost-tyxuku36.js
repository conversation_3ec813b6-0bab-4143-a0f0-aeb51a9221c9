import{j as s,L as u,r as y}from"./index-3mE9H3a0.js";import{R as b,I as f,C as g}from"./interactionbox-B9SrINfg.js";import{C as w}from"./index-C-I6oq0X.js";import{C}from"./index-kHmX87X2.js";import{B as k}from"./index-Cd-Wothc.js";import{F as B}from"./flatbutton-Yo0mdDJ8.js";const I=({images:a})=>s.jsx("div",{className:"",children:s.jsx("div",{className:"row mb-4",children:a.map((l,e)=>e===0?s.jsx("div",{className:"col-4",children:s.jsx("img",{src:l,alt:`gallery-${e}`,className:"rounded-md object-cover w-100"})},e):e===1?s.jsx("div",{className:"col-8",children:s.jsx("img",{src:l,alt:`gallery-${e}`,className:"rounded-md object-cover w-100"})},e):s.jsx("div",{className:"col",children:s.jsx("div",{className:"gallery-img",children:s.jsx("img",{src:l,alt:`gallery-${e}`,className:"rounded-md object-cover w-100"})})},e))})}),d=({avatar:a,name:l,time:e})=>s.jsxs("div",{className:"d-flex align-items-center",children:[s.jsx("img",{src:a,alt:"avatar",className:"rounded-circle",width:40,height:40}),s.jsxs("div",{className:"ms-2",children:[s.jsx("h6",{className:"mb-0",children:l}),s.jsx("small",{className:"text-muted",children:e})]})]}),R=({isReply:a,mycomment:l,commentlink:e,likes:r,shares:m,avatar:n})=>s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:a?"mycomment comment-reply ms-4":"mycomment",children:[n,s.jsxs("div",{className:a?"reply-bg ms-5":"ms-5",children:[s.jsx("p",{className:"text-muted mt-2 ",children:l}),s.jsx(u,{className:"color-blue  mb-3",children:e})]})]}),s.jsxs("div",{className:"ms-4 mb-4",children:[s.jsxs("span",{className:"me-2",children:[s.jsx(b,{})," ",s.jsx("span",{className:"font-14",children:r})]}),s.jsxs("span",{className:"me-2",children:[s.jsx("img",{src:"/assets/img/share-icon.png",alt:""}),s.jsxs("span",{className:"font-14",children:[" ",m," "]})]})]})," "]}),$=({user:a,body:l,likes:e,repost:r,comments:m,reactions:n,images:x,user_comment:c,type:j="shareit"})=>{const[h,i]=y.useState(!1),p=()=>{i(!0)},o=()=>{i(!1)},N=()=>{i(!1)};return s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"post-card p-3 rounded border bg-white mb-5",children:[s.jsxs("div",{className:"d-flex justify-content-between align-items-start mb-2",children:[s.jsx("div",{className:"d-flex align-items-center",children:s.jsx(d,{avatar:a.avatar,name:a.name,time:a.time})}),s.jsx("div",{children:s.jsx(w,{title:"⋮",overlayClassName:"my-drop",icon:!1,items:[{key:"1",label:s.jsx("p",{children:"Copy Link"})},{key:"2",label:s.jsx("p",{onClick:p,children:"Report Post"})}]})})]}),s.jsx("p",{className:"text-muted mt-2 mb-3",children:l}),j==="shareit"&&s.jsx(I,{images:x}),s.jsx(f,{reactions:n,comments:m,repost:r,likes:e,isSpareCard:!0}),c==null?void 0:c.map((t,v)=>s.jsx(R,{isReply:t.isReply,avatar:s.jsx(d,{avatar:t.avatar,name:t.name,time:t.time}),mycomment:t.mycomment,commentlink:t.commentlink,likes:t.likes,shares:t.shares},v)),s.jsx(g,{avatar:a.avatar,type:"text",className:"form-control w-100 ",placeholder:"Add your comments"})]}),s.jsxs(C,{title:"Report",closable:{"aria-label":"Custom Close Button"},open:h,onOk:o,onCancel:N,className:"report-modal",footer:!1,width:"700px",children:[s.jsx("p",{children:"Please enter below any additional details you would want to share with us"}),s.jsx(k,{placeholder:"Submit a reason for reporting this post",rows:6,type:"textarea"}),s.jsx(B,{title:"Submit",className:"modal-submit",onClick:o})]})]})};export{$ as S};
