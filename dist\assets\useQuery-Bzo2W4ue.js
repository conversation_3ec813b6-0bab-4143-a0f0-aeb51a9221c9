var dt=e=>{throw TypeError(e)};var G=(e,t,s)=>t.has(e)||dt("Cannot "+s);var i=(e,t,s)=>(G(e,t,"read from private field"),s?s.call(e):t.get(e)),b=(e,t,s)=>t.has(e)?dt("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s),d=(e,t,s,r)=>(G(e,t,"write to private field"),r?r.call(e,s):t.set(e,s),s),g=(e,t,s)=>(G(e,t,"access private method"),s);import{ad as Ot,ae as ft,af as P,ag as X,ah as A,ai as Y,aj as Z,ak as pt,al as wt,am as Et,an as Pt,ao as gt,ap as vt,r as S,aq as Tt,D as Qt,ar as xt,T as It}from"./index-3mE9H3a0.js";var R,a,B,y,I,M,T,Q,z,_,k,D,F,x,L,h,q,tt,et,st,it,rt,at,nt,St,Rt,Dt=(Rt=class extends Ot{constructor(t,s){super();b(this,h);b(this,R);b(this,a);b(this,B);b(this,y);b(this,I);b(this,M);b(this,T);b(this,Q);b(this,z);b(this,_);b(this,k);b(this,D);b(this,F);b(this,x);b(this,L,new Set);this.options=s,d(this,R,t),d(this,Q,null),d(this,T,ft()),this.options.experimental_prefetchInRender||i(this,T).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(i(this,a).addObserver(this),bt(i(this,a),this.options)?g(this,h,q).call(this):this.updateResult(),g(this,h,it).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return ot(i(this,a),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return ot(i(this,a),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,g(this,h,rt).call(this),g(this,h,at).call(this),i(this,a).removeObserver(this)}setOptions(t){const s=this.options,r=i(this,a);if(this.options=i(this,R).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof P(this.options.enabled,i(this,a))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");g(this,h,nt).call(this),i(this,a).setOptions(this.options),s._defaulted&&!X(this.options,s)&&i(this,R).getQueryCache().notify({type:"observerOptionsUpdated",query:i(this,a),observer:this});const c=this.hasListeners();c&&yt(i(this,a),r,this.options,s)&&g(this,h,q).call(this),this.updateResult(),c&&(i(this,a)!==r||P(this.options.enabled,i(this,a))!==P(s.enabled,i(this,a))||A(this.options.staleTime,i(this,a))!==A(s.staleTime,i(this,a)))&&g(this,h,tt).call(this);const o=g(this,h,et).call(this);c&&(i(this,a)!==r||P(this.options.enabled,i(this,a))!==P(s.enabled,i(this,a))||o!==i(this,x))&&g(this,h,st).call(this,o)}getOptimisticResult(t){const s=i(this,R).getQueryCache().build(i(this,R),t),r=this.createResult(s,t);return Ut(this,r)&&(d(this,y,r),d(this,M,this.options),d(this,I,i(this,a).state)),r}getCurrentResult(){return i(this,y)}trackResult(t,s){return new Proxy(t,{get:(r,c)=>(this.trackProp(c),s==null||s(c),Reflect.get(r,c))})}trackProp(t){i(this,L).add(t)}getCurrentQuery(){return i(this,a)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const s=i(this,R).defaultQueryOptions(t),r=i(this,R).getQueryCache().build(i(this,R),s);return r.fetch().then(()=>this.createResult(r,s))}fetch(t){return g(this,h,q).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),i(this,y)))}createResult(t,s){var lt;const r=i(this,a),c=this.options,o=i(this,y),n=i(this,I),m=i(this,M),p=t!==r?t.state:i(this,B),{state:C}=t;let u={...C},w=!1,l;if(s._optimisticResults){const O=this.hasListeners(),W=!O&&bt(t,s),U=O&&yt(t,r,s,c);(W||U)&&(u={...u,...Pt(C.data,t.options)}),s._optimisticResults==="isRestoring"&&(u.fetchStatus="idle")}let{error:j,errorUpdatedAt:N,status:v}=u;l=u.data;let H=!1;if(s.placeholderData!==void 0&&l===void 0&&v==="pending"){let O;o!=null&&o.isPlaceholderData&&s.placeholderData===(m==null?void 0:m.placeholderData)?(O=o.data,H=!0):O=typeof s.placeholderData=="function"?s.placeholderData((lt=i(this,k))==null?void 0:lt.state.data,i(this,k)):s.placeholderData,O!==void 0&&(v="success",l=gt(o==null?void 0:o.data,O,s),w=!0)}if(s.select&&l!==void 0&&!H)if(o&&l===(n==null?void 0:n.data)&&s.select===i(this,z))l=i(this,_);else try{d(this,z,s.select),l=s.select(l),l=gt(o==null?void 0:o.data,l,s),d(this,_,l),d(this,Q,null)}catch(O){d(this,Q,O)}i(this,Q)&&(j=i(this,Q),l=i(this,_),N=Date.now(),v="error");const K=u.fetchStatus==="fetching",$=v==="pending",J=v==="error",ct=$&&K,ut=l!==void 0,E={status:v,fetchStatus:u.fetchStatus,isPending:$,isSuccess:v==="success",isError:J,isInitialLoading:ct,isLoading:ct,data:l,dataUpdatedAt:u.dataUpdatedAt,error:j,errorUpdatedAt:N,failureCount:u.fetchFailureCount,failureReason:u.fetchFailureReason,errorUpdateCount:u.errorUpdateCount,isFetched:u.dataUpdateCount>0||u.errorUpdateCount>0,isFetchedAfterMount:u.dataUpdateCount>p.dataUpdateCount||u.errorUpdateCount>p.errorUpdateCount,isFetching:K,isRefetching:K&&!$,isLoadingError:J&&!ut,isPaused:u.fetchStatus==="paused",isPlaceholderData:w,isRefetchError:J&&ut,isStale:ht(t,s),refetch:this.refetch,promise:i(this,T)};if(this.options.experimental_prefetchInRender){const O=V=>{E.status==="error"?V.reject(E.error):E.data!==void 0&&V.resolve(E.data)},W=()=>{const V=d(this,T,E.promise=ft());O(V)},U=i(this,T);switch(U.status){case"pending":t.queryHash===r.queryHash&&O(U);break;case"fulfilled":(E.status==="error"||E.data!==U.value)&&W();break;case"rejected":(E.status!=="error"||E.error!==U.reason)&&W();break}}return E}updateResult(){const t=i(this,y),s=this.createResult(i(this,a),this.options);if(d(this,I,i(this,a).state),d(this,M,this.options),i(this,I).data!==void 0&&d(this,k,i(this,a)),X(s,t))return;d(this,y,s);const r=()=>{if(!t)return!0;const{notifyOnChangeProps:c}=this.options,o=typeof c=="function"?c():c;if(o==="all"||!o&&!i(this,L).size)return!0;const n=new Set(o??i(this,L));return this.options.throwOnError&&n.add("error"),Object.keys(i(this,y)).some(m=>{const f=m;return i(this,y)[f]!==t[f]&&n.has(f)})};g(this,h,St).call(this,{listeners:r()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&g(this,h,it).call(this)}},R=new WeakMap,a=new WeakMap,B=new WeakMap,y=new WeakMap,I=new WeakMap,M=new WeakMap,T=new WeakMap,Q=new WeakMap,z=new WeakMap,_=new WeakMap,k=new WeakMap,D=new WeakMap,F=new WeakMap,x=new WeakMap,L=new WeakMap,h=new WeakSet,q=function(t){g(this,h,nt).call(this);let s=i(this,a).fetch(this.options,t);return t!=null&&t.throwOnError||(s=s.catch(Y)),s},tt=function(){g(this,h,rt).call(this);const t=A(this.options.staleTime,i(this,a));if(Z||i(this,y).isStale||!pt(t))return;const r=wt(i(this,y).dataUpdatedAt,t)+1;d(this,D,setTimeout(()=>{i(this,y).isStale||this.updateResult()},r))},et=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(i(this,a)):this.options.refetchInterval)??!1},st=function(t){g(this,h,at).call(this),d(this,x,t),!(Z||P(this.options.enabled,i(this,a))===!1||!pt(i(this,x))||i(this,x)===0)&&d(this,F,setInterval(()=>{(this.options.refetchIntervalInBackground||Et.isFocused())&&g(this,h,q).call(this)},i(this,x)))},it=function(){g(this,h,tt).call(this),g(this,h,st).call(this,g(this,h,et).call(this))},rt=function(){i(this,D)&&(clearTimeout(i(this,D)),d(this,D,void 0))},at=function(){i(this,F)&&(clearInterval(i(this,F)),d(this,F,void 0))},nt=function(){const t=i(this,R).getQueryCache().build(i(this,R),this.options);if(t===i(this,a))return;const s=i(this,a);d(this,a,t),d(this,B,t.state),this.hasListeners()&&(s==null||s.removeObserver(this),t.addObserver(this))},St=function(t){vt.batch(()=>{t.listeners&&this.listeners.forEach(s=>{s(i(this,y))}),i(this,R).getQueryCache().notify({query:i(this,a),type:"observerResultsUpdated"})})},Rt);function Ft(e,t){return P(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function bt(e,t){return Ft(e,t)||e.state.data!==void 0&&ot(e,t,t.refetchOnMount)}function ot(e,t,s){if(P(t.enabled,e)!==!1&&A(t.staleTime,e)!=="static"){const r=typeof s=="function"?s(e):s;return r==="always"||r!==!1&&ht(e,t)}return!1}function yt(e,t,s,r){return(e!==t||P(r.enabled,e)===!1)&&(!s.suspense||e.state.status!=="error")&&ht(e,s)}function ht(e,t){return P(t.enabled,e)!==!1&&e.isStaleByTime(A(t.staleTime,e))}function Ut(e,t){return!X(e.getCurrentResult(),t)}var Ct=S.createContext(!1),Mt=()=>S.useContext(Ct);Ct.Provider;function _t(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var kt=S.createContext(_t()),Lt=()=>S.useContext(kt),jt=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},Nt=e=>{S.useEffect(()=>{e.clearReset()},[e])},qt=({result:e,errorResetBoundary:t,throwOnError:s,query:r,suspense:c})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&(c&&e.data===void 0||Tt(s,[e.error,r])),At=e=>{if(e.suspense){const t=r=>r==="static"?r:Math.max(r??1e3,1e3),s=e.staleTime;e.staleTime=typeof s=="function"?(...r)=>t(s(...r)):t(s),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3))}},Bt=(e,t)=>e.isLoading&&e.isFetching&&!t,zt=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,mt=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function Ht(e,t,s){var u,w,l,j,N;const r=Mt(),c=Lt(),o=Qt(),n=o.defaultQueryOptions(e);(w=(u=o.getDefaultOptions().queries)==null?void 0:u._experimental_beforeQuery)==null||w.call(u,n),n._optimisticResults=r?"isRestoring":"optimistic",At(n),jt(n,c),Nt(c);const m=!o.getQueryCache().get(n.queryHash),[f]=S.useState(()=>new t(o,n)),p=f.getOptimisticResult(n),C=!r&&e.subscribed!==!1;if(S.useSyncExternalStore(S.useCallback(v=>{const H=C?f.subscribe(vt.batchCalls(v)):Y;return f.updateResult(),H},[f,C]),()=>f.getCurrentResult(),()=>f.getCurrentResult()),S.useEffect(()=>{f.setOptions(n)},[n,f]),zt(n,p))throw mt(n,f,c);if(qt({result:p,errorResetBoundary:c,throwOnError:n.throwOnError,query:o.getQueryCache().get(n.queryHash),suspense:n.suspense}))throw p.error;if((j=(l=o.getDefaultOptions().queries)==null?void 0:l._experimental_afterQuery)==null||j.call(l,n,p),n.experimental_prefetchInRender&&!Z&&Bt(p,r)){const v=m?mt(n,f,c):(N=o.getQueryCache().get(n.queryHash))==null?void 0:N.promise;v==null||v.catch(Y).finally(()=>{f.updateResult()})}return n.notifyOnChangeProps?p:f.trackResult(p)}function Wt(e,t){return Ht(e,Dt)}function Vt(e,t={}){const{params:s,slug:r,showSuccessNotification:c=!1,token:o,...n}=t,m=xt[e];if(!m)throw new Error(`API endpoint "${e}" not found`);const f=S.useMemo(()=>{const C=[e];if(r&&C.push(r),s&&Object.keys(s).length>0){const u=Object.keys(s).sort().reduce((w,l)=>(w[l]=s[l],w),{});C.push(JSON.stringify(u))}return C},[e,r,s]),p=S.useMemo(()=>async()=>{try{const u=m.method==="POST"&&c,{data:w,pagination:l}=await It.request(e,{params:s,slug:r,showSuccessNotification:u,token:o});return{data:w,pagination:l}}catch(C){throw C}},[e,r,s,c,m.method]);return Wt({queryKey:f,queryFn:p,select:n.select,enabled:n.enabled!==!1,placeholderData:n.placeholderData,...n})}function Gt(e,t={}){var f;const[s,r]=S.useState(t.initialPage||1),[c,o]=S.useState(t.initialPageSize||10),n={...t.params||{},page:s,limit:c},m=Vt(e,{...t,params:n,placeholderData:t.keepPreviousData?p=>p:void 0,refetchOnWindowFocus:!1,staleTime:5*60*1e3,gcTime:10*60*1e3,select:t.select,notifyOnChangeProps:["data","error","isLoading"],showSuccessNotification:t.showSuccessNotification});return{...m,page:s,pageSize:c,setPage:r,setPageSize:o,nextPage:()=>r(p=>p+1),prevPage:()=>r(p=>Math.max(1,p-1)),goToPage:p=>r(p),pagination:((f=m.data)==null?void 0:f.pagination)||window.globalPagination||{}}}export{Vt as a,Gt as u};
