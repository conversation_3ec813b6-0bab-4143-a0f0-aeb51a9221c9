import React from "react";
import InnerLayout from "@/components/shared/layout/innerlayout";
import PropertyCard from "@/components/shared/card/propertycard";
import { Skeleton } from "antd";
import ReusablePagination from "@/components/shared/ReusablePagination";
import EmptyState from "@/components/shared/EmptyState";
import useSearchFilterPagination from "@/hooks/useSearchFilterPagination";
import { transformPropertiesData } from "@/utils/propertyUtils";
import _ from "lodash";

const Favourites = () => {
  // Use the search filter pagination hook but only for favorites
  const { data, isLoading, pagination, handlePageChange } =
    useSearchFilterPagination("properties", {
      pageSize: 12,
      // Add default filter for favorites
      defaultParams: {
        is_favorite: true,
      },
    });

  // Transform server data to match PropertyCard props without actions
  const properties = React.useMemo(() => {
    if (!data?.data) return [];
    return transformPropertiesData(data.data, {
      showActions: false, // Remove edit/delete actions
    });
  }, [data]);

  return (
    <InnerLayout>
      <div className="container-fluid">
        <div className="row">
          <div className="col-12 mt-3">
            <p className="font-36 font-600 color-black mb-4">Favourites</p>
          </div>
        </div>

        <div className="row mt-3">
          {/* Show skeletons when loading */}
          {isLoading ? (
            Array.from({ length: 12 }).map((_, index) => (
              <div key={index} className="col-12 col-sm-6 col-md-4 col-lg-3">
                <Skeleton active paragraph={{ rows: 4 }} />
              </div>
            ))
          ) : !_.isEmpty(properties) ? (
            properties.map((item, index) => (
              <div
                key={item.id || index}
                className="col-12 col-sm-6 col-md-4 col-lg-3"
              >
                <PropertyCard {...item} />
              </div>
            ))
          ) : (
            <EmptyState
              title="No favorite properties found"
              description="You haven't added any properties to your favorites yet"
            />
          )}
        </div>

        {/* Reusable Pagination */}
        <ReusablePagination
          pagination={pagination}
          handlePageChange={handlePageChange}
          isLoading={isLoading}
          itemName="favorite properties"
          pageSizeOptions={["12", "24", "48"]}
        />
      </div>
    </InnerLayout>
  );
};

export default Favourites;
