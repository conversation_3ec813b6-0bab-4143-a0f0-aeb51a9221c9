import React from "react";
import InnerLayout from "@/components/shared/layout/innerlayout";
import NotificationItem from "@/components/partial/notificationitems/notificationitem";


const notifications = [
  {
    avatar: "/assets/img/avatar.png",
    message: "<PERSON> started following you.",
    time: "5 days ago",
  },
  {
    avatar: "/assets/img/avatar.png",
    message: "<PERSON><PERSON><PERSON> liked your post.",
    time: "2 days ago",
  },
  {
    avatar: "/assets/img/avatar.png",
    message: "<PERSON> mentioned you in a comment.",
    time: "1 day ago",
  },
];
const Notifications = () => {
  return (
    <div>
      <InnerLayout>
        <div className="container-fluid">
          <div className="row">
            <div className="col-12 mt-4">
              <p className="font-36 color-black font-600 ">Notifications</p>
            </div>
            <div className="col-12">
              {notifications.map((item, index) => (
                <NotificationItem
                  key={index}
                  avatar={item.avatar}
                  message={item.message}
                  time={item.time}
                />
              ))}
            </div>
          </div>
        </div>
      </InnerLayout>
    </div>
  );
};

export default Notifications;
