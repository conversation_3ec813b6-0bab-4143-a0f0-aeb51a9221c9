import React, { useState } from "react";
import { Form, Input } from "antd";
import "react-phone-number-input/style.css";
import PhoneInput from "react-phone-number-input";
import { formatPhoneNumber } from "@/config/rules";

const PhoneInputField = ({
  name,
  label,
  rules,
  placeholder,
  className,
  disabled,
  onChange,
  defaultCountry = "US",
}) => {
  const [formatted, setFormatted] = useState("");
  const formatPhone = (input) => {
    const digits = input.replace(/\D/g, "").slice(0, 10); // Only digits, max 10
    const part1 = digits.slice(0, 3);
    const part2 = digits.slice(3, 6);
    const part3 = digits.slice(6, 10);
    let formatted = "";
    if (part1) formatted += `(${part1}`;
    if (part1 && part1.length === 3) formatted += `) `;
    if (part2) formatted += part2;
    if (part2 && part2.length === 3) formatted += `-`;
    if (part3) formatted += part3;
    return formatted;
  };
  const handleChange = (e) => {
    const input = e.target.value;
    const formattedValue = formatPhone(input);
    setFormatted(formattedValue);
    onChange?.(`1${input.replace(/\D/g, "").slice(0, 10)}`); // Pass raw value with "1" prefix
  };
  return (
    <Form.Item
      label={label}
      name={name}
      rules={rules}
      validateTrigger="onBlur"
      normalize={(value) => formatPhoneNumber(value)}
    >
      <Input
        size="large"
        placeholder="(XXX) XXX-XXXX"
        value={formatted}
        onChange={handleChange}
        maxLength={14} // (************* = 14 characters
      />
    </Form.Item>
  );
};

export default PhoneInputField;
