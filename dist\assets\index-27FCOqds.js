import{j as s}from"./index-3mE9H3a0.js";import{B as c,I as d}from"./index-C-I6oq0X.js";import{B as g,R as h}from"./index-Cd-Wothc.js";import{c as n}from"./react-stripe.esm-CaXK0k-R.js";import{P as x}from"./postinput-DZ9heEdZ.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";import"./index-BUUUOhAK.js";import"./flatbutton-Yo0mdDJ8.js";import"./index-DX-6A722.js";import"./fade-yKzH711D.js";import"./index-CY48Knbi.js";import"./DeleteOutlined-DCDN0YWx.js";const j=e=>{switch(e){case"online":return"#52c41a";case"offline":return"#d9d9d9";case"away":return"#f5222d";default:return"#d9d9d9"}},u=e=>{switch(e){case"sent":return s.jsx("img",{src:"/assets/img/send-icon.png",alt:"sent"});case"viewed":return s.jsx("img",{src:"/assets/img/viewed-msg.png",alt:"viewed"});case"received":return s.jsx("p",{className:"msg-receive"});default:return null}},o=({name:e,message:a,isTyping:t,msgTime:i,msgStatus:r,onlineStatus:m,avatar:l})=>s.jsxs("div",{className:"chat-user",children:[s.jsx("div",{className:"chat-user-img",children:s.jsx(c,{color:j(m),dot:!0,children:s.jsx("img",{src:l,alt:e})})}),s.jsxs("div",{className:"chat-user-msg-area ms-3 d-flex justify-content-between w-100",children:[s.jsxs("div",{children:[s.jsx("p",{className:"font-16 color-black",children:e}),s.jsx("p",{className:"color-light font-14",children:t?s.jsx("em",{children:"Typing..."}):a})]}),s.jsxs("div",{className:"text-end",children:[s.jsx("p",{className:"font-12 color-gray",children:i}),s.jsx("div",{className:"text-end",children:u(r)})]})]})]});o.propTypes={name:n.string.isRequired,message:n.string,isTyping:n.bool,msgTime:n.string,msgStatus:n.oneOf(["sent","received","viewed"]),onlineStatus:n.oneOf(["online","offline","away"]),avatar:n.string.isRequired};o.defaultProps={message:"",isTyping:!1,msgTime:"",msgStatus:"sent",onlineStatus:"offline"};const p=({users:e})=>s.jsx(s.Fragment,{children:e.map((a,t)=>s.jsx(o,{name:a.name,message:a.message,isTyping:a.isTyping,msgTime:a.msgTime,msgStatus:a.msgStatus,onlineStatus:a.onlineStatus,avatar:a.avatar},t))}),v=({msg:e})=>{const a=e.from==="me";return s.jsx("div",{className:a?"my-chat-box":"user-chat-box",children:s.jsxs("div",{className:"d-flex ms-3",children:[!a&&s.jsx("div",{className:"chat-user-img",children:s.jsx(c,{color:"green",dot:e.onlineStatus==="online",children:s.jsx("img",{src:e.avatar,alt:""})})}),s.jsxs("div",{className:a?"me-3":"ms-3",children:[e.text&&s.jsx("div",{className:"text-user",children:s.jsx("p",{children:e.text})}),e.images&&s.jsx("div",{className:"d-flex align-items-center gap-2 flex-wrap",children:e.images.map((t,i)=>s.jsx("div",{className:"chat-img-area",children:s.jsx("img",{src:t,alt:""})},i))}),s.jsx("p",{className:"color-light mt-2",children:e.time})]}),a&&s.jsx("div",{className:"chat-user-img",children:s.jsx(c,{color:"gray",dot:!0,children:s.jsx("img",{src:e.avatar,alt:""})})})]})})},f=({user:e={}})=>{const{name:a="Alene",avatar:t="/assets/img/avatar-1.png",statusText:i="Offline",onlineStatus:r="offline"}=e;return s.jsxs("div",{className:"detail-header d-flex align-items-center justify-content-between",children:[s.jsxs("div",{className:"d-flex align-items-center",children:[s.jsx("div",{children:s.jsx("img",{src:"/assets/img/chat-toogle.png",alt:"Toggle"})}),s.jsxs("div",{className:"d-flex align-items-center ms-3",children:[s.jsx("div",{className:"chat-user-img",children:s.jsx(c,{color:r==="online"?"green":r==="offline"?"gray":"red",dot:!0,children:s.jsx("img",{src:t,alt:"User Avatar"})})}),s.jsxs("div",{className:"ms-2",children:[s.jsx("p",{children:a}),s.jsx("p",{className:"color-light",children:i})]})]})]}),s.jsx("div",{className:"d-flex align-items-center",children:["chat-1","chat-2","chat-3","chat-4"].map((m,l)=>s.jsx("div",{className:"ms-3",children:s.jsx("img",{src:`/assets/img/${m}.png`,alt:`Icon ${l}`})},l))})]})},N=({messages:e,selectedUser:a})=>s.jsxs("div",{className:"chat-detail",children:[s.jsx(f,{user:a}),s.jsx("div",{className:"chat-area",children:e.map((t,i)=>s.jsx(v,{msg:t},i))}),s.jsx("div",{className:"chat-send-area",children:s.jsx(x,{button:s.jsx("img",{src:"/assets/img/chat-send-icon.png"})})})]}),y=[{name:"Alene",message:"Hey! I need help to setup the M...",isTyping:!1,msgTime:"Now",msgStatus:"sent",onlineStatus:"online",avatar:"/assets/img/avatar-1.png"},{name:"Robert",message:"Sure, let me check it...",isTyping:!1,msgTime:"2 min ago",msgStatus:"received",onlineStatus:"offline",avatar:"/assets/img/avatar-2.png"},{name:"Elina",message:"",isTyping:!0,msgTime:"Typing...",msgStatus:null,onlineStatus:"away",avatar:"/assets/img/avatar-3.png"},{name:"David",message:"Thanks for the update!",isTyping:!1,msgTime:"5 min ago",msgStatus:"viewed",onlineStatus:"online",avatar:"/assets/img/avatar-4.png"}],S=[{from:"user",text:"Hey.. Bill, nice to meet you!",time:"9hr ago",avatar:"/assets/img/avatar-1.png",onlineStatus:"online"},{from:"me",text:"Hope you’re doing fine.",time:"9hr ago",avatar:"/assets/img/avatar-1.png"},{from:"user",text:"Distinctio architecto debitis...",images:["/assets/img/image-6.png","/assets/img/image-6.png","/assets/img/image-6.png"],time:"9hr ago",avatar:"/assets/img/avatar-1.png",onlineStatus:"away"}],T={name:"Stebin Ben",statusText:"Active 1 h ago",avatar:"/assets/img/avatar-1.png",onlineStatus:"online"},L=()=>s.jsx(d,{children:s.jsx("div",{className:"container-fluid mt-5",children:s.jsx("div",{className:"row",children:s.jsx("div",{className:"col-12",children:s.jsxs("div",{className:"chat-box",children:[s.jsxs("div",{className:"chat-listing",children:[s.jsx("div",{className:"listing-header d-flex justify-content-between",children:s.jsxs("div",{className:"d-flex",children:[s.jsx("p",{className:"color-black font-18 me-2",children:"Messages"}),s.jsx("p",{className:"msg-numbers",children:"9"})]})}),s.jsx("div",{children:s.jsx(g,{placeholder:"Search in Messenger",icon:s.jsx(h,{style:{color:"gray"}}),className:"chat-search"})}),s.jsx(p,{users:y,selectedUser:T})]}),s.jsx(N,{messages:S})]})})})})});export{L as default};
