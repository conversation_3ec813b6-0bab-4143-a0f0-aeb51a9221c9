import { useQuery } from './useQuery';

/**
 * Hook to fetch subscription products dynamically from the API
 * Returns subscription plans with Stripe pricing information
 */
export function useSubscriptionProducts(options = {}) {
  return useQuery("getSubscriptionProducts", {
    // Cache subscription products aggressively since they don't change often
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    
    // Transform data to ensure consistent structure
    select: (response) => {
      const products = response?.data || response || [];
      
      // Log products for debugging
      if (process.env.NODE_ENV === 'development') {
        console.log('Subscription products:', products);
      }
      
      // Ensure each product has required fields
      return products.map(product => ({
        id: product.id,
        name: product.name,
        description: product.description || '',
        priceId: product.priceId,
        price: product.price,
        currency: product.currency || 'usd',
        // Add formatted price for display
        formattedPrice: `$${product.price}`,
        // Add period info if available
        period: product.period || 'month',
        ...product,
      }));
    },
    
    // Error handling
    onError: (error) => {
      console.error('Failed to fetch subscription products:', error);
    },
    
    ...options,
  });
}

export default useSubscriptionProducts;
