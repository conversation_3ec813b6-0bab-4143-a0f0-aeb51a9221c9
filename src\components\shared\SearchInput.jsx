import React, { useEffect, useMemo } from "react";
import { debounce } from "lodash";
import { Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { useScopedSearch } from "@/store/ScopedSearchContext";

const SearchInput = ({ placeholder = "Search...", className = "" }) => {
  const { searchKeyword, setSearchKeyword } = useScopedSearch();

  // Create debounced function to avoid too many API calls
  const debouncedSetSearch = useMemo(
    () => debounce(setSearchKeyword, 300),
    [setSearchKeyword]
  );

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => debouncedSetSearch.cancel();
  }, [debouncedSetSearch]);

  return (
    <Input
      placeholder={placeholder}
      prefix={<SearchOutlined style={{ color: "gray", fontSize: "150%" }} />}
      value={searchKeyword}
      onChange={(e) => debouncedSetSearch(e.target.value)}
      style={{
        width: "100%",
        borderRadius: "4px",
        padding: "6px 10px",
        height: "40px",
      }}
      className={`search-bar ${className}`}
    />
  );
};

export default SearchInput;
