import { Form, Switch } from "antd";
import React from "react";

const NotificationSettingForm = () => {
  const onChange = (checked) => {};

  return (
    <Form
      name="login"
      layout="vertical"
      onFinish={() => setIsModalOpen(false)}
      initialValues={{
        remember: true,
      }}
      autoComplete="off"
    >
      <div className="d-flex align-items-center justify-content-between mt-3">
        <div>
          <p className="font-16">When someone assigned you a new job</p>
        </div>
        <div>
          <Switch defaultChecked onChange={onChange} />
        </div>
      </div>
      <div className="d-flex align-items-center justify-content-between mt-3">
        <div>
          <p className="font-16">When task started</p>
        </div>
        <div>
          <Switch onChange={onChange} />
        </div>
      </div>
      <div className="d-flex align-items-center justify-content-between mt-3">
        <div>
          <p className="font-16">When task ended</p>
        </div>
        <div>
          <Switch defaultChecked onChange={onChange} />
        </div>
      </div>
      <div className="d-flex align-items-center justify-content-between mt-3">
        <div>
          <p className="font-16">When someone assigned you a project</p>
        </div>
        <div>
          <Switch onChange={onChange} />
        </div>
      </div>
      <div className="d-flex align-items-center justify-content-between mt-3">
        <div>
          <p className="font-16">When drawing is updated/revision</p>
        </div>
        <div>
          <Switch defaultChecked onChange={onChange} />
        </div>
      </div>
      <div className="d-flex align-items-center justify-content-between mt-3">
        <div>
          <p className="font-16">When a task is overdue</p>
        </div>
        <div>
          <Switch onChange={onChange} />
        </div>
      </div>
    </Form>
  );
};

export default NotificationSettingForm;
