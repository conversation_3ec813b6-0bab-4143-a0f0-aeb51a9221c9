import React, { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardNumberElement,
  CardExpiryElement,
  CardCvcElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { Button, Form, Alert, Spin, Card } from 'antd';
import { useMutation } from '../../hooks/reactQuery';

// Initialize Stripe
const stripePromise = loadStripe(window.constants?.stripe_publish_key);

// Stripe element styles
const elementStyles = {
  style: {
    base: {
      fontSize: '16px',
      color: '#424770',
      '::placeholder': {
        color: '#aab7c4',
      },
      padding: '10px 12px',
      border: '1px solid #d9d9d9',
      borderRadius: '6px',
    },
    invalid: {
      color: '#9e2146',
    },
  },
};

/**
 * Stripe Payment Form Component
 * Handles card input and payment processing
 */
const PaymentForm = ({ selectedPlan, onSuccess, onError }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentError, setPaymentError] = useState(null);

  // Mutation for creating subscription
  const { mutate: createSubscription, isPending } = useMutation('createSubscription', {
    onSuccess: (data) => {
      console.log('Subscription created successfully:', data);
      setIsProcessing(false);
      onSuccess?.(data);
    },
    onError: (error) => {
      console.error('Subscription creation failed:', error);
      setIsProcessing(false);
      setPaymentError(error.message || 'Payment failed. Please try again.');
      onError?.(error);
    },
  });

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || !elements) {
      console.error('Stripe not loaded yet');
      return;
    }

    if (!selectedPlan) {
      setPaymentError('Please select a subscription plan');
      return;
    }

    setIsProcessing(true);
    setPaymentError(null);

    const cardElement = elements.getElement(CardNumberElement);

    try {
      // Create payment method
      const { error, paymentMethod } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
      });

      if (error) {
        console.error('Payment method creation failed:', error);
        setPaymentError(error.message);
        setIsProcessing(false);
        return;
      }

      console.log('Payment method created:', paymentMethod);

      // Create subscription with payment method
      createSubscription({
        priceId: selectedPlan.priceId,
        paymentMethodId: paymentMethod.id,
        planId: selectedPlan.id,
      });

    } catch (error) {
      console.error('Payment processing error:', error);
      setPaymentError('An unexpected error occurred. Please try again.');
      setIsProcessing(false);
    }
  };

  const isLoading = isProcessing || isPending || !stripe;

  return (
    <Card title="Payment Information" style={{ maxWidth: 500, margin: '0 auto' }}>
      <Form onFinish={handleSubmit} layout="vertical">
        {paymentError && (
          <Alert
            message="Payment Error"
            description={paymentError}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {selectedPlan && (
          <Alert
            message={`Selected Plan: ${selectedPlan.name}`}
            description={`Price: ${selectedPlan.formattedPrice}/${selectedPlan.period}`}
            type="info"
            style={{ marginBottom: 16 }}
          />
        )}

        <Form.Item label="Card Number" required>
          <div style={{ padding: '8px', border: '1px solid #d9d9d9', borderRadius: '6px' }}>
            <CardNumberElement options={elementStyles} />
          </div>
        </Form.Item>

        <div style={{ display: 'flex', gap: '16px' }}>
          <Form.Item label="Expiry Date" required style={{ flex: 1 }}>
            <div style={{ padding: '8px', border: '1px solid #d9d9d9', borderRadius: '6px' }}>
              <CardExpiryElement options={elementStyles} />
            </div>
          </Form.Item>

          <Form.Item label="CVC" required style={{ flex: 1 }}>
            <div style={{ padding: '8px', border: '1px solid #d9d9d9', borderRadius: '6px' }}>
              <CardCvcElement options={elementStyles} />
            </div>
          </Form.Item>
        </div>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={isLoading}
            disabled={isLoading || !selectedPlan}
            size="large"
            style={{ width: '100%' }}
          >
            {isLoading ? (
              <>
                <Spin size="small" style={{ marginRight: 8 }} />
                Processing Payment...
              </>
            ) : (
              `Pay ${selectedPlan?.formattedPrice || '$0.00'}`
            )}
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

/**
 * Stripe Payment Wrapper with Elements Provider
 */
const StripePaymentForm = ({ selectedPlan, onSuccess, onError }) => {
  return (
    <Elements stripe={stripePromise}>
      <PaymentForm
        selectedPlan={selectedPlan}
        onSuccess={onSuccess}
        onError={onError}
      />
    </Elements>
  );
};

export default StripePaymentForm;
