import ContentBlock from "../../components/partial/contentblock/contentblock";
import InnerLayout from "../../components/shared/layout/innerlayout";

const Terms = () => {
  const termsData = [
    "What is a privacy policy?",
    "A privacy policy is a legal document where you disclose what data you collect from users...",
    "Is the privacy policy generator free to use?",
    "The Privacy Policy Generator (privacypolicygenerator.info) is a free generator of privacy policies for websites, apps & Facebook pages/app. You can use our free generator to create the privacy policy for your business.",
    "Why is a privacy policy important?",
    "The most important thing to remember is that a privacy policy is required by law if you collect data from users, either directly or indirectly. For example, if you have a contact form on your website you need a privacy policy. But you will also need a privacy policy if you use analytics tools such as Google Analytics.",
    "Where do I put my privacy policy?",
    "Usually, you can find privacy policies in the footer of a website. We recommend that you place your privacy policy in easy to find locations on your website.",
  ];

  return (
  <InnerLayout>
      <div className="container-fluid mt-4">
      <ContentBlock title="Terms And Conditions" content={termsData} />
    </div>
  </InnerLayout>
  );
};

export default Terms;
