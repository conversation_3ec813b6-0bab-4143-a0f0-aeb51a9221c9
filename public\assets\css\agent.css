.agent-box {
    border: 1px solid #F0F0F0;
}

.agent-header {
    height: 300px;
    overflow: hidden;
}

.agent-header img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.agent-select .ant-form-item {
    margin-top: 0;
    width: 150px;
}

.agent-body {
    padding: 0 30px 20px;
    background: #ffff;
    border: 1px solid #fafafa;
}



.agent-profile {
    width: 110px;
    height: 110px;
    overflow: hidden;
    margin-top: -18px;
}

.agent-profile img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.active-tab-button {
    border-bottom-width: 0;
    padding: 33px 50px;
    width: fit-content;
    border-radius: 12px;
    color: #1890FF;
    font-size: 18px;
    font-weight: 600;
    border: 1px solid #ECECEC;
    border-bottom: 1px solid transparent !important;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    box-shadow: none !important;

}
.active-tab-button::before,
.post-tab-button::before{
    content: none !important;
}
.post-tab-button:hover{
    border-radius: 12px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.active-tab-button:hover span,
.post-tab-button:hover span{
    color: #000 !important;
}
.post-tab-button {
    background-color: #F3F3F3;
    border-width: 0;
    border-style: solid;
    border-color: transparent;
    border-bottom-width: 0;
    padding: 33px 50px;
    width: fit-content;
    border-radius: 0;
    color: #C0C0C0;
    font-size: 18px;
    font-weight: 500;
}


.agent-item .agent-profile {
    width: 90px;
    height: 90px;
    margin-top: 0;
}

.agent-item {
    background: #fafafa;
    padding: 10px;
    border-radius: 12px;
    box-shadow: -1px 2px 37px #00000036;
    margin-bottom: 20px;
    cursor: pointer;
}

.profile-edit {
    border: 1px solid rgba(240, 240, 240, 1);
    padding: 20px 20px 60px 20px;
    background-color: #fff;
    border-radius: 12px;
    border-radius: 12px;
}

.profile-edit .agent-profile {
    width: 110px;
    height: 110px;
    overflow: hidden;
    margin: 0 auto;
}

.personal-border {
    border: 1px solid rgba(240, 240, 240, 1);
   
    border-radius: 12px;
    margin-bottom: 30px;
    background-color: #fff;
}