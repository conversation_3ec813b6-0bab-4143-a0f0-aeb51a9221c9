import { Link, useParams } from "react-router-dom";
import FlatButton from "../../shared/button/flatbutton";
import { useQuery, useMutation } from "../../../hooks/reactQuery";
import apiClient from "../../../services/apiClient";
import { useState } from "react";
import PaymentForm from "./paymentform";
import { Modal, Skeleton, notification } from "antd";
import useSweetAlert from "../../../hooks/useSweetAlert";
import { useLocation, useNavigate } from "react-router-dom";
const SubscriptionScreen = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isPaymentModalVisible, setIsPaymentModalVisible] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [clientSecret, setClientSecret] = useState(null);
  const [isCreatingSubscription, setIsCreatingSubscription] = useState(false);
  const { token } = useParams(); // Get token from URL
  const { showAlert } = useSweetAlert();

  // Get user data to check for subscriptions

  const {
    data: userData,
    isLoading: userLoading,
    refetch: refetchUser,
  } = useQuery("getProfile", {
    token,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });

  // Cancel subscription mutation
  const { mutate: cancelSubscription, isPending: isCancelling } = useMutation(
    "cancelSubscription",
    {
      // Only pass token if it exists (for token-based access), otherwise use regular auth
      ...(token && { token }),
      onSuccess: async (data) => {
        // Refresh user data and update window.user
        const updatedUserData = await refetchUser();

        if (updatedUserData?.data?.data) {
          window.user = updatedUserData.data.data;
        }
      },
    }
  );
  const handleSubscribeClick = async (plan) => {
    setSelectedPlan(plan);
    setIsPaymentModalVisible(true);
    setIsCreatingSubscription(true);
    setClientSecret(null);

    try {
      // Hit createSubscription API when subscribe button is clicked
      const priceId =
        plan.priceId || plan.price_id || plan.stripe_price_id || plan.id;

      if (!priceId) {
        throw new Error("Price ID not found in selected plan");
      }

      const response = await apiClient.request("createSubscription", {
        data: {
          price_id: priceId,
        },
        token,
        useFormData: false,
      });

      const { clientSecret: secret } = response.data;
      setClientSecret(secret);
    } catch (error) {
      // notification.error({
      //   description: error.message,
      //   duration: 7,
      // });
      // Keep modal open but show error
    } finally {
      setIsCreatingSubscription(false);
    }
  };

  const handlePaymentSuccess = async (paymentData) => {
    // Close modal and reset state
    setIsPaymentModalVisible(false);
    setSelectedPlan(null);
    setClientSecret(null);

    setTimeout(async () => {
      window.location.reload();
    }, 3000);
  };

  const handlePaymentCancel = () => {
    setIsPaymentModalVisible(false);
    setSelectedPlan(null);
    setClientSecret(null);
  };

  const handleCancelSubscription = async () => {
    const result = await showAlert({
      title: "Cancel Subscription",
      text: "Are you sure you want to cancel your subscription? This action cannot be undone.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, Cancel",
      cancelButtonText: "No, Keep",
    });

    if (result.isConfirmed) {
      cancelSubscription();
    }
  };

  return (
    <div className="subscription-container">
      <Header />
      <PlanOptions
        onSubscribeClick={handleSubscribeClick}
        token={token}
        userData={userData}
        userLoading={userLoading}
        onCancelSubscription={handleCancelSubscription}
        isCancelling={isCancelling}
      />
      <SubscriptionOffer />

      <Modal
        title="Complete Your Subscription"
        open={isPaymentModalVisible}
        onCancel={handlePaymentCancel}
        footer={null}
        centered
      >
        <PaymentForm
          selectedPlan={selectedPlan}
          onSuccess={handlePaymentSuccess}
          onCancel={handlePaymentCancel}
          token={token}
          clientSecret={clientSecret}
          isCreatingSubscription={isCreatingSubscription}
        />
      </Modal>
    </div>
  );
};

const Header = () => {
  return (
    <div className="header">
      {/* <Link to="/subscription/history" className="color-blue text-end">
        Subscription History
      </Link> */}
      <div className="logo mt-3 mb-3">
        <img src="/assets/img/suscribtion-img.png" alt="" />
      </div>
      <h1>Get Sphere Premium Free for 2 Months.</h1>
      {/* <p>Choose your plan after the free trial</p> */}
    </div>
  );
};

const PlanOptions = ({
  onSubscribeClick,
  token,
  userData,
  userLoading,
  onCancelSubscription,
  isCancelling,
}) => {
  const {
    data: subscriptionProducts,
    isLoading,
    isError,
  } = useQuery("getSubscriptionProduct", {
    showSuccessNotification: false,
    token,
  });

  // Check if user has subscriptions
  const hasSubscriptions = userData?.data?.isSubscribed;
  if (isLoading || userLoading) {
    return (
      <div className="plan-options">
        <div className="plans-container">
          {[...Array(1)].map((_, index) => (
            <div className="plan-card" key={index}>
              <Skeleton active paragraph={{ rows: 2 }} />
              <Skeleton.Button active size="large" className="mt-4" block />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    return <div className="text-center text-danger">Failed to load plans.</div>;
  }

  // If user has subscriptions, show current subscription with cancel button or cancellation message
  if (hasSubscriptions) {
    const subscription = userData.data.subscriptions; // Get first subscription
    const isScheduledForCancellation = subscription.cancelAtPeriodEnd === true;

    return (
      <div className="plan-options">
        <div className="plans-container">
          <div className="plan-card active-subscription">
            <div className="subscription-status">
              <span
                className={`status-badge ${
                  isScheduledForCancellation ? "cancelling" : "active"
                }`}
              >
                {isScheduledForCancellation ? "Ending Soon" : "Active"}
              </span>
            </div>
            <h3>
              <strong>{subscription.productName}</strong>
            </h3>
            <p>
              {subscription.description ||
                "You are currently subscribed to our premium plan."}
            </p>

            <p className="price">
              ${subscription.amount || subscription.price || "5.99"}{" "}
              {(subscription.currency || "USD").toUpperCase()}
            </p>

            {isScheduledForCancellation && subscription.endAt ? (
              <div className="cancellation-notice mt-3">
                <p className="cancellation-message">
                  <strong>
                    Your subscription will end on{" "}
                    {new Date(subscription.endAt).toLocaleDateString()}.
                  </strong>
                </p>
              </div>
            ) : subscription.current_period_end ? (
              <p className="renewal-date">
                Next billing:{" "}
                {new Date(subscription.current_period_end).toLocaleDateString()}
              </p>
            ) : null}

            {!isScheduledForCancellation && (
              <FlatButton
                title={isCancelling ? "Cancelling..." : "Cancel Subscription"}
                className="cancel-btn mt-4"
                onClick={onCancelSubscription}
                loading={isCancelling}
                disabled={isCancelling}
              />
            )}
          </div>
        </div>
      </div>
    );
  }

  // Show available plans if no subscriptions
  return (
    <div className="plan-options">
      <div className="plans-container">
        {subscriptionProducts?.data?.map((plan) => (
          <PlanCard
            key={plan.id}
            plan={plan}
            onSubscribeClick={onSubscribeClick}
          />
        ))}
      </div>
    </div>
  );
};

const PlanCard = ({ plan, onSubscribeClick }) => {
  return (
    <div className="plan-card">
      <h3>
        <strong>{plan.name}</strong>
      </h3>
      <p>{plan.description || "No description available."}</p>

      <p className="price">
        ${plan.price} {plan.currency.toUpperCase()}
      </p>

      <FlatButton
        title="Subscribe"
        className="signin-btn mt-4"
        onClick={() => onSubscribeClick(plan)}
      />
    </div>
  );
};

const SubscriptionOffer = () => {
  return (
    <div className="subscription-offer">
      <p>
        <strong>2 months for free, then $4.99 a month</strong>
      </p>
    </div>
  );
};

export default SubscriptionScreen;
