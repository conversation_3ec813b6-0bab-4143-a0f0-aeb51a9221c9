import{u,j as s}from"./index-3mE9H3a0.js";import{I as w}from"./index-C-I6oq0X.js";import{B as o,c as l,v as r}from"./index-Cd-Wothc.js";import{F as f}from"./flatbutton-Yo0mdDJ8.js";import{u as h}from"./useMutation-lvneVzbk.js";import{F as d}from"./react-stripe.esm-CaXK0k-R.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";import"./index-BUUUOhAK.js";const q=()=>{const c=u(),[t]=d.useForm(),m=e=>{p(e)},{mutate:p,isPending:a}=h("changePassword",{useFormData:!1,onSuccess:async e=>{e&&(c("/setting"),t.resetFields())}});return s.jsx(w,{showSidebar:!1,pageType:"signup",children:s.jsx(d,{name:"login",layout:"vertical",onFinish:m,form:t,initialValues:{remember:!0},autoComplete:"off",children:s.jsxs("div",{className:"container-fluid",children:[s.jsxs("div",{className:"row mt-5 justify-content-center",children:[s.jsx("div",{className:"col-12 col-md-12 col-lg-7",children:s.jsx("h2",{className:"mb-3",children:"Chanage Password"})}),s.jsx("div",{className:"col-12 col-md-8 col-lg-7",children:s.jsx(o,{name:"current_password",placeholder:"***",label:"Current Password",type:"password",rules:l("current-password",r.required,r.password)})}),s.jsx("div",{className:"col-12 col-md-8 col-lg-7",children:s.jsx(o,{name:"password",placeholder:"***",label:"New Password",type:"password",rules:l("new-password",r.required,r.password)})}),s.jsx("div",{className:"col-12 col-md-8 col-lg-7",children:s.jsx(o,{name:"confirm_password",placeholder:"***",label:"Confirm Password",type:"password",rules:[r.required("confirm-password"),({getFieldValue:e})=>({validator:(x,i)=>{const n=e("password");return!i||!n?Promise.resolve():i!==n?Promise.reject(new Error("Confirm Passwords does not match")):Promise.resolve()}})],dependencies:["password"]})})]}),s.jsx("div",{className:"text-center",children:s.jsx(f,{title:a?"Updating...":"Update",className:"mx-auto mt-4 signin-btn signup-btn mt-5",htmlType:"submit",loading:a,disabled:a})})]})})})};export{q as default};
