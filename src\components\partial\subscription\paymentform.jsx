import React, { useState, useEffect } from "react";
import { Form, message, Skeleton, notification } from "antd";
import {
  Elements,
  useStripe,
  useElements,
  PaymentElement,
} from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import FlatButton from "../../shared/button/flatbutton";
import "./paymentform.css";
import { useParams, useLocation } from "react-router-dom";
import constants from "../../../config/constants";

// Initialize Stripe
const stripePromise = loadStripe(constants.stripe_publish_key);

const PaymentFormContent = ({
  onSuccess,
  onCancel,
  token: propToken,
  clientSecret,
}) => {
  const { token: urlToken } = useParams();
  const token = propToken || urlToken;
  const location = useLocation();

  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);

  // Check for payment status on page load following Stripe documentation
  useEffect(() => {
    const checkPaymentStatus = async () => {
      // Retrieve the "payment_intent_client_secret" query parameter appended to
      // your return_url by Stripe.js
      const urlParams = new URLSearchParams(location.search);
      const paymentIntentClientSecret = urlParams.get(
        "payment_intent_client_secret"
      );

      if (paymentIntentClientSecret && stripe) {
        try {
          // Retrieve the PaymentIntent
          const { paymentIntent } = await stripe.retrievePaymentIntent(
            paymentIntentClientSecret
          );

          switch (paymentIntent.status) {
            case "succeeded":
              notification.success({
                message: "Payment Successful",
                description: "Success! Payment received.",
                duration: 5,
              });
              if (onSuccess) {
                onSuccess({ paymentIntent });
              }
              break;

            case "processing":
              notification.info({
                message: "Payment Processing",
                description:
                  "Payment processing. We'll update you when payment is received.",
                duration: 5,
              });
              break;

            case "requires_payment_method":
              notification.error({
                message: "Payment Failed",
                description:
                  "Payment failed. Please try another payment method.",
                duration: 7,
              });

              break;

            default:
              notification.error({
                message: "Payment Error",
                description: "Something went wrong.",
                duration: 7,
              });
              break;
          }
        } catch (error) {
          console.error("Error retrieving payment intent:", error);
          notification.error({
            message: "Error",
            description: "Failed to retrieve payment status.",
            duration: 7,
          });
        }
      }
    };

    // Initialize Stripe.js and check payment status
    if (stripe) {
      checkPaymentStatus();
    }
  }, [stripe, location.search, onSuccess]);

  const [form] = Form.useForm();

  const handleSubmit = async (values) => {
    if (!stripe || !elements || !clientSecret) {
      notification.error({
        message: "Payment System Error",
        description:
          "Stripe has not loaded yet. Please refresh the page and try again.",
        duration: 7,
      });
      return;
    }

    setIsProcessing(true);

    try {
      // Use confirmPayment without redirect - handle status directly
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        redirect: "if_required", // Only redirect if absolutely necessary (like 3D Secure)
      });

      if (error) {
        // Handle immediate errors (card declined, validation errors, etc.)
        notification.error({
          message: "Payment Error",
          description: error.message,
          duration: 7,
        });
        setIsProcessing(false);
      } else if (paymentIntent) {
        // Payment completed - handle status directly without redirect

        handlePaymentStatus(paymentIntent);
        setIsProcessing(false);
      }
    } catch (err) {
      setIsProcessing(false);
      notification.error({
        message: "Unexpected Error",
        description:
          "An unexpected error occurred during payment processing. Please try again.",
        duration: 7,
      });
      console.error("Payment error:", err);
    }
  };

  // Handle payment status directly
  const handlePaymentStatus = (paymentIntent) => {
    switch (paymentIntent.status) {
      case "succeeded":
        notification.success({
          message: "Payment Successful",
          description: "Success! Payment received.",
          duration: 5,
        });
        if (onSuccess) {
          onSuccess({ paymentIntent });
        }
        break;

      case "processing":
        notification.info({
          message: "Payment Processing",
          description:
            "Payment processing. We'll update you when payment is received.",
          duration: 5,
        });
        break;

      case "requires_payment_method":
        notification.error({
          message: "Payment Failed",
          description: "Payment failed. Please try another payment method.",
          duration: 7,
        });
        break;

      case "requires_action":
        notification.info({
          message: "Additional Authentication Required",
          description: "Please complete the additional authentication step.",
          duration: 5,
        });
        break;

      default:
        notification.error({
          message: "Payment Error",
          description: "Something went wrong.",
          duration: 7,
        });
        break;
    }
  };

  return (
    <div className="payment-form-container">
      <Form form={form} onFinish={handleSubmit} className="payment-form">
        {/* Use PaymentElement like index.html */}
        <div className="payment-element-container">
          <PaymentElement
            options={{
              layout: "tabs",
            }}
          />
        </div>

        <div className="form-actions">
          <div className="d-flex align-items-center">
            <FlatButton
              title="Cancel"
              className="cancel-btn"
              onClick={onCancel}
              type="button"
            />

            <FlatButton
              title={isProcessing ? "Processing..." : "Subscribe"}
              className="payment-btn ms-3"
              htmlType="submit"
              loading={isProcessing}
              disabled={!stripe || isProcessing}
            />
          </div>
        </div>
      </Form>
    </div>
  );
};

const PaymentForm = ({
  selectedPlan,
  onSuccess,
  onCancel,
  token,
  clientSecret,
  isCreatingSubscription,
}) => {
  // Show loading while createSubscription API is being called
  if (isCreatingSubscription) {
    return (
      <div className="payment-form-container">
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    );
  }

  // Show error if no clientSecret
  if (!clientSecret) {
    return (
      <div className="payment-form-container">
        <div className="text-center">
          <p>Failed to initialize payment. Please try again.</p>
          <FlatButton
            title="Cancel"
            className="cancel-btn"
            onClick={onCancel}
          />
        </div>
      </div>
    );
  }

  // Show payment form when clientSecret is available
  const options = {
    clientSecret: clientSecret,
    appearance: {
      theme: "stripe",
    },
  };

  return (
    <Elements stripe={stripePromise} options={options}>
      <PaymentFormContent
        onSuccess={onSuccess}
        onCancel={onCancel}
        token={token}
        clientSecret={clientSecret}
      />
    </Elements>
  );
};

export default PaymentForm;
