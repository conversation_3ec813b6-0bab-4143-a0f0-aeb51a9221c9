import{u as l,c as o,r as n,j as s}from"./index-3mE9H3a0.js";import{I as c}from"./index-C-I6oq0X.js";import"./index-Cd-Wothc.js";import"./react-stripe.esm-CaXK0k-R.js";import{F as a}from"./flatbutton-Yo0mdDJ8.js";import{A as m}from"./agentitems-pVIvr7TQ.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";import"./index-BUUUOhAK.js";import"./sparepost-tyxuku36.js";import"./interactionbox-B9SrINfg.js";import"./index-kHmX87X2.js";import"./index-KeJPQTWG.js";import"./index-DX-6A722.js";import"./fade-yKzH711D.js";import"./Skeleton-BqfVCYaM.js";import"./propertycard-DcHEKJZy.js";import"./DeleteOutlined-DCDN0YWx.js";const L=()=>{const i=l(),{type:e}=o();n.useEffect(()=>{e||i("/profile/posts",{replace:!0})},[e,i]);const r=t=>{i(`/profile/${t}`)};return s.jsx(c,{children:s.jsx("div",{className:"container-fluid",children:s.jsxs("div",{className:"row",children:[s.jsx("div",{className:"col-12",children:s.jsxs("div",{className:"agent-box mt-5",children:[s.jsx("div",{className:"agent-header",children:s.jsx("img",{src:"/assets/img/home-img.png",alt:""})}),s.jsxs("div",{className:"agent-body d-flex align-items-center justify-content-between",children:[s.jsxs("div",{className:"d-flex align-items-center",children:[s.jsx("div",{className:"agent-profile",children:s.jsx("img",{src:window.user.image_url,alt:window.user.name})}),s.jsxs("div",{className:"ms-3",children:[s.jsxs("div",{className:"d-flex",children:[s.jsx("p",{className:"me-2 font-600",children:window.user.name}),s.jsx("img",{src:"/assets/img/badge.png",alt:"",className:"img-fluid"})]}),s.jsx("p",{className:"color-light",children:window.user.professional_types&&Array.isArray(window.user.professional_types)?window.user.professional_types.map(t=>window.helper.getLabel("professional_type",t.name)).join(", "):window.helper.getLabel("professional_type",window.user.professional_type)}),s.jsxs("div",{className:"d-flex",children:[s.jsx("div",{children:s.jsx("img",{src:"/assets/img/location_on.png",alt:""})}),s.jsx("div",{children:s.jsxs("p",{children:[" ",window.user.state]})})]})]})]}),s.jsx("div",{className:"d-flex align-items-center",children:s.jsx("div",{className:"me-3",children:s.jsx(a,{title:"Edit your Profile",className:"blue-btn",onClick:()=>i("/editprofile")})})})]})]})}),s.jsx("div",{className:"col-12 mt-5 ",children:s.jsxs("div",{className:"text-center",children:[s.jsx(a,{title:"Posts",className:e==="posts"?"active-tab-button":"post-tab-button",onClick:()=>r("posts")}),s.jsx(a,{title:"Listings",className:e==="listing"?"active-tab-button":"post-tab-button",onClick:()=>r("listing")})]})}),s.jsx(m,{type:e})]})})})};export{L as default};
