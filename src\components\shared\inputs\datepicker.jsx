import React from "react";
import { DatePicker, Form } from "antd";

const DatePickerInput = ({
  onChange,
  placeholder,
  label,
  name,
  rules,
  defaultValue,
  picker,
  ...props
}) => {
  return (
    <Form.Item
      name={name}
      rules={rules}
      label={label}
      validateTrigger="onBlur"
      defaultValue={defaultValue}
    >
      <DatePicker
        placeholder={placeholder}
        onChange={onChange}
        picker={picker}
        style={{ width: "100%" }}
        {...props}
      />
    </Form.Item>
  );
};

export default DatePickerInput;
