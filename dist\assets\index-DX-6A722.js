import{r as o,R as f,ax as be,k as m,g as x,i as ee,p as re,e as M,f as Q,G as le,ay as ae,K as te,l as Ce,o as r,m as ye,n as he,s as Se,M as $e}from"./index-3mE9H3a0.js";import{d as pe,P as xe,c as we}from"./index-B2p2olBm.js";import{g as Re}from"./react-stripe.esm-CaXK0k-R.js";import{i as Ne}from"./fade-yKzH711D.js";var de=o.createContext({});function oe(e,a,t){var n=a;return!n&&t&&(n="".concat(e,"-").concat(t)),n}function ne(e,a){var t=e["page".concat(a?"Y":"X","Offset")],n="scroll".concat(a?"Top":"Left");if(typeof t!="number"){var i=e.document;t=i.documentElement[n],typeof t!="number"&&(t=i.body[n])}return t}function Ee(e){var a=e.getBoundingClientRect(),t={left:a.left,top:a.top},n=e.ownerDocument,i=n.defaultView||n.parentWindow;return t.left+=ne(i),t.top+=ne(i,!0),t}const Be=o.memo(function(e){var a=e.children;return a},function(e,a){var t=a.shouldUpdate;return!t});var Me={width:0,height:0,overflow:"hidden",outline:"none"},Ie={outline:"none"},Te=f.forwardRef(function(e,a){var t=e.prefixCls,n=e.className,i=e.style,g=e.title,c=e.ariaId,v=e.footer,l=e.closable,b=e.closeIcon,h=e.onClose,y=e.children,C=e.bodyStyle,w=e.bodyProps,$=e.modalRender,R=e.onMouseDown,N=e.onMouseUp,O=e.holderRef,H=e.visible,I=e.forceRender,S=e.width,T=e.height,d=e.classNames,u=e.styles,q=f.useContext(de),G=q.panel,U=be(O,G),P=o.useRef(),A=o.useRef();f.useImperativeHandle(a,function(){return{focus:function(){var p;(p=P.current)===null||p===void 0||p.focus({preventScroll:!0})},changeActive:function(p){var X=document,V=X.activeElement;p&&V===A.current?P.current.focus({preventScroll:!0}):!p&&V===P.current&&A.current.focus({preventScroll:!0})}}});var L={};S!==void 0&&(L.width=S),T!==void 0&&(L.height=T);var E=v?f.createElement("div",{className:x("".concat(t,"-footer"),d==null?void 0:d.footer),style:m({},u==null?void 0:u.footer)},v):null,B=g?f.createElement("div",{className:x("".concat(t,"-header"),d==null?void 0:d.header),style:m({},u==null?void 0:u.header)},f.createElement("div",{className:"".concat(t,"-title"),id:c},g)):null,z=o.useMemo(function(){return ee(l)==="object"&&l!==null?l:l?{closeIcon:b??f.createElement("span",{className:"".concat(t,"-close-x")})}:{}},[l,b,t]),D=re(z,!0),W=ee(l)==="object"&&l.disabled,K=l?f.createElement("button",M({type:"button",onClick:h,"aria-label":"Close"},D,{className:"".concat(t,"-close"),disabled:W}),z.closeIcon):null,_=f.createElement("div",{className:x("".concat(t,"-content"),d==null?void 0:d.content),style:u==null?void 0:u.content},K,B,f.createElement("div",M({className:x("".concat(t,"-body"),d==null?void 0:d.body),style:m(m({},C),u==null?void 0:u.body)},w),y),E);return f.createElement("div",{key:"dialog-element",role:"dialog","aria-labelledby":g?c:null,"aria-modal":"true",ref:U,style:m(m({},i),L),className:x(t,n),onMouseDown:R,onMouseUp:N},f.createElement("div",{ref:P,tabIndex:0,style:Ie},f.createElement(Be,{shouldUpdate:H||I},$?$(_):_)),f.createElement("div",{tabIndex:0,ref:A,style:Me}))}),se=o.forwardRef(function(e,a){var t=e.prefixCls,n=e.title,i=e.style,g=e.className,c=e.visible,v=e.forceRender,l=e.destroyOnClose,b=e.motionName,h=e.ariaId,y=e.onVisibleChanged,C=e.mousePosition,w=o.useRef(),$=o.useState(),R=Q($,2),N=R[0],O=R[1],H={};N&&(H.transformOrigin=N);function I(){var S=Ee(w.current);O(C&&(C.x||C.y)?"".concat(C.x-S.left,"px ").concat(C.y-S.top,"px"):"")}return o.createElement(le,{visible:c,onVisibleChanged:y,onAppearPrepare:I,onEnterPrepare:I,forceRender:v,motionName:b,removeOnLeave:l,ref:w},function(S,T){var d=S.className,u=S.style;return o.createElement(Te,M({},e,{ref:a,title:n,ariaId:h,prefixCls:t,holderRef:T,style:m(m(m({},u),i),H),className:x(g,d)}))})});se.displayName="Content";var ze=function(a){var t=a.prefixCls,n=a.style,i=a.visible,g=a.maskProps,c=a.motionName,v=a.className;return o.createElement(le,{key:"mask",visible:i,motionName:c,leavedClassName:"".concat(t,"-mask-hidden")},function(l,b){var h=l.className,y=l.style;return o.createElement("div",M({ref:b,style:m(m({},y),n),className:x("".concat(t,"-mask"),h,v)},g))})},He=function(a){var t=a.prefixCls,n=t===void 0?"rc-dialog":t,i=a.zIndex,g=a.visible,c=g===void 0?!1:g,v=a.keyboard,l=v===void 0?!0:v,b=a.focusTriggerAfterClose,h=b===void 0?!0:b,y=a.wrapStyle,C=a.wrapClassName,w=a.wrapProps,$=a.onClose,R=a.afterOpenChange,N=a.afterClose,O=a.transitionName,H=a.animation,I=a.closable,S=I===void 0?!0:I,T=a.mask,d=T===void 0?!0:T,u=a.maskTransitionName,q=a.maskAnimation,G=a.maskClosable,U=G===void 0?!0:G,P=a.maskStyle,A=a.maskProps,L=a.rootClassName,E=a.classNames,B=a.styles,z=o.useRef(),D=o.useRef(),W=o.useRef(),K=o.useState(c),_=Q(K,2),j=_[0],p=_[1],X=pe();function V(){ae(D.current,document.activeElement)||(z.current=document.activeElement)}function ce(){if(!ae(D.current,document.activeElement)){var s;(s=W.current)===null||s===void 0||s.focus()}}function ue(s){if(s)ce();else{if(p(!1),d&&z.current&&h){try{z.current.focus({preventScroll:!0})}catch{}z.current=null}j&&(N==null||N())}R==null||R(s)}function Y(s){$==null||$(s)}var F=o.useRef(!1),Z=o.useRef(),me=function(){clearTimeout(Z.current),F.current=!0},fe=function(){Z.current=setTimeout(function(){F.current=!1})},k=null;U&&(k=function(J){F.current?F.current=!1:D.current===J.target&&Y(J)});function ge(s){if(l&&s.keyCode===te.ESC){s.stopPropagation(),Y(s);return}c&&s.keyCode===te.TAB&&W.current.changeActive(!s.shiftKey)}o.useEffect(function(){c&&(p(!0),V())},[c]),o.useEffect(function(){return function(){clearTimeout(Z.current)}},[]);var ve=m(m(m({zIndex:i},y),B==null?void 0:B.wrapper),{},{display:j?null:"none"});return o.createElement("div",M({className:x("".concat(n,"-root"),L)},re(a,{data:!0})),o.createElement(ze,{prefixCls:n,visible:d&&c,motionName:oe(n,u,q),style:m(m({zIndex:i},P),B==null?void 0:B.mask),maskProps:A,className:E==null?void 0:E.mask}),o.createElement("div",M({tabIndex:-1,onKeyDown:ge,className:x("".concat(n,"-wrap"),C,E==null?void 0:E.wrapper),ref:D,onClick:k,style:ve},w),o.createElement(se,M({},a,{onMouseDown:me,onMouseUp:fe,ref:W,closable:S,ariaId:X,prefixCls:n,visible:c&&j,onClose:Y,onVisibleChanged:ue,motionName:oe(n,O,H)}))))},Pe=function(a){var t=a.visible,n=a.getContainer,i=a.forceRender,g=a.destroyOnClose,c=g===void 0?!1:g,v=a.afterClose,l=a.panelRef,b=o.useState(t),h=Q(b,2),y=h[0],C=h[1],w=o.useMemo(function(){return{panel:l}},[l]);return o.useEffect(function(){t&&C(!0)},[t]),!i&&c&&!y?null:o.createElement(de.Provider,{value:w},o.createElement(xe,{open:t||i||y,autoDestroy:!1,getContainer:n,autoLock:t||y},o.createElement(He,M({},a,{destroyOnClose:c,afterClose:function(){v==null||v(),C(!1)}}))))};Pe.displayName="Dialog";function ie(e){return{position:e,inset:0}}const De=e=>{const{componentCls:a,antCls:t}=e;return[{[`${a}-root`]:{[`${a}${t}-zoom-enter, ${a}${t}-zoom-appear`]:{transform:"none",opacity:0,animationDuration:e.motionDurationSlow,userSelect:"none"},[`${a}${t}-zoom-leave ${a}-content`]:{pointerEvents:"none"},[`${a}-mask`]:Object.assign(Object.assign({},ie("fixed")),{zIndex:e.zIndexPopupBase,height:"100%",backgroundColor:e.colorBgMask,pointerEvents:"none",[`${a}-hidden`]:{display:"none"}}),[`${a}-wrap`]:Object.assign(Object.assign({},ie("fixed")),{zIndex:e.zIndexPopupBase,overflow:"auto",outline:0,WebkitOverflowScrolling:"touch"})}},{[`${a}-root`]:Ne(e)}]},Oe=e=>{const{componentCls:a}=e;return[{[`${a}-root`]:{[`${a}-wrap-rtl`]:{direction:"rtl"},[`${a}-centered`]:{textAlign:"center","&::before":{display:"inline-block",width:0,height:"100%",verticalAlign:"middle",content:'""'},[a]:{top:0,display:"inline-block",paddingBottom:0,textAlign:"start",verticalAlign:"middle"}},[`@media (max-width: ${e.screenSMMax}px)`]:{[a]:{maxWidth:"calc(100vw - 16px)",margin:`${r(e.marginXS)} auto`},[`${a}-centered`]:{[a]:{flex:1}}}}},{[a]:Object.assign(Object.assign({},he(e)),{pointerEvents:"none",position:"relative",top:100,width:"auto",maxWidth:`calc(100vw - ${r(e.calc(e.margin).mul(2).equal())})`,margin:"0 auto",paddingBottom:e.paddingLG,[`${a}-title`]:{margin:0,color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.titleFontSize,lineHeight:e.titleLineHeight,wordWrap:"break-word"},[`${a}-content`]:{position:"relative",backgroundColor:e.contentBg,backgroundClip:"padding-box",border:0,borderRadius:e.borderRadiusLG,boxShadow:e.boxShadow,pointerEvents:"auto",padding:e.contentPadding},[`${a}-close`]:Object.assign({position:"absolute",top:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),insetInlineEnd:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),zIndex:e.calc(e.zIndexPopupBase).add(10).equal(),padding:0,color:e.modalCloseIconColor,fontWeight:e.fontWeightStrong,lineHeight:1,textDecoration:"none",background:"transparent",borderRadius:e.borderRadiusSM,width:e.modalCloseBtnSize,height:e.modalCloseBtnSize,border:0,outline:0,cursor:"pointer",transition:`color ${e.motionDurationMid}, background-color ${e.motionDurationMid}`,"&-x":{display:"flex",fontSize:e.fontSizeLG,fontStyle:"normal",lineHeight:r(e.modalCloseBtnSize),justifyContent:"center",textTransform:"none",textRendering:"auto"},"&:disabled":{pointerEvents:"none"},"&:hover":{color:e.modalCloseIconHoverColor,backgroundColor:e.colorBgTextHover,textDecoration:"none"},"&:active":{backgroundColor:e.colorBgTextActive}},Se(e)),[`${a}-header`]:{color:e.colorText,background:e.headerBg,borderRadius:`${r(e.borderRadiusLG)} ${r(e.borderRadiusLG)} 0 0`,marginBottom:e.headerMarginBottom,padding:e.headerPadding,borderBottom:e.headerBorderBottom},[`${a}-body`]:{fontSize:e.fontSize,lineHeight:e.lineHeight,wordWrap:"break-word",padding:e.bodyPadding,[`${a}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",margin:`${r(e.margin)} auto`}},[`${a}-footer`]:{textAlign:"end",background:e.footerBg,marginTop:e.footerMarginTop,padding:e.footerPadding,borderTop:e.footerBorderTop,borderRadius:e.footerBorderRadius,[`> ${e.antCls}-btn + ${e.antCls}-btn`]:{marginInlineStart:e.marginXS}},[`${a}-open`]:{overflow:"hidden"}})},{[`${a}-pure-panel`]:{top:"auto",padding:0,display:"flex",flexDirection:"column",[`${a}-content,
          ${a}-body,
          ${a}-confirm-body-wrapper`]:{display:"flex",flexDirection:"column",flex:"auto"},[`${a}-confirm-body`]:{marginBottom:"auto"}}}]},Ae=e=>{const{componentCls:a}=e;return{[`${a}-root`]:{[`${a}-wrap-rtl`]:{direction:"rtl",[`${a}-confirm-body`]:{direction:"rtl"}}}}},Le=e=>{const{componentCls:a}=e,t=Re(e);delete t.xs;const n=Object.keys(t).map(i=>({[`@media (min-width: ${r(t[i])})`]:{width:`var(--${a.replace(".","")}-${i}-width)`}}));return{[`${a}-root`]:{[a]:[{width:`var(--${a.replace(".","")}-xs-width)`}].concat($e(n))}}},We=e=>{const a=e.padding,t=e.fontSizeHeading5,n=e.lineHeightHeading5;return ye(e,{modalHeaderHeight:e.calc(e.calc(n).mul(t).equal()).add(e.calc(a).mul(2).equal()).equal(),modalFooterBorderColorSplit:e.colorSplit,modalFooterBorderStyle:e.lineType,modalFooterBorderWidth:e.lineWidth,modalCloseIconColor:e.colorIcon,modalCloseIconHoverColor:e.colorIconHover,modalCloseBtnSize:e.controlHeight,modalConfirmIconSize:e.fontHeight,modalTitleHeight:e.calc(e.titleFontSize).mul(e.titleLineHeight).equal()})},_e=e=>({footerBg:"transparent",headerBg:e.colorBgElevated,titleLineHeight:e.lineHeightHeading5,titleFontSize:e.fontSizeHeading5,contentBg:e.colorBgElevated,titleColor:e.colorTextHeading,contentPadding:e.wireframe?0:`${r(e.paddingMD)} ${r(e.paddingContentHorizontalLG)}`,headerPadding:e.wireframe?`${r(e.padding)} ${r(e.paddingLG)}`:0,headerBorderBottom:e.wireframe?`${r(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",headerMarginBottom:e.wireframe?0:e.marginXS,bodyPadding:e.wireframe?e.paddingLG:0,footerPadding:e.wireframe?`${r(e.paddingXS)} ${r(e.padding)}`:0,footerBorderTop:e.wireframe?`${r(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",footerBorderRadius:e.wireframe?`0 0 ${r(e.borderRadiusLG)} ${r(e.borderRadiusLG)}`:0,footerMarginTop:e.wireframe?0:e.marginSM,confirmBodyPadding:e.wireframe?`${r(e.padding*2)} ${r(e.padding*2)} ${r(e.paddingLG)}`:0,confirmIconMarginInlineEnd:e.wireframe?e.margin:e.marginSM,confirmBtnsMarginTop:e.wireframe?e.marginLG:e.marginSM}),qe=Ce("Modal",e=>{const a=We(e);return[Oe(a),Ae(a),De(a),we(a,"zoom"),Le(a)]},_e,{unitless:{titleLineHeight:!0}});export{Pe as D,Te as P,_e as a,De as g,We as p,qe as u};
