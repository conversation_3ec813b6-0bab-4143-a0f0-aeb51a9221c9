.image-slider {
  width: 100%;
  height: 100%;
}

/* Main Swiper Styles */
.image-slider .main-swiper {
  width: 100%;
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 15px;
}

.image-slider .main-image {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
}

.image-slider .main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Thumbnail Swiper Styles */
.image-slider .thumbs-swiper {
  width: 100%;
  height: 80px;
  box-sizing: border-box;
  padding: 5px 0;
}

.image-slider .thumbs-swiper .swiper-slide {
  width: 25%;
  height: 100%;
  opacity: 0.6;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.image-slider .thumbs-swiper .swiper-slide-thumb-active {
  opacity: 1;
}

.image-slider .thumbnail {
  width: 100%;
  height: 70px;
  border-radius: 6px;
  overflow: hidden;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  background-color: #f5f5f5;
}

.image-slider .thumbs-swiper .swiper-slide-thumb-active .thumbnail {
  border-color: #1890ff;
  box-shadow: 0 0 0 1px #1890ff;
}

.image-slider .thumbnail:hover {
  border-color: #1890ff;
  transform: scale(1.05);
}

.image-slider .thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Single Image Layout */
.image-slider.single-image .main-image {
  width: 100%;
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f5f5f5;
}

.image-slider.single-image .main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Swiper Navigation Buttons */
.image-slider .main-swiper .swiper-button-next,
.image-slider .main-swiper .swiper-button-prev {
  color: white;
  background: rgba(0, 0, 0, 0.6);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-top: -20px;
  transition: all 0.3s ease;
}

.image-slider .main-swiper .swiper-button-next:hover,
.image-slider .main-swiper .swiper-button-prev:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

.image-slider .main-swiper .swiper-button-next::after,
.image-slider .main-swiper .swiper-button-prev::after {
  font-size: 16px;
  font-weight: bold;
}

.image-slider .main-swiper .swiper-button-prev {
  left: 15px;
}

.image-slider .main-swiper .swiper-button-next {
  right: 15px;
}

/* Swiper Pagination */
.image-slider .main-swiper .swiper-pagination {
  bottom: 15px;
  right: 15px;
  left: auto;
  width: auto;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.image-slider .main-swiper .swiper-pagination-fraction {
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .image-slider .main-swiper {
    height: 300px;
  }
  
  .image-slider.single-image .main-image {
    height: 300px;
  }
  
  .image-slider .main-swiper .swiper-button-next,
  .image-slider .main-swiper .swiper-button-prev {
    width: 35px;
    height: 35px;
    margin-top: -17.5px;
  }
  
  .image-slider .main-swiper .swiper-button-next::after,
  .image-slider .main-swiper .swiper-button-prev::after {
    font-size: 14px;
  }
  
  .image-slider .main-swiper .swiper-button-prev {
    left: 10px;
  }
  
  .image-slider .main-swiper .swiper-button-next {
    right: 10px;
  }
  
  .image-slider .thumbs-swiper {
    height: 70px;
  }
  
  .image-slider .thumbnail {
    height: 60px;
  }
}

@media (max-width: 480px) {
  .image-slider .main-swiper {
    height: 250px;
    margin-bottom: 10px;
  }
  
  .image-slider.single-image .main-image {
    height: 250px;
  }
  
  .image-slider .thumbs-swiper {
    height: 60px;
  }
  
  .image-slider .thumbnail {
    height: 50px;
  }
  
  .image-slider .main-swiper .swiper-pagination {
    bottom: 10px;
    right: 10px;
    font-size: 11px;
    padding: 4px 8px;
  }
}

/* Loading State */
.image-slider .main-image img[src*="placeholder"] {
  opacity: 0.8;
}

/* Smooth transitions */
.image-slider * {
  box-sizing: border-box;
}

/* Custom scrollbar for thumbnail swiper */
.image-slider .thumbs-swiper .swiper-wrapper {
  transition-timing-function: linear;
}

/* Hover effects */
.image-slider .thumbs-swiper .swiper-slide:hover {
  opacity: 1;
}

/* Focus styles for accessibility */
.image-slider .thumbnail:focus {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* Animation for smooth transitions */
.image-slider .main-swiper .swiper-slide {
  transition: opacity 0.3s ease;
}

/* Custom styles for better visual appeal */
.image-slider .main-swiper {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.image-slider .thumbs-swiper .swiper-slide {
  border-radius: 6px;
  overflow: hidden;
}

/* Ensure proper aspect ratio */
.image-slider .main-image,
.image-slider .thumbnail {
  position: relative;
}

.image-slider .main-image::before,
.image-slider .thumbnail::before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 49%, rgba(255,255,255,0.1) 50%, transparent 51%);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-slider .thumbnail:hover::before {
  opacity: 1;
}

/* Property-specific styles */
.property-image-slider {
  width: 100%;
}

.property-image-slider .main-swiper {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.property-image-slider .thumbs-swiper {
  margin-top: 15px;
}

.property-image-slider .thumbnail {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.property-image-slider .thumbs-swiper .swiper-slide-thumb-active .thumbnail {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px #1890ff, 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* Ensure proper spacing in the detail page layout */
@media (min-width: 768px) {
  .property-image-slider .main-swiper {
    height: 450px;
  }
}

@media (min-width: 1200px) {
  .property-image-slider .main-swiper {
    height: 500px;
  }
}