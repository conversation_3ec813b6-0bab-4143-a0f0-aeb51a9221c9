{"name": "sphere_web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/v5-patch-for-react-19": "^1.0.3", "@react-google-maps/api": "^2.20.6", "@stripe/react-stripe-js": "^3.7.0", "@tanstack/react-query": "^5.80.6", "antd": "^5.25.1", "axios": "^1.9.0", "bootstrap": "^5.3.6", "dayjs": "^1.11.13", "emoji-picker-react": "^4.12.2", "lodash": "^4.17.21", "react": "^19.1.0", "react-dom": "^19.1.0", "react-phone-number-input": "^3.4.12", "react-router-dom": "^7.5.3", "stripe": "^18.2.1", "sweetalert2": "^11.22.0", "swiper": "^11.2.8"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}