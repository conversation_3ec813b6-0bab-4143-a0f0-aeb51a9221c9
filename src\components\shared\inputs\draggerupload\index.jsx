import React, { useState, useEffect } from "react";
import {
  InboxOutlined,
  DeleteOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { Upload, message, Button, Progress } from "antd";
import FlatButton from "@/components/shared/button/flatbutton";
import { useMutation } from "@/hooks/reactQuery";

const { Dragger } = Upload;

const App = ({
  onChange,
  multiple = true,
  maxCount = 10,
  value = [],
  type = "property_image",
}) => {
  const [fileList, setFileList] = useState([]);
  const [uploadingFiles, setUploadingFiles] = useState(new Set());

  // File upload mutation
  const { mutate: uploadFile, isPending: isUploading } = useMutation(
    "fileUpload",
    {
      useFormData: true,
      showSuccessNotification: false,
      onSuccess: (response, variables) => {
        const { file, fileIndex } = variables;
        console.log("File upload success:", response);

        // Update the file in fileList with the returned ID
        setFileList((prevList) => {
          const updatedList = prevList.map((f) => {
            if (f.uid === file.uid) {
              return {
                ...f,
                status: "done",
                uploadedId: response.data?.id || response.id, // Store the uploaded file ID
                response: response,
              };
            }
            return f;
          });

          // Remove from uploading set
          setUploadingFiles((prev) => {
            const newSet = new Set(prev);
            newSet.delete(file.uid);
            return newSet;
          });

          // Notify parent component with updated files
          notifyParentOfChanges(updatedList);

          return updatedList;
        });
      },
      onError: (error, variables) => {
        const { file } = variables;
        console.error("File upload error:", error);
        message.error(`Failed to upload ${file.name}`);

        // Update file status to error
        setFileList((prevList) => {
          const updatedList = prevList.map((f) => {
            if (f.uid === file.uid) {
              return {
                ...f,
                status: "error",
                error: error.message || "Upload failed",
              };
            }
            return f;
          });

          // Remove from uploading set
          setUploadingFiles((prev) => {
            const newSet = new Set(prev);
            newSet.delete(file.uid);
            return newSet;
          });

          return updatedList;
        });
      },
    }
  );

  // Helper function to notify parent component
  const notifyParentOfChanges = (updatedFileList) => {
    if (onChange) {
      // Separate uploaded files (with IDs) from existing files
      const uploadedFiles = updatedFileList
        .filter((f) => f.uploadedId)
        .map((f) => ({
          id: f.uploadedId,
          name: f.name,
          url: f.url || f.thumbUrl,
        }));

      const existingFiles = updatedFileList
        .filter((f) => !f.isNew && !f.uploadedId && (f.id || f.url))
        .map((f) => ({ id: f.id, name: f.name, url: f.url || f.thumbUrl }));

      onChange([...existingFiles, ...uploadedFiles]);
    }
  };

  // Initialize fileList from value prop (for edit mode)
  useEffect(() => {
    console.log("DraggerUpload: Initializing with value:", value);

    if (value && value.length > 0) {
      const initialFiles = value.map((file, index) => {
        console.log(`Processing file ${index}:`, file);

        // Handle different file formats
        let fileData = {
          uid: file.id ? `existing-${file.id}` : `existing-${index}`,
          name: file.name || file.original_name || `Image ${index + 1}`,
          status: "done",
          id: file.id, // Store the original ID for tracking removals
        };

        // Handle different URL formats
        if (typeof file === "string") {
          // If file is just a URL string
          fileData.url = file;
          fileData.thumbUrl = file;
          console.log(`File ${index} is string URL:`, file);
        } else if (file.url) {
          // If file has url property
          fileData.url = file.url;
          fileData.thumbUrl = file.url;
          console.log(`File ${index} has url property:`, file.url);
        } else if (file.image_url) {
          // If file has image_url property
          fileData.url = file.image_url;
          fileData.thumbUrl = file.image_url;
          console.log(`File ${index} has image_url property:`, file.image_url);
        } else if (file instanceof File) {
          // If it's a File object, create object URL
          try {
            fileData.thumbUrl = URL.createObjectURL(file);
            fileData.originFileObj = file;
            console.log(
              `File ${index} is File object, created blob URL:`,
              fileData.thumbUrl
            );
          } catch (error) {
            console.error(
              `Error creating object URL for file ${index}:`,
              error
            );
          }
        } else {
          console.warn(`File ${index} format not recognized:`, file);
        }

        return fileData;
      });

      setFileList(initialFiles);
    } else {
      console.log("No value provided, clearing fileList");
      setFileList([]);
    }
  }, [value]);

  // Check if upload limit is reached
  const isUploadLimitReached = fileList.length >= maxCount;

  const props = {
    name: "file",
    multiple,
    maxCount,
    disabled: isUploadLimitReached, // Disable when limit reached
    beforeUpload: (file, fileListFromUpload) => {
      // Check if this would exceed maxCount first
      if (fileList.length >= maxCount) {
        message.error(`Maximum ${maxCount} images allowed!`);
        return false;
      }

      // Prevent automatic upload - return false to stop upload
      const isImage = file.type.startsWith("image/");
      if (!isImage) {
        message.error("You can only upload image files!");
        return false;
      }

      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error("Image must be smaller than 5MB!");
        return false;
      }

      // Check if this would exceed maxCount
      if (fileList.length + fileListFromUpload.length > maxCount) {
        message.error(`You can only upload up to ${maxCount} images!`);
        return false;
      }

      return false; // Prevent upload
    },
    onChange: (info) => {
      // Handle multiple file selection
      const { fileList: newFileList } = info;

      // Filter out files that failed validation
      const validFiles = newFileList.filter((file) => {
        if (file.status === "error") return false;

        const fileObj = file.originFileObj || file;
        const isImage = fileObj.type?.startsWith("image/");
        const isLt5M = (fileObj.size || 0) / 1024 / 1024 < 5;
        return isImage && isLt5M;
      });

      // Convert new files to our format and merge with existing files
      const newFormattedFiles = validFiles.map((file) => {
        const fileObj = file.originFileObj || file;
        return {
          uid: file.uid,
          name: file.name || fileObj.name,
          status: "uploading",
          originFileObj: fileObj,
          thumbUrl: URL.createObjectURL(fileObj),
          isNew: true, // Mark as new file
        };
      });

      // Keep existing files and add new ones
      const existingFiles = fileList.filter((f) => !f.isNew);
      const allFiles = [...existingFiles, ...newFormattedFiles];

      // Update file list
      setFileList(allFiles);

      // Upload new files one by one
      newFormattedFiles.forEach((file, index) => {
        if (file.originFileObj instanceof File) {
          // Add to uploading set
          setUploadingFiles((prev) => new Set(prev).add(file.uid));

          // Create FormData for the file
          const formData = new FormData();
          formData.append("file", file.originFileObj);
          formData.append("type", type); // Add type parameter

          // Upload the file
          uploadFile({
            data: formData,
            file: file,
            fileIndex: index,
          });
        }
      });
    },
    showUploadList: false, // disable default list inside dragger
    fileList: [],
    onDrop(e) {
      console.log("Dropped files", e.dataTransfer.files);
    },
  };

  const handleRemove = (fileToRemove) => {
    const updatedFileList = fileList.filter(
      (file) => file.uid !== fileToRemove.uid
    );
    setFileList(updatedFileList);

    // Clean up object URL to prevent memory leaks
    if (fileToRemove.thumbUrl && fileToRemove.thumbUrl.startsWith("blob:")) {
      URL.revokeObjectURL(fileToRemove.thumbUrl);
    }

    // Remove from uploading set if it was uploading
    setUploadingFiles((prev) => {
      const newSet = new Set(prev);
      newSet.delete(fileToRemove.uid);
      return newSet;
    });

    // Notify parent component with updated files
    notifyParentOfChanges(updatedFileList);

    // Also pass the removed file for tracking (for edit mode)
    if (onChange) {
      onChange(
        updatedFileList.length > 0
          ? updatedFileList
              .filter((f) => f.uploadedId || (!f.isNew && f.id))
              .map((f) => ({
                id: f.uploadedId || f.id,
                name: f.name,
                url: f.url || f.thumbUrl,
              }))
          : [],
        fileToRemove
      );
    }
  };

  // Cleanup object URLs on unmount
  useEffect(() => {
    return () => {
      fileList.forEach((file) => {
        if (file.thumbUrl && file.thumbUrl.startsWith("blob:")) {
          URL.revokeObjectURL(file.thumbUrl);
        }
      });
    };
  }, []);

  return (
    <>
      <Dragger
        {...props}
        style={{ height: 200, opacity: isUploadLimitReached ? 0.5 : 1 }}
      >
        <p className="ant-upload-drag-icon mt-4 ">
          <InboxOutlined />
        </p>
        {isUploadLimitReached ? (
          <>
            <p className="ant-upload-text mb-3" style={{ color: "#ff4d4f" }}>
              Maximum {maxCount} images reached
            </p>
            <p className="or-line" style={{ color: "#999" }}>
              Remove an image to upload more
            </p>
          </>
        ) : (
          <>
            <p className="ant-upload-text mb-3">
              Drag your file(s) to start uploading
            </p>
            <p className="or-line">Or</p>
            <FlatButton
              title="Browse File"
              className="browse-file mt-3 mb-3"
              disabled={isUploadLimitReached}
            />
          </>
        )}
      </Dragger>

      {/* Custom file list rendered below dragger */}
      {fileList.length > 0 && (
        <div style={{ marginTop: 20 }}>
          <div
            style={{
              marginBottom: 10,
              fontWeight: "bold",
              color: isUploadLimitReached
                ? "#ff4d4f"
                : fileList.length >= maxCount - 1
                ? "#faad14"
                : "#000",
            }}
          >
            Selected Images ({fileList.length}/{maxCount})
            {isUploadLimitReached && (
              <span
                style={{
                  fontSize: "12px",
                  marginLeft: "8px",
                  color: "#ff4d4f",
                }}
              >
                - Maximum reached
              </span>
            )}
          </div>
          {fileList.map((file) => (
            <div
              key={file.uid}
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                marginBottom: 8,
                border: "1px solid #ddd",
                padding: 10,
                borderRadius: 4,
                backgroundColor: "#fafafa",
              }}
            >
              <div style={{ display: "flex", alignItems: "center" }}>
                {file.thumbUrl || file.url ? (
                  <>
                    <img
                      src={file.thumbUrl || file.url}
                      alt={file.name}
                      style={{
                        width: 50,
                        height: 50,
                        marginRight: 10,
                        objectFit: "cover",
                        borderRadius: 4,
                        border: "1px solid #ddd",
                      }}
                      onError={(e) => {
                        // Fallback if image fails to load
                        console.error(
                          "Failed to load image:",
                          file.thumbUrl || file.url
                        );
                        e.target.style.display = "none";
                        e.target.nextSibling.style.display = "flex";
                      }}
                    />
                    <div
                      style={{
                        display: "none",
                        width: 50,
                        height: 50,
                        marginRight: 10,
                        backgroundColor: "#f0f0f0",
                        borderRadius: 4,
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <InboxOutlined style={{ fontSize: 20, color: "#999" }} />
                    </div>
                  </>
                ) : (
                  <div
                    style={{
                      display: "flex",
                      width: 50,
                      height: 50,
                      marginRight: 10,
                      backgroundColor: "#f0f0f0",
                      borderRadius: 4,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <InboxOutlined style={{ fontSize: 20, color: "#999" }} />
                  </div>
                )}
                <div style={{ flex: 1 }}>
                  <div style={{ fontWeight: 500 }}>{file.name}</div>
                  <div style={{ fontSize: 12, color: "#666" }}>
                    {file.status === "uploading" ? (
                      <span style={{ color: "#1890ff" }}>
                        <LoadingOutlined style={{ marginRight: 4 }} />
                        Uploading...
                      </span>
                    ) : file.status === "error" ? (
                      <span style={{ color: "#ff4d4f" }}>
                        Upload failed: {file.error}
                      </span>
                    ) : file.status === "done" && file.uploadedId ? (
                      <span style={{ color: "#52c41a" }}>Upload complete</span>
                    ) : file.isNew ? (
                      "New upload"
                    ) : (
                      ""
                    )}
                  </div>
                </div>
              </div>
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleRemove(file)}
                size="small"
                title="Remove image"
                disabled={uploadingFiles.has(file.uid)}
              />
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default App;
