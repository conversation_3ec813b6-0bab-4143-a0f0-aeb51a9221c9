import React from 'react';
import { GoogleMap, useLoadScript, Marker } from '@react-google-maps/api';

const containerStyle = {
  width: '100%',
  height: '400px',
};

const center = {
  lat: 40.7128, // New York City
  lng: -74.0060,
};

const MapComponent = () => {
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: "AIzaSyD_G-fake-demo-key-just-for-testing", // 🔁 Replace with your actual API key
  });

  if (loadError) {
    return (
      <div style={containerStyle}>
        <div style={{ 
          backgroundColor: '#eee', 
          width: '100%', 
          height: '100%', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center', 
          fontSize: '18px', 
          color: '#999' 
        }}>
          Map failed to load. Check API key.
        </div>
      </div>
    );
  }

  if (!isLoaded) {
    return <div>Loading Map...</div>;
  }

  return (
    <GoogleMap
      mapContainerStyle={containerStyle}
      center={center}
      zoom={10}
    >
      <Marker position={center} />
    </GoogleMap>
  );
};

export default MapComponent;
