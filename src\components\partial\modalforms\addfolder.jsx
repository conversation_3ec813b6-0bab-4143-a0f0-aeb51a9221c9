import { Form } from 'antd'
import React from 'react'
import BaseInput from '../../shared/inputs'

const AddFolder = () => {
  return (
    <Form
    name="login"
    layout="vertical"
    onFinish={() => setIsModalOpen(false)}
    initialValues={{
      remember: true,
    }}
    autoComplete="off"
  >
    <BaseInput
      name="folder_name"
      placeholder=""
      label="Folder Name"
    />
  </Form>
  )
}

export default AddFolder
