import React from "react";
import { Input } from "antd";
import { SearchOutlined, FilterOutlined } from "@ant-design/icons";
import { useScopedSearch } from "@/store/ScopedSearchContext";

const SearchBar = ({ onSearch, onFilterClick, filterIcon }) => {
  const { searchKeyword, setSearchKeyword } = useScopedSearch();

  const handleChange = (e) => {
    const value = e.target.value;
    // Call the original onSearch if provided (for backward compatibility)
    if (onSearch) {
      onSearch(value);
    }
    // Update the global search context immediately for responsive UI
    setSearchKeyword(value);
  };

  return (
    <Input
      placeholder="Search"
      prefix={<SearchOutlined style={{ color: "gray", fontSize: "150%" }} />}
      value={searchKeyword}
      onChange={handleChange}
      suffix={
        filterIcon ? (
          <span onClick={onFilterClick}>{filterIcon}</span>
        ) : (
          <FilterOutlined
            style={{ color: "gray", fontSize: "150%" }}
            onClick={onFilterClick}
          />
        )
      }
      style={{
        width: "100%",
        borderRadius: "4px",
        padding: "6px 10px",
        height: "40px",
      }}
      className="search-bar mt-4"
    />
  );
};

export default SearchBar;
