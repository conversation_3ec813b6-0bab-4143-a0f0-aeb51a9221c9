import{j as l}from"./index-3mE9H3a0.js";const t=c=>l.jsx("div",{className:"auth-sidebar",children:l.jsx("img",{src:c.src,alt:"",className:"img-fluid"})}),d=({children:c,logoClass:m="auth-logo",title:e,detail:i,src:a="../admin/assets/img/auth-img.png",showSidebar:n=!1,pageType:o="login"})=>{let s="";return o==="signup"?s="col-12 col-sm-12 col-md-12 col-lg-10 col-xl-10":o==="login"?s="col-12 col-sm-10 col-md-7 col-lg-5 col-xl-4":o==="forgot"?s="col-12 col-sm-12 col-md-8 col-lg-5 col-xl-4":s="col-12 col-sm-12 col-md-12 col-lg-10 col-xl-8",l.jsx("div",{className:"auth",children:l.jsx("div",{className:"container",children:l.jsxs("div",{className:"row align-items-center gx-0 justify-content-center",children:[l.jsx("div",{className:s,children:l.jsxs("div",{className:"auth-box",children:[l.jsxs("div",{className:"col-12",children:[l.jsx("p",{className:"font-36 color-black",children:e}),l.jsx("p",{children:i})]}),c]})}),n&&l.jsx("div",{className:"col-12 col-sm-12 col-md-6  col-lg-7 col-xl-7",children:l.jsx(t,{src:a})})]})})})};export{d as A};
