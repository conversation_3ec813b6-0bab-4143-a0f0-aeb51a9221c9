import React, { useState } from "react";
import { Form, Radio, Space } from "antd";

const ViewingForm = () => {
  const [value, setValue] = useState(1);
  const onChange = (e) => {
    setValue(e.target.value);
  };
  return (
    <Form
      name="filter"
      layout="vertical"
      onFinish={() => setIsViewingModalOpen(false)}
      initialValues={{
        remember: true,
      }}
      autoComplete="off"
    >
      <Radio.Group onChange={onChange} value={value}>
        <Space direction="vertical">
          <Radio value={1}>List</Radio>
          <Radio value={2}>Small thumbnails</Radio>
          <Radio value={3}>Large Thumbnails </Radio>
        </Space>
      </Radio.Group>
    </Form>
  );
};

export default ViewingForm;
