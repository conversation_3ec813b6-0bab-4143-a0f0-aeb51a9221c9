import { createContext, useContext, useState } from "react";

const PostFilterContext = createContext();

export const PostFilterProvider = ({ children }) => {
  const [filters, setFilters] = useState({});
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);

  const updateFilters = (newFilters) => {
    setFilters(newFilters); // Replace instead of merge
  };

  const clearFilters = () => {
    setFilters({}); // Clear filters
  };

  return (
    <PostFilterContext.Provider
      value={{
        filters,
        updateFilters,
        clearFilters,
        isFilterModalOpen,
        setIsFilterModalOpen,
      }}
    >
      {children}
    </PostFilterContext.Provider>
  );
};

export const usePostFilter = () => useContext(PostFilterContext);