import React, { useState } from 'react';
import { Card, Button, Input, List, Avatar, Spin, Alert, Form, Space } from 'antd';
import { HeartOutlined, HeartFilled, MessageOutlined, SendOutlined } from '@ant-design/icons';
import { usePostDetail, usePostComment, usePostLike } from '../../hooks/reactQuery/usePostDetail';
import { dateHelper } from '../../helpers/dateHelper';

const { TextArea } = Input;

/**
 * Post Detail Component
 * Displays post content with comments section
 * Updates in real-time when comments are posted
 */
const PostDetail = ({ slug, showComments = true }) => {
  const [commentForm] = Form.useForm();
  const [isCommenting, setIsCommenting] = useState(false);

  // Fetch post detail
  const { 
    data: post, 
    isLoading, 
    isError, 
    error, 
    refetch 
  } = usePostDetail(slug);

  // Comment mutation
  const { mutate: postComment, isPending: isPostingComment } = usePostComment({
    onSuccess: () => {
      commentForm.resetFields();
      setIsCommenting(false);
    },
  });

  // Like mutation
  const { mutate: toggleLike, isPending: isLiking } = usePostLike();

  const handleLike = () => {
    if (!post?.id) return;
    
    toggleLike({
      post_id: post.id,
      action: post.isLiked ? 'unlike' : 'like',
    });
  };

  const handleComment = (values) => {
    if (!post?.id || !values.message?.trim()) return;

    postComment({
      post_id: post.id,
      message: values.message.trim(),
    });
  };

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" tip="Loading post..." />
      </div>
    );
  }

  if (isError) {
    return (
      <Alert
        message="Error Loading Post"
        description={error?.message || 'Failed to load post details'}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={() => refetch()}>
            Retry
          </Button>
        }
      />
    );
  }

  if (!post) {
    return (
      <Alert
        message="Post Not Found"
        description="The requested post could not be found."
        type="warning"
        showIcon
      />
    );
  }

  return (
    <div style={{ maxWidth: 800, margin: '0 auto', padding: '20px' }}>
      {/* Post Content */}
      <Card
        style={{ marginBottom: showComments ? 20 : 0 }}
        actions={[
          <Button
            type="text"
            icon={post.isLiked ? <HeartFilled style={{ color: '#ff4d4f' }} /> : <HeartOutlined />}
            onClick={handleLike}
            loading={isLiking}
          >
            {post.likesCount || 0}
          </Button>,
          <Button
            type="text"
            icon={<MessageOutlined />}
            onClick={() => setIsCommenting(!isCommenting)}
          >
            {post.commentsCount || 0}
          </Button>,
        ]}
      >
        <Card.Meta
          avatar={<Avatar src={post.user?.avatar} size={40}>{post.user?.name?.[0]}</Avatar>}
          title={post.user?.name || 'Anonymous'}
          description={dateHelper.getRelativeTime(post.created_at)}
        />
        
        <div style={{ marginTop: 16 }}>
          <p style={{ fontSize: '16px', lineHeight: '1.6' }}>
            {post.content || post.description || post.message}
          </p>
          
          {post.image && (
            <img
              src={post.image}
              alt="Post content"
              style={{
                width: '100%',
                maxHeight: '400px',
                objectFit: 'cover',
                borderRadius: '8px',
                marginTop: '12px',
              }}
            />
          )}
        </div>
      </Card>

      {/* Comments Section */}
      {showComments && (
        <Card title={`Comments (${post.commentsCount || 0})`}>
          {/* Comment Form */}
          <Form
            form={commentForm}
            onFinish={handleComment}
            style={{ marginBottom: 20 }}
          >
            <Form.Item
              name="message"
              rules={[
                { required: true, message: 'Please enter your comment' },
                { min: 1, message: 'Comment cannot be empty' },
              ]}
            >
              <TextArea
                placeholder="Write a comment..."
                rows={3}
                maxLength={500}
                showCount
              />
            </Form.Item>
            
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SendOutlined />}
                  loading={isPostingComment}
                  disabled={isPostingComment}
                >
                  Post Comment
                </Button>
                
                {isCommenting && (
                  <Button onClick={() => setIsCommenting(false)}>
                    Cancel
                  </Button>
                )}
              </Space>
            </Form.Item>
          </Form>

          {/* Comments List */}
          {post.comments && post.comments.length > 0 ? (
            <List
              dataSource={post.comments}
              renderItem={(comment) => (
                <List.Item key={comment.id}>
                  <List.Item.Meta
                    avatar={
                      <Avatar src={comment.user?.avatar} size={32}>
                        {comment.user?.name?.[0] || 'U'}
                      </Avatar>
                    }
                    title={
                      <Space>
                        <span>{comment.user?.name || 'Anonymous'}</span>
                        <span style={{ fontSize: '12px', color: '#999' }}>
                          {dateHelper.getRelativeTime(comment.created_at)}
                        </span>
                        {comment.isOptimistic && (
                          <span style={{ fontSize: '12px', color: '#1890ff' }}>
                            Posting...
                          </span>
                        )}
                      </Space>
                    }
                    description={comment.message}
                  />
                </List.Item>
              )}
            />
          ) : (
            <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
              No comments yet. Be the first to comment!
            </div>
          )}
        </Card>
      )}
    </div>
  );
};

export default PostDetail;
