import{j as o}from"./index-3mE9H3a0.js";import{I as r}from"./index-C-I6oq0X.js";import{C as i}from"./contentblock-BFnsW9IO.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";import"./index-BUUUOhAK.js";const l=()=>{const t=["What is a privacy policy?","A privacy policy is a legal document where you disclose what data you collect from users...","Is the privacy policy generator free to use?","Why is a privacy policy important?","Where do I put my privacy policy?"];return o.jsx(r,{children:o.jsx("div",{className:"container-fluid mt-4",children:o.jsx(i,{title:"Privacy Policy",content:t})})})};export{l as default};
