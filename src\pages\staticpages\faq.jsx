// components/FaqAccordion.js
import { useState } from "react";
import InnerLayout from "../../components/shared/layout/innerlayout";
import FaqAccordion from "../../components/partial/contentblock/faqcontent";

const text = `
 Notice that we don’t ask “do I”, but “Why do I”. This leaves people no out. Even phrasing the question this way reassures people that this is a service that they need. Now is your chance to elaborate on the why by pointing out all of the things that you do. Namely, focus on how buying and selling properties is your job and you’ve been doing it for years. You can take all the stress off people’s plates, etc. 
`;
const items = [
  {
    key: "1",
    label: "Why do I need a real estate agent?",
    children: <p>{text}</p>,
  },
  {
    key: "2",
    label: "How long will it take to sell my property?",
    children: <p>{text}</p>,
  },
  {
    key: "3",
    label: "Why do I need a real estate agent?",
    children: <p>{text}</p>,
  },
  {
    key: "4",
    label: "Why do I need a real estate agent?",
    children: <p>{text}</p>,
  },
  {
    key: "5",
    label: "Why do I need a real estate agent?",
    children: <p>{text}</p>,
  },
  {
    key: "6",
    label: "Why do I need a real estate agent?",
    children: <p>{text}</p>,
  },
];
const Faq = () => {
  return (
    <InnerLayout>
      <div className="container-fluid">
        <div className="row">
          <div className="col-12">
            <FaqAccordion title="FAQ'S" items={items} defaultActiveKey={['3']} />
          </div>
        </div>
      </div>
    </InnerLayout>
  );
};

export default Faq;
