import React from "react";
import InnerLayout from "@/components/shared/layout/innerlayout";
import { Form } from "antd";
import BaseInput from "@/components/shared/inputs";
import FlatButton from "@/components/shared/button/flatbutton";
import { combineRules, validations } from "@/config/rules";
import { useMutation } from "@/hooks/reactQuery";
import { useNavigate } from "react-router-dom";
const ChangePassword = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const onFinish = (values) => {
    mutate(values);
  };
  const { mutate, isPending } = useMutation("changePassword", {
    useFormData: false,
    onSuccess: async (data) => {
      if (data) {
        navigate("/setting");
        form.resetFields();
      }
    },
  });
  return (
    <InnerLayout showSidebar={false} pageType="signup">
      <Form
        name="login"
        layout="vertical"
        onFinish={onFinish}
        form={form}
        initialValues={{
          remember: true,
        }}
        autoComplete="off"
      >
        <div className="container-fluid">
          <div className="row mt-5 justify-content-center">
            <div className="col-12 col-md-12 col-lg-7">
              <h2 className="mb-3">Chanage Password</h2>
            </div>
            <div className="col-12 col-md-8 col-lg-7">
              <BaseInput
                name="current_password"
                placeholder="***"
                label="Current Password"
                type="password"
                rules={combineRules(
                  "current-password",
                  validations.required,
                  validations.password
                )}
              />
            </div>
            <div className="col-12 col-md-8 col-lg-7">
              <BaseInput
                name="password"
                placeholder="***"
                label="New Password"
                type="password"
                rules={combineRules(
                  "new-password",
                  validations.required,
                  validations.password
                )}
              />
            </div>
            <div className="col-12 col-md-8 col-lg-7">
              <BaseInput
                name="confirm_password"
                placeholder="***"
                label="Confirm Password"
                type="password"
                rules={[
                  validations.required("confirm-password"),
                  ({ getFieldValue }) => ({
                    validator: (_, value) => {
                      const password = getFieldValue("password");
                      if (!value || !password) {
                        return Promise.resolve();
                      }
                      if (value !== password) {
                        return Promise.reject(
                          new Error("Confirm Passwords does not match")
                        );
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
                dependencies={["password"]}
              />
            </div>
          </div>

          <div className="text-center">
            <FlatButton
              title={isPending ? "Updating..." : "Update"}
              className="mx-auto mt-4 signin-btn signup-btn mt-5"
              htmlType="submit"
              loading={isPending}
              disabled={isPending}
            />
          </div>
        </div>
      </Form>
    </InnerLayout>
  );
};

export default ChangePassword;
