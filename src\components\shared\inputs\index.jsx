import React from "react";
import InputField from "./input";
import Pass<PERSON><PERSON>ield from "./passwordfield";
import SelectInput from "./select";
import CheckBoxInput from "./checkbox";
import CheckboxGroupInput from "./checkboxgroup";
import DatePickerInput from "./datepicker";
import TimePikerInput from "./timepiker";
import TextAreaInput from "./textarea";
import PhoneInputField from "./phonenumber";
import RadioInput from "./radio";
import Stripe<PERSON>ardField from "./stripecardfield";

const BaseInput = (props) => {
  if (props.type == "select") return <SelectInput {...props} />;
  else if (props.type == "password") return <PasswordField {...props} />;
  else if (props.type == "checkbox") return <CheckBoxInput {...props} />;
  else if (props.type == "checkboxgroup") return <CheckboxGroupInput {...props} />;
  else if (props.type == "datepicker") return <DatePickerInput {...props} />;
  else if (props.type == "timepiker") return <TimePikerInput {...props} />;
  else if (props.type == "textarea") return <TextAreaInput {...props} />;
  else if (props.type == "phonenumber") return <PhoneInputField {...props} />;
  else if (props.type === "radio") return <RadioInput {...props} />;
  else if (props.type === "stripecard") return <StripeCardField {...props} />;
  else return <InputField {...props} />;
};

export default BaseInput;
