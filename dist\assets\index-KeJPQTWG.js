import{r,M as w,U as _e,R as y,b as H,p as qe,V as ie,W as Xe,X as Ze,Y as ue,Z as Qe,N as A,$ as fe,g as T,a0 as Ge,a1 as Ue,t as Ye,o as K,a2 as Je,a3 as de,v as Ke,a4 as en,a5 as nn,a6 as tn,a7 as on,a8 as ln,a9 as rn,aa as sn}from"./index-3mE9H3a0.js";import{C as an,b as z,w as cn}from"./index-B2p2olBm.js";import{B as _,c as me}from"./button-CMBVME-6.js";import{u as Ce,D as un,p as fn,a as dn,P as mn}from"./index-DX-6A722.js";import{u as W}from"./react-stripe.esm-CaXK0k-R.js";import{S as Cn}from"./Skeleton-BqfVCYaM.js";function gn(){const[e,t]=r.useState([]),l=r.useCallback(n=>(t(o=>[].concat(w(o),[n])),()=>{t(o=>o.filter(c=>c!==n))}),[]);return[e,l]}function D(e){return!!(e!=null&&e.then)}const ge=e=>{const{type:t,children:l,prefixCls:n,buttonProps:o,close:c,autoFocus:C,emitEvent:u,isSilent:a,quitOnNullishReturnValue:g,actionFn:s}=e,i=r.useRef(!1),f=r.useRef(null),[b,O]=_e(!1),m=function(){c==null||c.apply(void 0,arguments)};r.useEffect(()=>{let d=null;return C&&(d=setTimeout(()=>{var x;(x=f.current)===null||x===void 0||x.focus({preventScroll:!0})})),()=>{d&&clearTimeout(d)}},[]);const v=d=>{D(d)&&(O(!0),d.then(function(){O(!1,!0),m.apply(void 0,arguments),i.current=!1},x=>{if(O(!1,!0),i.current=!1,!(a!=null&&a()))return Promise.reject(x)}))},p=d=>{if(i.current)return;if(i.current=!0,!s){m();return}let x;if(u){if(x=s(d),g&&!D(x)){i.current=!1,m(d);return}}else if(s.length)x=s(c),i.current=!1;else if(x=s(),!D(x)){m();return}v(x)};return r.createElement(_,Object.assign({},me(t),{onClick:p,loading:b,prefixCls:n},o,{ref:f}),l)},M=y.createContext({}),{Provider:be}=M,ee=()=>{const{autoFocusButton:e,cancelButtonProps:t,cancelTextLocale:l,isSilent:n,mergedOkCancel:o,rootPrefixCls:c,close:C,onCancel:u,onConfirm:a}=r.useContext(M);return o?y.createElement(ge,{isSilent:n,actionFn:u,close:function(){C==null||C.apply(void 0,arguments),a==null||a(!1)},autoFocus:e==="cancel",buttonProps:t,prefixCls:`${c}-btn`},l):null},ne=()=>{const{autoFocusButton:e,close:t,isSilent:l,okButtonProps:n,rootPrefixCls:o,okTextLocale:c,okType:C,onConfirm:u,onOk:a}=r.useContext(M);return y.createElement(ge,{isSilent:l,type:C||"primary",actionFn:a,close:function(){t==null||t.apply(void 0,arguments),u==null||u(!0)},autoFocus:e==="ok",buttonProps:n,prefixCls:`${o}-btn`},c)};function te(){const e={};for(var t=arguments.length,l=new Array(t),n=0;n<t;n++)l[n]=arguments[n];return l.forEach(o=>{o&&Object.keys(o).forEach(c=>{o[c]!==void 0&&(e[c]=o[c])})}),e}function oe(e){if(e)return{closable:e.closable,closeIcon:e.closeIcon}}function le(e){const{closable:t,closeIcon:l}=e||{};return y.useMemo(()=>{if(!t&&(t===!1||l===!1||l===null))return!1;if(t===void 0&&l===void 0)return null;let n={closeIcon:typeof l!="boolean"&&l!==null?l:void 0};return t&&typeof t=="object"&&(n=Object.assign(Object.assign({},n),t)),n},[t,l])}const bn={};function vn(e,t){let l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:bn;const n=le(e),o=le(t),[c]=W("global",ie.global),C=typeof n!="boolean"?!!(n!=null&&n.disabled):!1,u=y.useMemo(()=>Object.assign({closeIcon:y.createElement(H,null)},l),[l]),a=y.useMemo(()=>n===!1?!1:n?te(u,o,n):o===!1?!1:o?te(u,o):u.closable?u:!1,[n,o,u]);return y.useMemo(()=>{if(a===!1)return[!1,null,C,{}];const{closeIconRender:g}=u,{closeIcon:s}=a;let i=s;const f=qe(a,!0);return i!=null&&(g&&(i=g(s)),i=y.isValidElement(i)?y.cloneElement(i,Object.assign({"aria-label":c.close},f)):y.createElement("span",Object.assign({"aria-label":c.close},f),i)),[!0,i,C,f]},[a,u])}const pn=()=>Xe()&&window.document.documentElement;function re(){}const xn=r.createContext({add:re,remove:re});function yn(e){const t=r.useContext(xn),l=r.useRef(null);return Ze(o=>{if(o){const c=e?o.querySelector(e):o;t.add(c),l.current=c}else t.remove(l.current)})}const se=()=>{const{cancelButtonProps:e,cancelTextLocale:t,onCancel:l}=r.useContext(M);return y.createElement(_,Object.assign({onClick:l},e),t)},ae=()=>{const{confirmLoading:e,okButtonProps:t,okType:l,okTextLocale:n,onOk:o}=r.useContext(M);return y.createElement(_,Object.assign({},me(l),{loading:e,onClick:o},t),n)};function ve(e,t){return y.createElement("span",{className:`${e}-close-x`},t||y.createElement(H,{className:`${e}-close-icon`}))}const pe=e=>{const{okText:t,okType:l="primary",cancelText:n,confirmLoading:o,onOk:c,onCancel:C,okButtonProps:u,cancelButtonProps:a,footer:g}=e,[s]=W("Modal",ue()),i=t||(s==null?void 0:s.okText),f=n||(s==null?void 0:s.cancelText),b={confirmLoading:o,okButtonProps:u,cancelButtonProps:a,okTextLocale:i,cancelTextLocale:f,okType:l,onOk:c,onCancel:C},O=y.useMemo(()=>b,w(Object.values(b)));let m;return typeof g=="function"||typeof g>"u"?(m=y.createElement(y.Fragment,null,y.createElement(se,null),y.createElement(ae,null)),typeof g=="function"&&(m=g(m,{OkBtn:ae,CancelBtn:se})),m=y.createElement(be,{value:O},m)):m=g,y.createElement(Qe,{disabled:!1},m)};var On=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(l[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(l[n[o]]=e[n[o]]);return l};let V;const hn=e=>{V={x:e.pageX,y:e.pageY},setTimeout(()=>{V=null},100)};pn()&&document.documentElement.addEventListener("click",hn,!0);const xe=e=>{const{prefixCls:t,className:l,rootClassName:n,open:o,wrapClassName:c,centered:C,getContainer:u,focusTriggerAfterClose:a=!0,style:g,visible:s,width:i=520,footer:f,classNames:b,styles:O,children:m,loading:v,confirmLoading:p,zIndex:d,mousePosition:x,onOk:E,onCancel:P,destroyOnHidden:S,destroyOnClose:F}=e,k=On(e,["prefixCls","className","rootClassName","open","wrapClassName","centered","getContainer","focusTriggerAfterClose","style","visible","width","footer","classNames","styles","children","loading","confirmLoading","zIndex","mousePosition","onOk","onCancel","destroyOnHidden","destroyOnClose"]),{getPopupContainer:Se,getPrefixCls:q,direction:Ne,modal:h}=r.useContext(A),X=I=>{p||P==null||P(I)},ke=I=>{E==null||E(I)},$=q("modal",t),Z=q(),Q=fe($),[Me,G,Re]=Ce($,Q),Fe=T(c,{[`${$}-centered`]:C??(h==null?void 0:h.centered),[`${$}-wrap-rtl`]:Ne==="rtl"}),Be=f!==null&&!v?r.createElement(pe,Object.assign({},e,{onOk:ke,onCancel:X})):null,[U,Y,Le,ze]=vn(oe(e),oe(h),{closable:!0,closeIcon:r.createElement(H,{className:`${$}-close-icon`}),closeIconRender:I=>ve($,I)}),Ae=yn(`.${$}-content`),[We,De]=Ge("Modal",d),[Ve,B]=r.useMemo(()=>i&&typeof i=="object"?[void 0,i]:[i,void 0],[i]),He=r.useMemo(()=>{const I={};return B&&Object.keys(B).forEach(J=>{const L=B[J];L!==void 0&&(I[`--${$}-${J}-width`]=typeof L=="number"?`${L}px`:L)}),I},[B]);return Me(r.createElement(an,{form:!0,space:!0},r.createElement(Ue.Provider,{value:De},r.createElement(un,Object.assign({width:Ve},k,{zIndex:We,getContainer:u===void 0?Se:u,prefixCls:$,rootClassName:T(G,n,Re,Q),footer:Be,visible:o??s,mousePosition:x??V,onClose:X,closable:U&&Object.assign({disabled:Le,closeIcon:Y},ze),closeIcon:Y,focusTriggerAfterClose:a,transitionName:z(Z,"zoom",e.transitionName),maskTransitionName:z(Z,"fade",e.maskTransitionName),className:T(G,l,h==null?void 0:h.className),style:Object.assign(Object.assign(Object.assign({},h==null?void 0:h.style),g),He),classNames:Object.assign(Object.assign(Object.assign({},h==null?void 0:h.classNames),b),{wrapper:T(Fe,b==null?void 0:b.wrapper)}),styles:Object.assign(Object.assign({},h==null?void 0:h.styles),O),panelRef:Ae,destroyOnClose:S??F}),v?r.createElement(Cn,{active:!0,title:!1,paragraph:{rows:4},className:`${$}-body-skeleton`}):m))))},Pn=e=>{const{componentCls:t,titleFontSize:l,titleLineHeight:n,modalConfirmIconSize:o,fontSize:c,lineHeight:C,modalTitleHeight:u,fontHeight:a,confirmBodyPadding:g}=e,s=`${t}-confirm`;return{[s]:{"&-rtl":{direction:"rtl"},[`${e.antCls}-modal-header`]:{display:"none"},[`${s}-body-wrapper`]:Object.assign({},Je()),[`&${t} ${t}-body`]:{padding:g},[`${s}-body`]:{display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${e.iconCls}`]:{flex:"none",fontSize:o,marginInlineEnd:e.confirmIconMarginInlineEnd,marginTop:e.calc(e.calc(a).sub(o).equal()).div(2).equal()},[`&-has-title > ${e.iconCls}`]:{marginTop:e.calc(e.calc(u).sub(o).equal()).div(2).equal()}},[`${s}-paragraph`]:{display:"flex",flexDirection:"column",flex:"auto",rowGap:e.marginXS,maxWidth:`calc(100% - ${K(e.marginSM)})`},[`${e.iconCls} + ${s}-paragraph`]:{maxWidth:`calc(100% - ${K(e.calc(e.modalConfirmIconSize).add(e.marginSM).equal())})`},[`${s}-title`]:{color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:l,lineHeight:n},[`${s}-content`]:{color:e.colorText,fontSize:c,lineHeight:C},[`${s}-btns`]:{textAlign:"end",marginTop:e.confirmBtnsMarginTop,[`${e.antCls}-btn + ${e.antCls}-btn`]:{marginBottom:0,marginInlineStart:e.marginXS}}},[`${s}-error ${s}-body > ${e.iconCls}`]:{color:e.colorError},[`${s}-warning ${s}-body > ${e.iconCls},
        ${s}-confirm ${s}-body > ${e.iconCls}`]:{color:e.colorWarning},[`${s}-info ${s}-body > ${e.iconCls}`]:{color:e.colorInfo},[`${s}-success ${s}-body > ${e.iconCls}`]:{color:e.colorSuccess}}},En=Ye(["Modal","confirm"],e=>{const t=fn(e);return[Pn(t)]},dn,{order:-1e3});var $n=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(l[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(l[n[o]]=e[n[o]]);return l};function ye(e){const{prefixCls:t,icon:l,okText:n,cancelText:o,confirmPrefixCls:c,type:C,okCancel:u,footer:a,locale:g}=e,s=$n(e,["prefixCls","icon","okText","cancelText","confirmPrefixCls","type","okCancel","footer","locale"]);let i=l;if(!l&&l!==null)switch(C){case"info":i=r.createElement(ln,null);break;case"success":i=r.createElement(on,null);break;case"error":i=r.createElement(tn,null);break;default:i=r.createElement(nn,null)}const f=u??C==="confirm",b=e.autoFocusButton===null?!1:e.autoFocusButton||"ok",[O]=W("Modal"),m=g||O,v=n||(f?m==null?void 0:m.okText:m==null?void 0:m.justOkText),p=o||(m==null?void 0:m.cancelText),d=Object.assign({autoFocusButton:b,cancelTextLocale:p,okTextLocale:v,mergedOkCancel:f},s),x=r.useMemo(()=>d,w(Object.values(d))),E=r.createElement(r.Fragment,null,r.createElement(ee,null),r.createElement(ne,null)),P=e.title!==void 0&&e.title!==null,S=`${c}-body`;return r.createElement("div",{className:`${c}-body-wrapper`},r.createElement("div",{className:T(S,{[`${S}-has-title`]:P})},i,r.createElement("div",{className:`${c}-paragraph`},P&&r.createElement("span",{className:`${c}-title`},e.title),r.createElement("div",{className:`${c}-content`},e.content))),a===void 0||typeof a=="function"?r.createElement(be,{value:x},r.createElement("div",{className:`${c}-btns`},typeof a=="function"?a(E,{OkBtn:ne,CancelBtn:ee}):E)):a,r.createElement(En,{prefixCls:t}))}const jn=e=>{const{close:t,zIndex:l,maskStyle:n,direction:o,prefixCls:c,wrapClassName:C,rootPrefixCls:u,bodyStyle:a,closable:g=!1,onConfirm:s,styles:i}=e,f=`${c}-confirm`,b=e.width||416,O=e.style||{},m=e.mask===void 0?!0:e.mask,v=e.maskClosable===void 0?!1:e.maskClosable,p=T(f,`${f}-${e.type}`,{[`${f}-rtl`]:o==="rtl"},e.className),[,d]=Ke(),x=r.useMemo(()=>l!==void 0?l:d.zIndexPopupBase+en,[l,d]);return r.createElement(xe,Object.assign({},e,{className:p,wrapClassName:T({[`${f}-centered`]:!!e.centered},C),onCancel:()=>{t==null||t({triggerCancel:!0}),s==null||s(!1)},title:"",footer:null,transitionName:z(u||"","zoom",e.transitionName),maskTransitionName:z(u||"","fade",e.maskTransitionName),mask:m,maskClosable:v,style:O,styles:Object.assign({body:a,mask:n},i),width:b,zIndex:x,closable:g}),r.createElement(ye,Object.assign({},e,{confirmPrefixCls:f})))},Oe=e=>{const{rootPrefixCls:t,iconPrefixCls:l,direction:n,theme:o}=e;return r.createElement(de,{prefixCls:t,iconPrefixCls:l,direction:n,theme:o},r.createElement(jn,Object.assign({},e)))},N=[];let he="";function Pe(){return he}const In=e=>{var t,l;const{prefixCls:n,getContainer:o,direction:c}=e,C=ue(),u=r.useContext(A),a=Pe()||u.getPrefixCls(),g=n||`${a}-modal`;let s=o;return s===!1&&(s=void 0),y.createElement(Oe,Object.assign({},e,{rootPrefixCls:a,prefixCls:g,iconPrefixCls:u.iconPrefixCls,theme:u.theme,direction:c??u.direction,locale:(l=(t=u.locale)===null||t===void 0?void 0:t.Modal)!==null&&l!==void 0?l:C,getContainer:s}))};function R(e){const t=rn(),l=document.createDocumentFragment();let n=Object.assign(Object.assign({},e),{close:a,open:!0}),o,c;function C(){for(var s,i=arguments.length,f=new Array(i),b=0;b<i;b++)f[b]=arguments[b];if(f.some(v=>v==null?void 0:v.triggerCancel)){var m;(s=e.onCancel)===null||s===void 0||(m=s).call.apply(m,[e,()=>{}].concat(w(f.slice(1))))}for(let v=0;v<N.length;v++)if(N[v]===a){N.splice(v,1);break}c()}function u(s){clearTimeout(o),o=setTimeout(()=>{const i=t.getPrefixCls(void 0,Pe()),f=t.getIconPrefixCls(),b=t.getTheme(),O=y.createElement(In,Object.assign({},s));c=sn()(y.createElement(de,{prefixCls:i,iconPrefixCls:f,theme:b},t.holderRender?t.holderRender(O):O),l)})}function a(){for(var s=arguments.length,i=new Array(s),f=0;f<s;f++)i[f]=arguments[f];n=Object.assign(Object.assign({},n),{open:!1,afterClose:()=>{typeof e.afterClose=="function"&&e.afterClose(),C.apply(this,i)}}),n.visible&&delete n.visible,u(n)}function g(s){typeof s=="function"?n=s(n):n=Object.assign(Object.assign({},n),s),u(n)}return u(n),N.push(a),{destroy:a,update:g}}function Ee(e){return Object.assign(Object.assign({},e),{type:"warning"})}function $e(e){return Object.assign(Object.assign({},e),{type:"info"})}function je(e){return Object.assign(Object.assign({},e),{type:"success"})}function Ie(e){return Object.assign(Object.assign({},e),{type:"error"})}function Te(e){return Object.assign(Object.assign({},e),{type:"confirm"})}function Tn(e){let{rootPrefixCls:t}=e;he=t}var wn=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(l[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(l[n[o]]=e[n[o]]);return l};const Sn=(e,t)=>{var l,{afterClose:n,config:o}=e,c=wn(e,["afterClose","config"]);const[C,u]=r.useState(!0),[a,g]=r.useState(o),{direction:s,getPrefixCls:i}=r.useContext(A),f=i("modal"),b=i(),O=()=>{var d;n(),(d=a.afterClose)===null||d===void 0||d.call(a)},m=function(){var d;u(!1);for(var x=arguments.length,E=new Array(x),P=0;P<x;P++)E[P]=arguments[P];if(E.some(k=>k==null?void 0:k.triggerCancel)){var F;(d=a.onCancel)===null||d===void 0||(F=d).call.apply(F,[a,()=>{}].concat(w(E.slice(1))))}};r.useImperativeHandle(t,()=>({destroy:m,update:d=>{g(x=>Object.assign(Object.assign({},x),d))}}));const v=(l=a.okCancel)!==null&&l!==void 0?l:a.type==="confirm",[p]=W("Modal",ie.Modal);return r.createElement(Oe,Object.assign({prefixCls:f,rootPrefixCls:b},a,{close:m,open:C,afterClose:O,okText:a.okText||(v?p==null?void 0:p.okText:p==null?void 0:p.justOkText),direction:a.direction||s,cancelText:a.cancelText||(p==null?void 0:p.cancelText)},c))},Nn=r.forwardRef(Sn);let ce=0;const kn=r.memo(r.forwardRef((e,t)=>{const[l,n]=gn();return r.useImperativeHandle(t,()=>({patchElement:n}),[]),r.createElement(r.Fragment,null,l)}));function Mn(){const e=r.useRef(null),[t,l]=r.useState([]);r.useEffect(()=>{t.length&&(w(t).forEach(C=>{C()}),l([]))},[t]);const n=r.useCallback(c=>function(u){var a;ce+=1;const g=r.createRef();let s;const i=new Promise(v=>{s=v});let f=!1,b;const O=r.createElement(Nn,{key:`modal-${ce}`,config:c(u),ref:g,afterClose:()=>{b==null||b()},isSilent:()=>f,onConfirm:v=>{s(v)}});return b=(a=e.current)===null||a===void 0?void 0:a.patchElement(O),b&&N.push(b),{destroy:()=>{function v(){var p;(p=g.current)===null||p===void 0||p.destroy()}g.current?v():l(p=>[].concat(w(p),[v]))},update:v=>{function p(){var d;(d=g.current)===null||d===void 0||d.update(v)}g.current?p():l(d=>[].concat(w(d),[p]))},then:v=>(f=!0,i.then(v))}},[]);return[r.useMemo(()=>({info:n($e),success:n(je),error:n(Ie),warning:n(Ee),confirm:n(Te)}),[]),r.createElement(kn,{key:"modal-holder",ref:e})]}var Rn=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(l[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(l[n[o]]=e[n[o]]);return l};const Fn=e=>{const{prefixCls:t,className:l,closeIcon:n,closable:o,type:c,title:C,children:u,footer:a}=e,g=Rn(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:s}=r.useContext(A),i=s(),f=t||s("modal"),b=fe(i),[O,m,v]=Ce(f,b),p=`${f}-confirm`;let d={};return c?d={closable:o??!1,title:"",footer:"",children:r.createElement(ye,Object.assign({},e,{prefixCls:f,confirmPrefixCls:p,rootPrefixCls:i,content:u}))}:d={closable:o??!0,title:C,footer:a!==null&&r.createElement(pe,Object.assign({},e)),children:u},O(r.createElement(mn,Object.assign({prefixCls:f,className:T(m,`${f}-pure-panel`,c&&p,c&&`${p}-${c}`,l,v,b)},g,{closeIcon:ve(f,n),closable:o},d)))},Bn=cn(Fn);function we(e){return R(Ee(e))}const j=xe;j.useModal=Mn;j.info=function(t){return R($e(t))};j.success=function(t){return R(je(t))};j.error=function(t){return R(Ie(t))};j.warning=we;j.warn=we;j.confirm=function(t){return R(Te(t))};j.destroyAll=function(){for(;N.length;){const t=N.pop();t&&t()}};j.config=Tn;j._InternalPanelDoNotUseOrYouWillBeFired=Bn;export{j as M};
