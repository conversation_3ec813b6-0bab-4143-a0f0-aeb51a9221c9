var D=s=>{throw TypeError(s)};var w=(s,t,e)=>t.has(s)||D("Cannot "+e);var r=(s,t,e)=>(w(s,t,"read from private field"),e?e.call(s):t.get(s)),E=(s,t,e)=>t.has(s)?D("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(s):t.set(s,e),O=(s,t,e,i)=>(w(s,t,"write to private field"),i?i.call(s,e):t.set(s,e),e),K=(s,t,e)=>(w(s,t,"access private method"),e);import{ad as U,ag as H,aL as R,aM as P,ap as L,D as T,r as m,ai as $,aq as N,ar as Q,T as z}from"./index-3mE9H3a0.js";var y,b,u,f,p,S,A,k,B=(k=class extends U{constructor(t,e){super();E(this,p);E(this,y);E(this,b);E(this,u);E(this,f);O(this,y,t),this.setOptions(e),this.bindMethods(),K(this,p,S).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){var i;const e=this.options;this.options=r(this,y).defaultMutationOptions(t),H(this.options,e)||r(this,y).getMutationCache().notify({type:"observerOptionsUpdated",mutation:r(this,u),observer:this}),e!=null&&e.mutationKey&&this.options.mutationKey&&R(e.mutationKey)!==R(this.options.mutationKey)?this.reset():((i=r(this,u))==null?void 0:i.state.status)==="pending"&&r(this,u).setOptions(this.options)}onUnsubscribe(){var t;this.hasListeners()||(t=r(this,u))==null||t.removeObserver(this)}onMutationUpdate(t){K(this,p,S).call(this),K(this,p,A).call(this,t)}getCurrentResult(){return r(this,b)}reset(){var t;(t=r(this,u))==null||t.removeObserver(this),O(this,u,void 0),K(this,p,S).call(this),K(this,p,A).call(this)}mutate(t,e){var i;return O(this,f,e),(i=r(this,u))==null||i.removeObserver(this),O(this,u,r(this,y).getMutationCache().build(r(this,y),this.options)),r(this,u).addObserver(this),r(this,u).execute(t)}},y=new WeakMap,b=new WeakMap,u=new WeakMap,f=new WeakMap,p=new WeakSet,S=function(){var e;const t=((e=r(this,u))==null?void 0:e.state)??P();O(this,b,{...t,isPending:t.status==="pending",isSuccess:t.status==="success",isError:t.status==="error",isIdle:t.status==="idle",mutate:this.mutate,reset:this.reset})},A=function(t){L.batch(()=>{var e,i,n,o,a,l,c,v;if(r(this,f)&&this.hasListeners()){const h=r(this,b).variables,g=r(this,b).context;(t==null?void 0:t.type)==="success"?((i=(e=r(this,f)).onSuccess)==null||i.call(e,t.data,h,g),(o=(n=r(this,f)).onSettled)==null||o.call(n,t.data,null,h,g)):(t==null?void 0:t.type)==="error"&&((l=(a=r(this,f)).onError)==null||l.call(a,t.error,h,g),(v=(c=r(this,f)).onSettled)==null||v.call(c,void 0,t.error,h,g))}this.listeners.forEach(h=>{h(r(this,b))})})},k);function G(s,t){const e=T(),[i]=m.useState(()=>new B(e,s));m.useEffect(()=>{i.setOptions(s)},[i,s]);const n=m.useSyncExternalStore(m.useCallback(a=>i.subscribe(L.batchCalls(a)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),o=m.useCallback((a,l)=>{i.mutate(a,l).catch($)},[i]);if(n.error&&N(i.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:o,mutateAsync:n.mutate}}function Y(s,t={}){const{useFormData:e=!0,showSuccessNotification:i=!0,invalidateQueries:n=[],method:o,headers:a,token:l,onSuccess:c,...v}=t,h=T(),g=Q[s];if(!g)throw new Error(`API endpoint "${s}" not found`);const C=o||g.method,j=m.useCallback(async d=>{const{data:M,slug:x}=d&&typeof d=="object"&&"data"in d?d:{data:d,slug:void 0},q=C==="DELETE";return await z.request(s,{...!q&&{data:M},slug:x,useFormData:!q&&e,showSuccessNotification:i,method:C,customHeaders:a,token:l})},[s,C,e,i,l]),F=m.useCallback((d,M,x)=>{typeof n=="function"?n({data:d,variables:M},h):J(n,h,{data:d,variables:M}),c&&c(d,M,x)},[n,h,c]);return G({mutationFn:j,onSuccess:F,networkMode:"online",...v})}function J(s,t,{data:e,variables:i}){if(s)try{let n=[];typeof s=="function"?n=s({data:e,variables:i}):Array.isArray(s)&&(n=s),n.length>0&&n.forEach(o=>{if(o)if(typeof o=="object"&&o.queryKey){const a={queryKey:Array.isArray(o.queryKey)?o.queryKey:[o.queryKey]};o.exact!==void 0&&(a.exact=o.exact),o.type==="paginated"&&(a.predicate=l=>{const c=l.queryKey,v=a.queryKey;return c[0]===v[0]&&(c.length>1||c.some(h=>typeof h=="string"&&h.includes('"page"')))},delete a.exact),t.invalidateQueries(a)}else t.invalidateQueries({queryKey:Array.isArray(o)?o:[o]})})}catch(n){console.error("Error during query invalidation:",n)}}export{Y as u};
