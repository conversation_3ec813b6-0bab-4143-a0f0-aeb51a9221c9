/* Subscription Screen Styles */
.subscription-container {
  max-width: 1200px;
  
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.header .logo {
  margin: 20px 0;
}

.header .logo img {
  max-width: 200px;
  height: auto;
}

.header h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 10px;
}

.header p {
  font-size: 1.2rem;
  color: #666;
}

/* Plan Options */
.plan-options {
  margin-bottom: 40px;
}

.plans-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.plan-card {
  background: white;
  border: 2px solid #e8e8e8;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.plan-card:hover {
  border-color: #1890ff;
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.subscription-status {
  position: absolute;
  top: -10px;
  right: 20px;
  background: #52c41a;
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.plan-header h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 15px;
}

.plan-price {
  margin-bottom: 20px;
}

.plan-price .price {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1890ff;
}

.plan-price .period {
  font-size: 1rem;
  color: #666;
  margin-left: 5px;
}

.plan-description {
  color: #666;
  margin-bottom: 15px;
  line-height: 1.5;
}

.auto-renew-note {
  font-size: 0.9rem;
  color: #999;
  margin-bottom: 25px;
}

.subscribe-btn, .cancel-subscription-btn {
  width: 100%;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.subscribe-btn {
  background: #1890ff;
  color: white;
  border: none;
}

.subscribe-btn:hover {
  background: #40a9ff;
  transform: translateY(-2px);
}

.cancel-subscription-btn {
  background: #ff4d4f;
  color: white;
  border: none;
}

.cancel-subscription-btn:hover {
  background: #ff7875;
}

/* Subscription Offer */
.subscription-offer {
  text-align: center;
  background: #f0f9ff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.subscription-offer p {
  margin: 0;
  font-size: 1.1rem;
  color: #1890ff;
}

/* Payment Form Styles */
.payment-form-container {
  padding: 20px;
}

.selected-plan-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.selected-plan-info h3 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.2rem;
}

.selected-plan-info p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.card-section {
  margin-bottom: 30px;
}

.card-section h4 {
  margin-bottom: 20px;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.card-field {
  margin-bottom: 20px;
}

.card-field label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.stripe-element {
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.stripe-element:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.card-row {
  display: flex;
  gap: 15px;
}

.card-row .card-field {
  flex: 1;
}

.form-actions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e8e8e8;
}

.form-actions .row {
  display: flex;
  gap: 15px;
}

.form-actions .col-12 {
  flex: 1;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #d9d9d9;
  padding: 12px 24px;
  border-radius: 6px;
  width: 100%;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.payment-btn {
  background: #1890ff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  width: 100%;
  font-weight: 600;
  transition: all 0.3s ease;
}

.payment-btn:hover:not(:disabled) {
  background: #40a9ff;
  transform: translateY(-1px);
}

.payment-btn:disabled {
  background: #d9d9d9;
  color: #999;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .plans-container {
    grid-template-columns: 1fr;
  }
  
  .card-row {
    flex-direction: column;
    gap: 10px;
  }
  
  .form-actions .row {
    flex-direction: column;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .plan-price .price {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .subscription-container {
    padding: 10px;
  }
  
  .plan-card {
    padding: 20px;
  }
  
  .payment-form-container {
    padding: 15px;
  }
}