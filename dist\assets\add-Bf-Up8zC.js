import{r as x,B as U,j as e,C as se,u as ae,c as te,D as re,E as ie}from"./index-3mE9H3a0.js";import{I as oe}from"./index-C-I6oq0X.js";import{B as p,c as m,P as ne,D as ce,v as n}from"./index-Cd-Wothc.js";import{F as D}from"./flatbutton-Yo0mdDJ8.js";import{u as T}from"./useMutation-lvneVzbk.js";import{U as de}from"./index-CY48Knbi.js";import{R}from"./InboxOutlined-ERhPIBBD.js";import{B as me}from"./button-CMBVME-6.js";import{R as ue}from"./DeleteOutlined-DCDN0YWx.js";import{u as pe,a as he}from"./useLocationData-QmC5yoKM.js";import{a as ge}from"./useQuery-Bzo2W4ue.js";import{F as z}from"./react-stripe.esm-CaXK0k-R.js";import"./index-B2p2olBm.js";import"./index-BUUUOhAK.js";import"./fade-yKzH711D.js";const{Dragger:ye}=de,be=({onChange:b,multiple:w=!0,maxCount:o=10,value:d=[],type:$="property_image"})=>{const[y,v]=x.useState([]),[L,j]=x.useState(new Set),{mutate:P}=T("fileUpload",{useFormData:!0,showSuccessNotification:!1,onSuccess:(l,s)=>{const{file:a,fileIndex:t}=s;console.log("File upload success:",l),v(S=>{const u=S.map(c=>{var h;return c.uid===a.uid?{...c,status:"done",uploadedId:((h=l.data)==null?void 0:h.id)||l.id,response:l}:c});return j(c=>{const h=new Set(c);return h.delete(a.uid),h}),F(u),u})},onError:(l,s)=>{const{file:a}=s;console.error("File upload error:",l),U.error(`Failed to upload ${a.name}`),v(t=>{const S=t.map(u=>u.uid===a.uid?{...u,status:"error",error:l.message||"Upload failed"}:u);return j(u=>{const c=new Set(u);return c.delete(a.uid),c}),S})}}),F=l=>{if(b){const s=l.filter(t=>t.uploadedId).map(t=>({id:t.uploadedId,name:t.name,url:t.url||t.thumbUrl})),a=l.filter(t=>!t.isNew&&!t.uploadedId&&(t.id||t.url)).map(t=>({id:t.id,name:t.name,url:t.url||t.thumbUrl}));b([...a,...s])}};x.useEffect(()=>{if(console.log("DraggerUpload: Initializing with value:",d),d&&d.length>0){const l=d.map((s,a)=>{console.log(`Processing file ${a}:`,s);let t={uid:s.id?`existing-${s.id}`:`existing-${a}`,name:s.name||s.original_name||`Image ${a+1}`,status:"done",id:s.id};if(typeof s=="string")t.url=s,t.thumbUrl=s,console.log(`File ${a} is string URL:`,s);else if(s.url)t.url=s.url,t.thumbUrl=s.url,console.log(`File ${a} has url property:`,s.url);else if(s.image_url)t.url=s.image_url,t.thumbUrl=s.image_url,console.log(`File ${a} has image_url property:`,s.image_url);else if(s instanceof File)try{t.thumbUrl=URL.createObjectURL(s),t.originFileObj=s,console.log(`File ${a} is File object, created blob URL:`,t.thumbUrl)}catch(S){console.error(`Error creating object URL for file ${a}:`,S)}else console.warn(`File ${a} format not recognized:`,s);return t});v(l)}else console.log("No value provided, clearing fileList"),v([])},[d]);const f=y.length>=o,E={name:"file",multiple:w,maxCount:o,disabled:f,beforeUpload:(l,s)=>y.length>=o?(U.error(`Maximum ${o} images allowed!`),!1):l.type.startsWith("image/")?l.size/1024/1024<5?(y.length+s.length>o&&U.error(`You can only upload up to ${o} images!`),!1):(U.error("Image must be smaller than 5MB!"),!1):(U.error("You can only upload image files!"),!1),onChange:l=>{const{fileList:s}=l,t=s.filter(c=>{var _;if(c.status==="error")return!1;const h=c.originFileObj||c,N=(_=h.type)==null?void 0:_.startsWith("image/"),I=(h.size||0)/1024/1024<5;return N&&I}).map(c=>{const h=c.originFileObj||c;return{uid:c.uid,name:c.name||h.name,status:"uploading",originFileObj:h,thumbUrl:URL.createObjectURL(h),isNew:!0}}),u=[...y.filter(c=>!c.isNew),...t];v(u),t.forEach((c,h)=>{if(c.originFileObj instanceof File){j(I=>new Set(I).add(c.uid));const N=new FormData;N.append("file",c.originFileObj),N.append("type",$),P({data:N,file:c,fileIndex:h})}})},showUploadList:!1,fileList:[],onDrop(l){console.log("Dropped files",l.dataTransfer.files)}},B=l=>{const s=y.filter(a=>a.uid!==l.uid);v(s),l.thumbUrl&&l.thumbUrl.startsWith("blob:")&&URL.revokeObjectURL(l.thumbUrl),j(a=>{const t=new Set(a);return t.delete(l.uid),t}),F(s),b&&b(s.length>0?s.filter(a=>a.uploadedId||!a.isNew&&a.id).map(a=>({id:a.uploadedId||a.id,name:a.name,url:a.url||a.thumbUrl})):[],l)};return x.useEffect(()=>()=>{y.forEach(l=>{l.thumbUrl&&l.thumbUrl.startsWith("blob:")&&URL.revokeObjectURL(l.thumbUrl)})},[]),e.jsxs(e.Fragment,{children:[e.jsxs(ye,{...E,style:{height:200,opacity:f?.5:1},children:[e.jsx("p",{className:"ant-upload-drag-icon mt-4 ",children:e.jsx(R,{})}),f?e.jsxs(e.Fragment,{children:[e.jsxs("p",{className:"ant-upload-text mb-3",style:{color:"#ff4d4f"},children:["Maximum ",o," images reached"]}),e.jsx("p",{className:"or-line",style:{color:"#999"},children:"Remove an image to upload more"})]}):e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"ant-upload-text mb-3",children:"Drag your file(s) to start uploading"}),e.jsx("p",{className:"or-line",children:"Or"}),e.jsx(D,{title:"Browse File",className:"browse-file mt-3 mb-3",disabled:f})]})]}),y.length>0&&e.jsxs("div",{style:{marginTop:20},children:[e.jsxs("div",{style:{marginBottom:10,fontWeight:"bold",color:f?"#ff4d4f":y.length>=o-1?"#faad14":"#000"},children:["Selected Images (",y.length,"/",o,")",f&&e.jsx("span",{style:{fontSize:"12px",marginLeft:"8px",color:"#ff4d4f"},children:"- Maximum reached"})]}),y.map(l=>e.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:8,border:"1px solid #ddd",padding:10,borderRadius:4,backgroundColor:"#fafafa"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[l.thumbUrl||l.url?e.jsxs(e.Fragment,{children:[e.jsx("img",{src:l.thumbUrl||l.url,alt:l.name,style:{width:50,height:50,marginRight:10,objectFit:"cover",borderRadius:4,border:"1px solid #ddd"},onError:s=>{console.error("Failed to load image:",l.thumbUrl||l.url),s.target.style.display="none",s.target.nextSibling.style.display="flex"}}),e.jsx("div",{style:{display:"none",width:50,height:50,marginRight:10,backgroundColor:"#f0f0f0",borderRadius:4,alignItems:"center",justifyContent:"center"},children:e.jsx(R,{style:{fontSize:20,color:"#999"}})})]}):e.jsx("div",{style:{display:"flex",width:50,height:50,marginRight:10,backgroundColor:"#f0f0f0",borderRadius:4,alignItems:"center",justifyContent:"center"},children:e.jsx(R,{style:{fontSize:20,color:"#999"}})}),e.jsxs("div",{style:{flex:1},children:[e.jsx("div",{style:{fontWeight:500},children:l.name}),e.jsx("div",{style:{fontSize:12,color:"#666"},children:l.status==="uploading"?e.jsxs("span",{style:{color:"#1890ff"},children:[e.jsx(se,{style:{marginRight:4}}),"Uploading..."]}):l.status==="error"?e.jsxs("span",{style:{color:"#ff4d4f"},children:["Upload failed: ",l.error]}):l.status==="done"&&l.uploadedId?e.jsx("span",{style:{color:"#52c41a"},children:"Upload complete"}):l.isNew?"New upload":""})]})]}),e.jsx(me,{type:"text",danger:!0,icon:e.jsx(ue,{}),onClick:()=>B(l),size:"small",title:"Remove image",disabled:L.has(l.uid)})]},l.uid))]})]})},ze=()=>{var M,Y;const[b]=z.useForm(),w=ae(),{id:o}=te(),d=re(),[$,y]=x.useState(!1),[v,L]=x.useState(!1),[j,P]=x.useState([]),[F,f]=x.useState([]),[E,B]=x.useState([]),{data:l,loading:s}=pe(),a=l==null?void 0:l.data,{data:t,isLoading:S}=ge("properties",{slug:o,staleTime:5*60*1e3,gcTime:10*60*1e3,enabled:!!o}),u=t==null?void 0:t.data,{selectedState:c,stateOptions:h,cityOptions:N,statesLoading:I,citiesLoading:_,handleStateChange:X,updateSelectedState:Q}=he(),{mutate:k,isPending:H}=T("addProperty",{useFormData:!1,onSuccess:async r=>{var g;await d.invalidateQueries({queryKey:["properties"],exact:!1}),await d.invalidateQueries({queryKey:["getProperty"],exact:!1}),await d.invalidateQueries({queryKey:["getUser"],exact:!1}),d.removeQueries({queryKey:["getProperty"],exact:!1});const i=((g=r==null?void 0:r.data)==null?void 0:g.id)||(r==null?void 0:r.id);i&&(console.log("Invalidating specific property query for ID:",i),await d.invalidateQueries({queryKey:["getProperty",i.toString()]}),await d.invalidateQueries({queryKey:["getProperty",i]})),await d.refetchQueries({queryKey:["properties"]}),localStorage.setItem(`property_updated_${o}`,Date.now().toString()),localStorage.removeItem(`property_updated_${o}`),r&&w("/listing")}}),{mutate:V,isPending:W}=T("updateProperty",{useFormData:!1,onSuccess:async r=>{await d.invalidateQueries({queryKey:["properties"],exact:!1}),await d.invalidateQueries({queryKey:["getProperty"],exact:!1}),await d.invalidateQueries({queryKey:["getUser"],exact:!1}),d.removeQueries({queryKey:["getProperty"],exact:!1}),o&&(console.log("Invalidating specific property query for ID:",o),d.removeQueries({queryKey:["getProperty",o.toString()]}),d.removeQueries({queryKey:["getProperty",o]}),await d.invalidateQueries({queryKey:["getProperty",o.toString()],exact:!1}),await d.invalidateQueries({queryKey:["getProperty",o],exact:!1}),await d.refetchQueries({queryKey:["getProperty",o.toString()],type:"active"}),await d.refetchQueries({queryKey:["getProperty",o],type:"active"})),await d.refetchQueries({queryKey:["properties"]}),r&&w("/listing")}}),C=H||W,Z=r=>{X(r,b)},G=[{value:"$",label:"$"},{value:"%",label:"%"}],K=[{value:"sqft",label:"Sq.Ft"},{value:"sq.m",label:"Sq.M"},{value:"sq.yd",label:"Sq.Yd"},{value:"acres",label:"Acres"}],A=Array.from({length:10},(r,i)=>({value:i+1,label:i+1})),J=Array.from({length:10},(r,i)=>({value:i+1,label:`${i+1} Car${i+1>1?"s":""}`})),ee=r=>{const i={...r,basement:r.basement===!0||r.basement==="true",hoa:r.hoa===!0||r.hoa==="true"};if(delete i.images,i.year_built&&(i.year_built=parseInt(i.year_built.format("YYYY"),10)),j&&j.length>0){const g=j.map(O=>O.id||O.uploadedId).filter(Boolean);g.length>0&&(i.images=g)}o&&F.length>0&&(i.remove_image_ids=F),i.hoa||(delete i.hoa_price,delete i.hoa_type),console.log("Form data:",i),o?V({slug:o,data:i}):k(i)},le=(r,i=null)=>{const g=r.filter(q=>q instanceof File),O=r.filter(q=>!(q instanceof File));P([...O,...g]),i&&i.id&&o&&f(q=>[...q,i.id])};return x.useEffect(()=>{if(u&&o){const r={...u,year_built:u.year_built?ie().year(u.year_built):null};y(u.basement||!1),L(u.hoa||!1),P(u.images||[]),B(u.images||[]),f([]),u.state&&Q(u.state),setTimeout(()=>{b.setFieldsValue(r)},100)}},[u,o,b,Q]),e.jsx(oe,{children:e.jsx("div",{className:"container-fluid",children:e.jsxs("div",{className:"row mt-5",children:[e.jsx("div",{className:"col-12",children:e.jsxs("h2",{children:[o?"Edit":"Add"," Listing"]})}),e.jsx("div",{className:"col-12",children:e.jsxs(z,{form:b,name:"propertyForm",layout:"vertical",onFinish:ee,initialValues:!0,scrollToFirstError:!0,autoComplete:"off",className:"add-listing",children:[e.jsxs("div",{className:"row gx-5",children:[e.jsx("div",{className:"col-12",children:e.jsx("p",{className:"font-18 mt-4",children:"Property Details"})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(p,{name:"type_id",placeholder:"Select Property Type",label:"Property Type",type:"select",rules:m("Property Type",n.required),options:(M=a==null?void 0:a.propertyTypes)==null?void 0:M.map(r=>({value:r.id,label:r.name})),loading:s})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(p,{name:"home_style_id",placeholder:"Select Home Style",label:"Home Style",type:"select",rules:m("Home Style",n.required),options:(Y=a==null?void 0:a.homeStyles)==null?void 0:Y.map(r=>({value:r.id,label:r.name})),loading:s})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(ne,{name:"mobile_no",placeholder:"(XXX) XXX-XXXX",label:"Phone Number",rules:m("mobile-number",n.required,n.phone)})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(p,{name:"mls_id",placeholder:"Enter MLS ID",label:"MLS ID (optional)"})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(p,{name:"address",placeholder:"xyz street",label:"Address",rules:m("Address",n.required)})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-2",children:e.jsx(p,{name:"state",placeholder:"Select State",options:h,loading:I,handlechange:Z,showSearch:!0,label:"State",type:"select",rules:m("State",n.required),filterOption:(r,i)=>{var g;return(g=i==null?void 0:i.label)==null?void 0:g.toLowerCase().includes(r.toLowerCase())}})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-2",children:e.jsx(p,{name:"city",placeholder:"Select City",options:N,loading:_,disabled:!c,showSearch:!0,rules:m("City",n.required),label:"City",type:"select",filterOption:(r,i)=>{var g;return(g=i==null?void 0:i.label)==null?void 0:g.toLowerCase().includes(r.toLowerCase())}})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(p,{name:"zip",placeholder:"Enter Zip Code",label:"Zip Code",rules:m("Zip Code",n.required)})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(p,{name:"price",placeholder:"Enter Price ($)",label:"Price",type:"number",rules:m("Price",n.required,n.greaterThanOne)})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-8",children:e.jsx(p,{name:"buy_side_compensation",placeholder:"Enter Compensation",label:"Buy-Side Compensation",rules:m("Buy Side Compensation",n.required,n.greaterThanOne),type:"number"})}),e.jsx("div",{className:"col-4",children:e.jsx(p,{name:"buy_side_compensation_type",placeholder:"Enter Compensation",label:"Type",options:G,rules:m("Buy Side Compensation Type",n.required),type:"select"})})]})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-8",children:e.jsx(p,{name:"size",placeholder:"Enter Property Size",label:"Property Size",type:"number",rules:m("Property Size",n.required,n.greaterThanOne)})}),e.jsx("div",{className:"col-4",children:e.jsx(p,{placeholder:"Unit",options:K,name:"size_type",label:"Size Type",rules:m("Property Size Type",n.required),type:"select"})})]})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-8",children:e.jsx(p,{name:"lot_size",placeholder:"Enter Lot Size",label:"Lot Size",type:"number",rules:m("Lot Size",n.required,n.greaterThanOne)})}),e.jsx("div",{className:"col-4",children:e.jsx(p,{placeholder:"Select Type",label:"Size Type",options:K,rules:m("Lot Size Type",n.required),name:"lot_type",type:"select"})})]})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(z.Item,{name:"year_built",label:"Year Built",rules:m("Year Built",n.required),children:e.jsx(ce,{picker:"year",placeholder:"Select Year",style:{width:"100%"}})})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(p,{name:"bed",placeholder:"No. of Bed",label:"Bed",type:"select",options:A,rules:m("Bed",n.required)})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(p,{name:"bath",placeholder:"No. of Bath",label:"Bath",type:"select",options:A,rules:m("Bath",n.required)})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-4",children:e.jsx(p,{name:"garage",placeholder:"No. of Cars",label:"Garage",type:"select",options:J,rules:m("Garage",n.required)})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-3",children:e.jsx(p,{type:"radio",name:"hoa",label:"HOA",options:[{value:!0,label:"Yes"},{value:!1,label:"No"}],rules:m("HOA",n.required),handlechange:r=>{L(r),r||b.setFieldsValue({hoa_price:void 0,hoa_type:void 0})}})}),e.jsx("div",{className:"col-12 col-sm-6 col-md-6 col-lg-3",children:e.jsx(p,{type:"radio",name:"basement",label:"Basement",options:[{value:!0,label:"Yes"},{value:!1,label:"No"}],rules:m("basement",n.required)})}),e.jsx(z.Item,{noStyle:!0,shouldUpdate:(r,i)=>r.hoa!==i.hoa,children:({getFieldValue:r})=>r("hoa")===!0?e.jsx("div",{className:"col-12 col-sm-6 col-md-6",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-8",children:e.jsx(p,{name:"hoa_price",placeholder:"Enter Price ($)",label:"HOA Price",type:"number",rules:m("HOA Price",n.required,n.greaterThanOne)})}),e.jsx("div",{className:"col-4",children:e.jsx(p,{placeholder:"Select Type",label:"Type",options:[{value:"month",label:"Month"},{value:"year",label:"Year"}],name:"hoa_type",type:"select",rules:m("HOA Type",n.required)})})]})}):null})]}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12",children:e.jsx("p",{className:"font-18 mt-4",children:"Property Description"})}),e.jsx("div",{className:"col-12",children:e.jsx(p,{name:"description",placeholder:"Enter detailed property description...",label:"Description",type:"textarea",rows:5,rules:m("description",n.required)})})]}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12",children:e.jsx("p",{className:"font-18 mt-4",children:"Property Images"})}),e.jsxs("div",{className:"col-12 mt-4 mb-5",children:[e.jsx("div",{className:"form-item-label",children:e.jsx("label",{children:"Upload Images"})}),e.jsx(be,{onChange:le,multiple:!1,maxCount:15,value:j,type:"property_image"})]})]}),e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12 mt-3 mb-3 text-end",children:[e.jsx(D,{title:"Cancel",className:"gray-btn",onClick:()=>w(-1),disabled:C}),e.jsx(D,{title:C?"Saving...":o?"Update":"Save",className:"blue-btn ms-3",htmlType:"submit",disabled:C})]})})]})})]})})})};export{ze as default};
