import InnerLayout from "../../components/shared/layout/innerlayout";
import ContentBlock from "../../components/partial/contentblock/contentblock";


const Privacy = () => {
  const privacyData = [
    "What is a privacy policy?",
    "A privacy policy is a legal document where you disclose what data you collect from users...",
    "Is the privacy policy generator free to use?",
    "Why is a privacy policy important?",
    "Where do I put my privacy policy?",
  ];

  return (
    <InnerLayout>
      <div className="container-fluid mt-4">
        <ContentBlock title="Privacy Policy" content={privacyData} />
      </div>
    </InnerLayout>
  );
};

export default Privacy;
