const e=(r,i=[])=>{if(!r)return[];if(Array.isArray(r)&&r.length>0&&typeof r[0]=="number")return r;if(Array.isArray(r)&&r.length>0&&r[0].id)return r.map(t=>t.id);if(Array.isArray(r))return r.map(t=>{if(typeof t=="object"&&t.name){const f=i.find(n=>n.name===t.name);return f?f.id:t.name}if(typeof t=="object"&&t.id)return t.id;const o=i.find(f=>f.name===t);return o?o.id:t});if(typeof r=="string")try{const t=JSON.parse(r);return e(t,i)}catch{return r.split(",").map(o=>{const f=i.find(n=>n.name===o.trim());return f?f.id:o.trim()})}if(typeof r=="object"&&r.id)return[r.id];if(typeof r=="object"&&r.name){const t=i.find(o=>o.name===r.name);return t?[t.id]:[r.name]}return[]},d=r=>{if(!Array.isArray(r))return{};const i={};return r.forEach((t,o)=>{i[`languages[${o}]`]=t}),i},m=r=>!r||!Array.isArray(r)?[]:r.map(i=>({value:i.id,label:i.name}));export{m as g,e as p,d as t};
