import { useState, useMemo } from 'react';
import { useQuery } from './useQuery';

/**
 * Enhanced pagination hook that handles pagination from response headers
 * and provides proper loading states for UI
 */
export function usePaginatedData(endpoint, options = {}) {
  const [page, setPage] = useState(options.initialPage || 1);
  const [pageSize, setPageSize] = useState(options.initialPageSize || 10);

  // Add pagination params
  const params = {
    ...(options.params || {}),
    page,
    limit: pageSize,
  };

  // Use the base query hook with pagination params
  const query = useQuery(endpoint, {
    ...options,
    params,
    // Keep previous data while loading new page for better UX
    placeholderData: (previousData) => previousData,
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    // Optimize for pagination performance
    select: (response) => {
      // Extract pagination info from response headers or data
      const data = response?.data || response;
      const pagination = response?.pagination || response?.meta || {};
      
      // Log pagination data for debugging
      if (process.env.NODE_ENV === 'development') {
        console.log('Pagination data:', {
          currentPage: page,
          pageSize,
          totalItems: pagination.total,
          totalPages: pagination.totalPages || Math.ceil(pagination.total / pageSize),
          hasNextPage: pagination.hasNextPage,
          hasPrevPage: pagination.hasPrevPage,
        });
      }

      return {
        data: Array.isArray(data) ? data : data?.items || data?.results || [],
        pagination: {
          current: page,
          pageSize,
          total: pagination.total || 0,
          totalPages: pagination.totalPages || Math.ceil((pagination.total || 0) / pageSize),
          hasNextPage: pagination.hasNextPage || page < Math.ceil((pagination.total || 0) / pageSize),
          hasPrevPage: pagination.hasPrevPage || page > 1,
          ...pagination,
        },
      };
    },
    notifyOnChangeProps: ["data", "error", "isLoading", "isFetching"],
  });

  // Pagination helpers
  const paginationHelpers = useMemo(() => ({
    // Current page info
    page,
    pageSize,
    
    // Setters
    setPage: (newPage) => {
      console.log(`Changing page from ${page} to ${newPage}`);
      setPage(newPage);
    },
    setPageSize: (newPageSize) => {
      console.log(`Changing page size from ${pageSize} to ${newPageSize}`);
      setPageSize(newPageSize);
      setPage(1); // Reset to first page when changing page size
    },
    
    // Navigation helpers
    nextPage: () => {
      const nextPageNum = page + 1;
      const maxPage = query.data?.pagination?.totalPages || 1;
      if (nextPageNum <= maxPage) {
        console.log(`Going to next page: ${nextPageNum}`);
        setPage(nextPageNum);
      }
    },
    
    prevPage: () => {
      if (page > 1) {
        const prevPageNum = page - 1;
        console.log(`Going to previous page: ${prevPageNum}`);
        setPage(prevPageNum);
      }
    },
    
    goToPage: (newPage) => {
      const maxPage = query.data?.pagination?.totalPages || 1;
      if (newPage >= 1 && newPage <= maxPage) {
        console.log(`Going to page: ${newPage}`);
        setPage(newPage);
      }
    },
    
    // Reset pagination
    reset: () => {
      console.log('Resetting pagination to page 1');
      setPage(1);
    },
  }), [page, pageSize, query.data?.pagination?.totalPages]);

  return {
    // Data
    data: query.data?.data || [],
    pagination: query.data?.pagination || {
      current: page,
      pageSize,
      total: 0,
      totalPages: 0,
      hasNextPage: false,
      hasPrevPage: false,
    },
    
    // Loading states
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    isError: query.isError,
    error: query.error,
    
    // Pagination helpers
    ...paginationHelpers,
    
    // Query methods
    refetch: query.refetch,
    invalidate: query.invalidate,
  };
}

/**
 * Hook specifically for users with pagination
 */
export function usePaginatedUsers(options = {}) {
  return usePaginatedData('getUser', {
    ...options,
    initialPageSize: options.pageSize || 20,
  });
}

/**
 * Hook specifically for posts with pagination
 */
export function usePaginatedPosts(options = {}) {
  return usePaginatedData('postItem', {
    ...options,
    initialPageSize: options.pageSize || 10,
  });
}

export default usePaginatedData;
