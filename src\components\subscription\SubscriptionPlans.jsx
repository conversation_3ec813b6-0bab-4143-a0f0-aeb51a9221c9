import React, { useState } from 'react';
import { Card, Button, Row, Col, Spin, Alert, Radio, Typography } from 'antd';
import { CheckOutlined } from '@ant-design/icons';
import { useSubscriptionProducts } from '../../hooks/reactQuery/useSubscriptionProducts';
import StripePaymentForm from '../payment/StripePaymentForm';

const { Title, Text } = Typography;

/**
 * Subscription Plans Component
 * Displays dynamic subscription products from API
 * Integrates with Stripe payment form
 */
const SubscriptionPlans = ({ onSubscriptionSuccess }) => {
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [showPaymentForm, setShowPaymentForm] = useState(false);

  // Fetch subscription products
  const { 
    data: products, 
    isLoading, 
    isError, 
    error, 
    refetch 
  } = useSubscriptionProducts();

  const handlePlanSelect = (plan) => {
    setSelectedPlan(plan);
    setShowPaymentForm(true);
  };

  const handlePaymentSuccess = (data) => {
    console.log('Payment successful:', data);
    setShowPaymentForm(false);
    setSelectedPlan(null);
    onSubscriptionSuccess?.(data);
  };

  const handlePaymentError = (error) => {
    console.error('Payment failed:', error);
    // Keep payment form open for retry
  };

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" tip="Loading subscription plans..." />
      </div>
    );
  }

  if (isError) {
    return (
      <Alert
        message="Error Loading Plans"
        description={error?.message || 'Failed to load subscription plans'}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={() => refetch()}>
            Retry
          </Button>
        }
      />
    );
  }

  if (!products || products.length === 0) {
    return (
      <Alert
        message="No Plans Available"
        description="No subscription plans are currently available."
        type="info"
        showIcon
      />
    );
  }

  // Show payment form if plan is selected
  if (showPaymentForm && selectedPlan) {
    return (
      <div>
        <div style={{ textAlign: 'center', marginBottom: 20 }}>
          <Button 
            type="link" 
            onClick={() => setShowPaymentForm(false)}
          >
            ← Back to Plans
          </Button>
        </div>
        
        <StripePaymentForm
          selectedPlan={selectedPlan}
          onSuccess={handlePaymentSuccess}
          onError={handlePaymentError}
        />
      </div>
    );
  }

  return (
    <div style={{ maxWidth: 1200, margin: '0 auto', padding: '20px' }}>
      <div style={{ textAlign: 'center', marginBottom: 40 }}>
        <Title level={2}>Choose Your Subscription Plan</Title>
        <Text type="secondary">
          Select the plan that best fits your needs
        </Text>
      </div>

      <Row gutter={[24, 24]} justify="center">
        {products.map((plan) => (
          <Col xs={24} sm={12} lg={8} key={plan.id}>
            <Card
              hoverable
              style={{
                textAlign: 'center',
                height: '100%',
                border: selectedPlan?.id === plan.id ? '2px solid #1890ff' : '1px solid #d9d9d9',
              }}
              actions={[
                <Button
                  type="primary"
                  size="large"
                  onClick={() => handlePlanSelect(plan)}
                  style={{ width: '80%' }}
                >
                  Select Plan
                </Button>
              ]}
            >
              <div style={{ padding: '20px 0' }}>
                <Title level={3} style={{ marginBottom: 8 }}>
                  {plan.name}
                </Title>
                
                {plan.description && (
                  <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
                    {plan.description}
                  </Text>
                )}
                
                <div style={{ marginBottom: 20 }}>
                  <Title level={1} style={{ color: '#1890ff', marginBottom: 0 }}>
                    ${plan.price}
                  </Title>
                  <Text type="secondary">
                    /{plan.period || 'month'}
                  </Text>
                </div>

                {/* Plan Features (if available) */}
                {plan.features && plan.features.length > 0 && (
                  <div style={{ textAlign: 'left' }}>
                    {plan.features.map((feature, index) => (
                      <div key={index} style={{ marginBottom: 8 }}>
                        <CheckOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                        <Text>{feature}</Text>
                      </div>
                    ))}
                  </div>
                )}

                {/* Default features if none provided */}
                {(!plan.features || plan.features.length === 0) && (
                  <div style={{ textAlign: 'left' }}>
                    <div style={{ marginBottom: 8 }}>
                      <CheckOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                      <Text>Full access to platform</Text>
                    </div>
                    <div style={{ marginBottom: 8 }}>
                      <CheckOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                      <Text>Premium support</Text>
                    </div>
                    <div style={{ marginBottom: 8 }}>
                      <CheckOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                      <Text>Advanced features</Text>
                    </div>
                  </div>
                )}
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Plan Selection Radio (Alternative UI) */}
      {products.length > 0 && (
        <div style={{ marginTop: 40, textAlign: 'center' }}>
          <Title level={4}>Or select from list:</Title>
          <Radio.Group
            value={selectedPlan?.id}
            onChange={(e) => {
              const plan = products.find(p => p.id === e.target.value);
              setSelectedPlan(plan);
            }}
            style={{ marginBottom: 20 }}
          >
            {products.map((plan) => (
              <Radio.Button key={plan.id} value={plan.id} style={{ margin: '4px' }}>
                {plan.name} - ${plan.price}
              </Radio.Button>
            ))}
          </Radio.Group>
          
          {selectedPlan && (
            <div>
              <Button
                type="primary"
                size="large"
                onClick={() => setShowPaymentForm(true)}
              >
                Proceed to Payment
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SubscriptionPlans;
