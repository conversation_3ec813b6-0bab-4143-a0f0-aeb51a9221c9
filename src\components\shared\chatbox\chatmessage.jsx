import React from 'react';

const ChatMessage = () => {
    return (
        <>
            <div className="row">
                <div className="col-12 col-sm-12 col-md-11 col-lg-10 col-xl-8 col-xxl-6">
                    <div className='d-flex align-items-center'>
                        <div className='assign-user-avatar avatar-blue'>
                            <p>HM</p>
                        </div>
                        <div>
                            <p className='font-16 color-black font-600'><PERSON></p>
                        </div>
                    </div>
                    <div className='d-flex'>
                        <span className='user-first-text'>Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus.</span>
                        <span className='text-time ms-2'>5/23/24</span>
                    </div>
                    <div className='d-flex'>
                        <span className='user-first-text mt-3'>Aenean vulputate eleifend tellus. <PERSON><PERSON><PERSON> leo ligula, porttitor eu, consequat vitae, eleifend ac, enim. Aliquam lorem ante, dapibus in, viverra quis, feugiat a, tellus. Phasellus viverra nulla ut metus varius laoreet. Quisque rutrum. Aenean imperdiet. Etiam ultricies nisi vel augue. </span>
                        <span className='text-time ms-2'>5/23/24</span>
                    </div>
                </div>
            </div>
            <div className="row ">
                <div className="col-12 col-sm-12 col-md-11 offset-md-1 col-lg-10 offset-lg-2 col-xl-8 offset-xl-4 col-xxl-6 offset-xxl-6">
                    <div className='d-flex mt-4'>
                        <span className='text-time me-2'>5/23/24</span>
                        <span className='user-first-text'>urabitur ullamcorper ultricies nisi. Nam eget dui. Etiam rhoncus. Maecenas tempus, tellus eget condimentum rhoncus, sem quam semper libero, sit amet adipiscing sem neque sed ipsum.</span>
                    </div>
                </div>
            </div>
        </>
    );
};

export default ChatMessage;