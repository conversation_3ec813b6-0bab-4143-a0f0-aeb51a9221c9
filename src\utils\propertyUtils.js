/**
 * Truncate text to specified number of lines
 */
export const truncateToLines = (text, maxLines = 2, wordsPerLine = 8) => {
  if (!text) return "No description available";

  const words = text.split(" ");
  const maxWords = maxLines * wordsPerLine;

  if (words.length <= maxWords) {
    return text;
  }

  return words.slice(0, maxWords).join(" ") + "...";
};

/**
 * Helper function to remove decimal places from numbers
 */
const formatNumber = (value) => {
  if (!value) return value;
  const num = parseFloat(value);
  return isNaN(num) ? value : Math.floor(num);
};

/**
 * Transform server property data to match PropertyCard component props
 */
export const transformPropertyData = (property, options = {}) => {
  if (!property) return null;

  const { showActions = false, onEdit = null, onDelete = null } = options;

  // Check if the property belongs to the current logged-in user
  const currentUser = window.user || {};
  const isOwner =
    property.user_id === currentUser.id ||
    property.user?.id === currentUser.id ||
    property.created_by === currentUser.id;

  // Only show actions if the user owns this property
  const shouldShowActions = showActions && isOwner;

  // Get the first image or use placeholder
  const firstImage =
    property.images && property.images.length > 0
      ? property.images[0].url
      : "/assets/img/property-placeholder.png";

  // Truncate description to 2 lines
  const truncatedDescription = truncateToLines(property.description, 2, 8);

  return {
    id: property.id,
    slug: property.slug,
    title:
      property.name ||
      `${formatNumber(property.bed)} Bed ${formatNumber(property.bath)} Bath ${
        property.type?.name || "Property"
      }`,
    location: `${property.city}, ${property.state}`,
    price: `${formatNumber(property.price).toLocaleString()}`,
    detail: truncatedDescription,
    bath_tub: formatNumber(property.bath),
    bed_room: formatNumber(property.bed),
    square_feet: `${formatNumber(property.size)} ${
      property.size_type || "sqft"
    }`,
    src: `/listing/detail/${property.id}`,
    image: firstImage,
    // Additional data that might be useful
    garage: formatNumber(property.garage),
    year_built: formatNumber(property.year_built),
    basement: property.basement,
    hoa: property.hoa
      ? property.hoa_price
        ? formatNumber(property.hoa_price)
        : true
      : false,
    mls_id: property.mls_id,
    property_type: property.type?.name,
    home_style: property.home_style?.name,
    agent: property.user?.name,
    agent_phone: property.mobile_no,
    is_favorite: property.is_favorite,
    // Action handlers - only show for owned properties
    showActions: shouldShowActions,
    onEdit: shouldShowActions ? onEdit : null,
    onDelete: shouldShowActions ? onDelete : null,
    // Debug info
    isOwner,
    currentUserId: currentUser.id,
    propertyUserId:
      property.user_id || property.user?.id || property.created_by,
  };
};

/**
 * Transform array of server properties
 */
export const transformPropertiesData = (properties, options = {}) => {
  if (!Array.isArray(properties)) return [];
  return properties
    .map((property) => transformPropertyData(property, options))
    .filter(Boolean);
};

export default {
  transformPropertyData,
  transformPropertiesData,
  truncateToLines,
};
