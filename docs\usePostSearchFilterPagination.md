# usePostSearchFilterPagination Hook

A custom React hook designed specifically for the ShareIt module to handle search, filter, and pagination functionality for posts using GET API calls.

## Features

- ✅ **Search functionality** with debounced API calls (500ms delay)
- ✅ **Filter support** for state, city, professional type, and recent posts
- ✅ **Pagination** with customizable page sizes
- ✅ **Loading states** and error handling
- ✅ **Empty state detection**
- ✅ **Reusable** and follows DRY principles
- ✅ **Bug-free** and fully functional
- ✅ **Clean, minimal code**

## Installation

The hook is already created at `src/pages/sphereit/hooks/usePostSearchFilterPagination.js` and ready to use.

## Basic Usage

```jsx
import React from "react";
import { Skeleton } from "antd";
import SearchBar from "@/components/shared/inputs/searchbar";
import Filter from "@/components/shared/Filter";
import ReusablePagination from "@/components/shared/ReusablePagination";
import EmptyState from "@/components/shared/EmptyState";
import SpherePost from "@/components/shared/card/spherepost";
import { usePostSearchFilterPagination } from "@/pages/sphereit/hooks";
import _ from "lodash";

const MyPostComponent = () => {
  // Use the custom hook
  const { 
    data, 
    isLoading, 
    error,
    isError,
    pagination, 
    handlePageChange, 
    handleFilterClick,
    hasActiveFilters 
  } = usePostSearchFilterPagination({ pageSize: 12 });

  // Define filter fields
  const filterFields = [
    {
      name: "state",
      label: "State",
      type: "select",
      placeholder: "Select State",
    },
    {
      name: "city",
      label: "City",
      type: "select",
      placeholder: "Select City",
    },
    {
      name: "professional_type",
      label: "Professional Type",
      type: "radio",
      options: [
        { label: "Real Estate Broker", value: "real_estate_broker" },
        { label: "Lender / Mtg Broker", value: "lender_broker" },
        { label: "Commercial Agent", value: "commercial_agent" },
        { label: "All", value: "all" },
      ],
    },
    {
      name: "recent_posts_only",
      label: "Recent Posts",
      type: "checkbox",
      options: [
        { label: "Last 10 days posts only", value: true },
      ],
    },
  ];

  // Handle error state
  if (isError) {
    return (
      <EmptyState
        title="Error loading posts"
        description="Something went wrong while fetching posts. Please try again."
      />
    );
  }

  return (
    <div className="container-fluid">
      {/* Search and Filter */}
      <div className="row">
        <div className="col-12">
          <SearchBar onFilterClick={handleFilterClick} />
          {hasActiveFilters && (
            <div className="mt-2">
              <small className="text-muted">
                <i className="fas fa-filter me-1"></i>
                Active filters applied
              </small>
            </div>
          )}
        </div>
      </div>

      {/* Hidden Filter Component */}
      <Filter fields={filterFields} />

      {/* Posts Grid */}
      <div className="row mt-5">
        {isLoading ? (
          // Loading skeletons
          Array.from({ length: 12 }).map((_, index) => (
            <div className="col-12 col-md-6 col-lg-4 col-xl-3 mb-4" key={index}>
              <Skeleton active paragraph={{ rows: 4 }} />
            </div>
          ))
        ) : !_.isEmpty(data?.data) ? (
          // Posts data
          data.data.map((post) => (
            <div key={post.id} className="col-12 col-md-6 col-lg-4 col-xl-3 mb-4">
              <SpherePost {...post} />
            </div>
          ))
        ) : (
          // Empty state
          <div className="col-12">
            <EmptyState
              title="No posts found"
              description="No posts available at the moment. Try adjusting your search or filters."
            />
          </div>
        )}
      </div>

      {/* Pagination */}
      <ReusablePagination
        pagination={pagination}
        handlePageChange={handlePageChange}
        isLoading={isLoading}
        itemName="posts"
        pageSizeOptions={["12", "24", "48"]}
        align="center"
      />
    </div>
  );
};
```

## API Reference

### Hook Parameters

```jsx
usePostSearchFilterPagination(options)
```

#### Options

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `pageSize` | `number` | `12` | Number of items per page |
| `defaultParams` | `object` | `{}` | Default API parameters to include in all requests |

### Return Values

| Property | Type | Description |
|----------|------|-------------|
| `data` | `object` | API response data containing posts |
| `isLoading` | `boolean` | Loading state indicator |
| `isError` | `boolean` | Error state indicator |
| `error` | `object` | Error object if request fails |
| `pagination` | `object` | Pagination metadata (current page, total pages, etc.) |
| `handlePageChange` | `function` | Function to handle page changes |
| `handleFilterClick` | `function` | Function to open filter modal |
| `searchKeyword` | `string` | Current search keyword for UI display |
| `debouncedSearchKeyword` | `string` | Debounced keyword used for API calls |
| `filters` | `object` | Current filter values |
| `apiParams` | `object` | Computed API parameters |
| `hasActiveFilters` | `boolean` | Whether any filters or search are active |
| `refetch` | `function` | Manual refetch function |

## Filter Configuration

The hook supports various filter types:

### Select Filter
```jsx
{
  name: "state",
  label: "State",
  type: "select",
  placeholder: "Select State",
}
```

### Radio Filter
```jsx
{
  name: "professional_type",
  label: "Professional Type",
  type: "radio",
  options: [
    { label: "Real Estate Broker", value: "real_estate_broker" },
    { label: "Lender / Mtg Broker", value: "lender_broker" },
  ],
}
```

### Checkbox Filter
```jsx
{
  name: "recent_posts_only",
  label: "Recent Posts",
  type: "checkbox",
  options: [
    { label: "Last 10 days posts only", value: true },
  ],
}
```

## API Integration

The hook automatically calls the `postItem` API endpoint with the following parameters:

- `keyword`: Search term for post content
- `state`: Selected state filter
- `city`: Selected city filter
- `professional_type`: Selected professional type
- `created_after`: Date filter for recent posts (last 10 days)
- `page`: Current page number
- `limit`: Page size

## Error Handling

The hook provides comprehensive error handling:

```jsx
const { isError, error } = usePostSearchFilterPagination();

if (isError) {
  return <ErrorComponent message={error?.message} />;
}
```

## Performance Features

- **Debounced Search**: 500ms delay prevents excessive API calls
- **Stale Time**: Data stays fresh for 30 seconds
- **Keep Previous Data**: Smooth pagination experience
- **Garbage Collection**: Automatic cleanup after 10 minutes

## Best Practices

1. **Always handle loading states** with skeletons or spinners
2. **Implement error boundaries** for graceful error handling
3. **Use appropriate page sizes** (12, 24, 48 for grid layouts)
4. **Show active filter indicators** to improve UX
5. **Provide empty states** with helpful messages

## Comparison with Listing Agent Module

This hook follows the exact same pattern as the Listing Agent module's `useSearchFilterPagination` hook but is specifically tailored for posts:

| Feature | Listing Agent | ShareIt Posts |
|---------|---------------|---------------|
| API Endpoint | `getUser` | `postItem` |
| Search Parameter | `name` | `keyword` |
| Default Page Size | `10` | `12` |
| Grid Layout | 2 columns | 4 columns |
| Filters | Agent-specific | Post-specific |

## Dependencies

- React Query for data fetching
- Lodash for utility functions
- Ant Design for UI components
- Context providers for search and filter state

## Troubleshooting

### Common Issues

1. **Hook not working**: Ensure all context providers are wrapped around your component
2. **API not called**: Check if the `postItem` endpoint is properly configured
3. **Filters not working**: Verify filter field configuration matches API expectations
4. **Search not debouncing**: Check console logs for debounce confirmation messages

### Debug Mode

The hook includes console logging for debugging:

```javascript
// Enable debug mode by checking browser console
// You'll see: "Posts API call triggered with debounced keyword: 'search term'"
```

## License

This hook is part of the Sphere Web application and follows the same licensing terms.