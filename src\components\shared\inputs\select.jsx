import React from "react";
import { Form, Select } from "antd";

const SelectInput = ({
  name,
  rules,
  placeholder,
  handlechange,
  options,
  style,
  loading,
  label,
  defaultValue,
  suffix,
  prefix,
  mode, // Add mode prop
  className,
  showSearch = false,
  disabled,
}) => {
  // Filter option function for search
  const filterOption = (input, option) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());

  return (
    <Form.Item
      name={name}
      rules={rules}
      label={label}
      validateTrigger="onBlur"
      defaultValue={defaultValue}
    >
      <Select
        size="large"
        placeholder={placeholder}
        disabled={disabled}
        onChange={handlechange}
        style={style}
        loading={loading}
        options={options}
        suffix={suffix}
        prefix={prefix}
        mode={mode} // Add mode prop to Select
        showSearch={showSearch}
        filterOption={showSearch ? filterOption : false}
        className={className}
      />
    </Form.Item>
  );
};

export default SelectInput;
