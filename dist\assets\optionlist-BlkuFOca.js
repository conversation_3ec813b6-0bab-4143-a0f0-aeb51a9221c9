import{j as o,L as e}from"./index-3mE9H3a0.js";const l=({options:s})=>o.jsx("div",{className:"p-3",children:s.map((r,t)=>r.link?o.jsx(e,{to:r.link,className:"d-block text-dark text-decoration-none",children:o.jsx("div",{className:"py-3 font-18 border-bottom",style:{cursor:"pointer"},children:r.label})},t):o.jsx("div",{className:"py-3 font-18 border-bottom text-dark",style:{cursor:"pointer"},onClick:r.action,children:r.label},t))});export{l as O};
