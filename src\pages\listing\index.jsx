import React, { useState } from "react";
import InnerLayout from "@/components/shared/layout/innerlayout";
import PropertyCard from "@/components/shared/card/propertycard";
import SearchBar from "@/components/shared/inputs/searchbar";
import IconButton from "@/components/shared/button/iconbutton";
import { useNavigate } from "react-router-dom";
import CustomModal from "@/components/shared/modal";
import { Form, Radio, Skeleton } from "antd";
import BaseInput from "@/components/shared/inputs";
import FlatButton from "@/components/shared/button/flatbutton";
import Filter from "@/components/shared/Filter";
import ReusablePagination from "@/components/shared/ReusablePagination";
import EmptyState from "@/components/shared/EmptyState";
import useSearchFilterPagination from "@/hooks/useSearchFilterPagination";
import { transformPropertiesData } from "@/utils/propertyUtils";
import { useMutation } from "@/hooks/reactQuery";
import useStartupData from "@/hooks/reactQuery/useStartupData";
import useSweetAlert from "@/hooks/useSweetAlert";
import _ from "lodash";

const Listing = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const navigate = useNavigate();
  const { showAlert } = useSweetAlert();

  // Use the same search, filter, and pagination logic as agents
  const {
    data,
    isLoading,
    pagination,
    handlePageChange,
    handleFilterClick,
    refetch,
  } = useSearchFilterPagination("properties", {
    pageSize: 12,
  });

  // Get startup data for filter options (same as Add Listing)
  const { data: startupResponse, loading: startupLoading } = useStartupData();
  const startupData = startupResponse?.data;

  // Delete property mutation
  const { mutate: deleteProperty, isPending: isDeleting } = useMutation(
    "deleteProperty",
    {
      showSuccessNotification: true,
      invalidateQueries: [
        { queryKey: ["properties"] },
        { queryKey: ["getProperty"] },
        { queryKey: ["getUser"] },
      ],
    }
  );

  // Handle edit property
  const handleEditProperty = (property) => {
    navigate(`/listing/add/${property.id}`);
  };

  // Handle delete property with confirmation
  const handleDeleteProperty = async (property) => {
    const result = await showAlert({
      title: "Are you sure?",
      text: `Do you want to delete this property`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
    });

    if (result.isConfirmed) {
      deleteProperty({ slug: property.id, data: "" });
    }
  };

  // Transform server data to match PropertyCard props with actions
  const properties = React.useMemo(() => {
    if (!data?.data) return [];
    return transformPropertiesData(data.data, {
      showActions: true,
      onEdit: handleEditProperty,
      onDelete: handleDeleteProperty,
    });
  }, [data]);

  // Generate options for bed and bath (1-10) - same as Add Listing
  const bedBathOptions = Array.from({ length: 10 }, (_, i) => ({
    value: i + 1,
    label: i + 1,
  }));

  // Generate options for garage (1-10) - same as Add Listing
  const garageOptions = Array.from({ length: 10 }, (_, i) => ({
    value: i + 1,
    label: `${i + 1} Car${i + 1 > 1 ? "s" : ""}`,
  }));

  // Size type options - same as Add Listing
  const sizeTypeOptions = [
    { value: "sqft", label: "Sq.Ft" },
    { value: "sq.m", label: "Sq.M" },
    { value: "sq.yd", label: "Sq.Yd" },
    { value: "acres", label: "Acres" },
  ];

  // Filter fields configuration - ordered exactly as specified in requirements
  // Removed min fields from UI, updated labels for max fields
  const filterFields = [
    // 1. Property Type
    {
      name: "type_id",
      label: "Property Type",
      type: "select",
      placeholder: "Select Property Type",
      options: startupData?.propertyTypes?.map((type) => ({
        value: type.id,
        label: type.name,
      })),
      loading: startupLoading,
    },
    // 2. Home Style
    {
      name: "home_style_id",
      label: "Home Style",
      type: "select",
      placeholder: "Select Home Style",
      options: startupData?.homeStyles?.map((style) => ({
        value: style.id,
        label: style.name,
      })),
      loading: startupLoading,
    },
    // 3. State
    {
      name: "state",
      label: "State",
      type: "select",
      placeholder: "Select State",
    },
    // 4. City
    {
      name: "city",
      label: "City",
      type: "select",
      placeholder: "Select City",
    },
    // 5. Zip Code
    {
      name: "zip",
      label: "Zip Code",
      type: "input",
      placeholder: "Enter Zip Code",
    },
    // 6. Basement
    {
      name: "basement",
      label: "Basement",
      type: "radio",
      options: [
        { value: true, label: "Yes" },
        { value: false, label: "No" },
      ],
    },
    // 7. Bedrooms (Max only - Min removed from UI but sent as 0 in payload)
    {
      name: "max_bed",
      label: "Bed",
      type: "select",
      placeholder: "Select Bedrooms",
      options: bedBathOptions,
    },
    // 8. Bathrooms (Max only - Min removed from UI but sent as 0 in payload)
    {
      name: "max_bath",
      label: "Bath",
      type: "select",
      placeholder: "Select Bathrooms",
      options: bedBathOptions,
    },
    // 9. Garage (Max only - Min removed from UI but sent as 0 in payload)
    {
      name: "max_garage",
      label: "Garage",
      type: "select",
      placeholder: "Select Garage",
      options: garageOptions,
    },
    // 10. Size Type
    {
      name: "size_type",
      label: "Size Type",
      type: "select",
      placeholder: "Select Size Type",
      options: sizeTypeOptions,
    },
    // Note: Property Size, Lot Size, Price, and Year Built are handled by sliders in Filter component
  ];

  return (
    <InnerLayout>
      <div className="container-fluid">
        <div className="row">
          <div className="col-12">
            <SearchBar onFilterClick={handleFilterClick} />
          </div>
          <div className="col-12 mt-3">
            <IconButton
              icon={<img src="/assets/img/addlist-icon.png" />}
              title="Add Listing"
              className="blue-btn"
              onClick={() => navigate("/listing/add")}
            />
          </div>
        </div>

        {/* Filter Component with updated fields and sliders */}
        <Filter fields={filterFields} />

        <div className="row mt-3">
          {/* Show skeletons when loading */}
          {isLoading ? (
            Array.from({ length: 12 }).map((_, index) => (
              <div key={index} className="col-12 col-sm-6 col-md-4 col-lg-3">
                <Skeleton active paragraph={{ rows: 4 }} />
              </div>
            ))
          ) : !_.isEmpty(properties) ? (
            properties.map((item, index) => (
              <div
                key={item.id || index}
                className="col-12 col-sm-6 col-md-4 col-lg-3"
              >
                <PropertyCard {...item} />
              </div>
            ))
          ) : (
            <EmptyState
              title="No properties found"
              description="No properties available at the moment"
            />
          )}
        </div>

        {/* Reusable Pagination */}
        <ReusablePagination
          pagination={pagination}
          handlePageChange={handlePageChange}
          isLoading={isLoading}
          itemName="properties"
          pageSizeOptions={["12", "24", "48"]}
        />
      </div>
    </InnerLayout>
  );
};

export default Listing;
