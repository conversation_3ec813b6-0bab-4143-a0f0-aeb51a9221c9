import React, { useState } from "react";
import {
  PictureOutlined,
  VideoCameraOutlined,
  CloseOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import FlatButton from "@/components/shared/button/flatbutton";
import { Input, Upload, Image, Button } from "antd";

const { TextArea } = Input;

const PostInput = ({
  placeholder = "What's on your mind?",
  button, // Custom button for other use cases (like chat)
  // Post creation props (when used in ShareIt)
  postCreationHook, // Hook containing all post creation logic
  onCancelEdit, // Callback for canceling edit mode
}) => {
  // Use post creation hook if provided (ShareIt mode)
  const {
    content,
    setContent,
    selectedFile,
    filePreview,
    fileType,
    isCreatingPost,
    isFormValid,
    isEditMode,
    handleImageUpload,
    handleVideoUpload,
    removeFile,
    handleSubmit,
  } = postCreationHook || {};

  // Local state for non-post creation mode
  const [localContent, setLocalContent] = useState("");

  // Determine which content to use
  const currentContent = postCreationHook ? content : localContent;
  const setCurrentContent = postCreationHook ? setContent : setLocalContent;

  return (
    <div
      style={{
        maxWidth: "100%",
        border: "1px solid #ddd",
        borderRadius: "10px",
      }}
      className="mt-4"
    >
      <div
        style={{
          backgroundColor: "#fff",
          padding: "10px",
          borderRadius: "10px",
        }}
      >
        <TextArea
          value={currentContent}
          onChange={(e) => setCurrentContent(e.target.value)}
          placeholder={placeholder}
          autoSize={{ minRows: 3, maxRows: 5 }}
          style={{
            border: "none",
            resize: "none",
            marginBottom: "10px",
            background: "transparent",
          }}
          disabled={isCreatingPost}
        />

        {/* File Preview - only show in post creation mode */}
        {postCreationHook && filePreview && (
          <div className="mb-3 position-relative" style={{ maxWidth: "200px" }}>
            {fileType === "image" ? (
              <Image
                width={200}
                height={150}
                src={filePreview}
                style={{ objectFit: "cover", borderRadius: "8px" }}
                preview={false}
              />
            ) : (
              <video
                width={200}
                height={150}
                controls
                style={{ borderRadius: "8px", objectFit: "cover" }}
              >
                <source src={filePreview} type={selectedFile?.type} />
                Your browser does not support the video tag.
              </video>
            )}
            <Button
              type="text"
              danger
              icon={<CloseOutlined />}
              size="small"
              onClick={removeFile}
              style={{
                position: "absolute",
                top: "5px",
                right: "5px",
                backgroundColor: "rgba(0,0,0,0.5)",
                color: "white",
                border: "none",
              }}
              disabled={isCreatingPost}
            />
          </div>
        )}

        <div className="d-flex justify-content-between align-items-center icon-color">
          <div className="d-flex gap-3">
            {/* Image Upload - functional only in post creation mode */}
            <Upload
              showUploadList={false}
              beforeUpload={(file) => {
                if (postCreationHook && handleImageUpload) {
                  return handleImageUpload({ file });
                }
                return false;
              }}
              accept="image/*"
              disabled={isCreatingPost || !postCreationHook}
            >
              <PictureOutlined
                style={{
                  cursor:
                    isCreatingPost || !postCreationHook
                      ? "not-allowed"
                      : "pointer",
                  fontSize: "24px",
                  color: fileType === "image" ? "#1890ff" : undefined,
                }}
                title="Upload Image"
              />
            </Upload>

            {/* Video Upload - functional only in post creation mode */}
            <Upload
              showUploadList={false}
              beforeUpload={(file) => {
                if (postCreationHook && handleVideoUpload) {
                  return handleVideoUpload({ file });
                }
                return false;
              }}
              accept="video/*"
              disabled={isCreatingPost || !postCreationHook}
            >
              <VideoCameraOutlined
                style={{
                  cursor:
                    isCreatingPost || !postCreationHook
                      ? "not-allowed"
                      : "pointer",
                  fontSize: "24px",
                  color: fileType === "video" ? "#1890ff" : undefined,
                }}
                title="Upload Video"
              />
            </Upload>
          </div>

          {button ? (
            button
          ) : (
            <div className="d-flex gap-2">
              {isEditMode && onCancelEdit && (
                <FlatButton
                  type="default"
                  title="Cancel"
                  className="px-4 post-btn px-5"
                  onClick={onCancelEdit}
                  disabled={isCreatingPost}
                />
              )}
              <FlatButton
                type="primary"
                title={
                  isCreatingPost
                    ? isEditMode
                      ? "Updating..."
                      : "Posting..."
                    : isEditMode
                    ? "Update"
                    : "Post"
                }
                className="post-btn px-5"
                onClick={handleSubmit}
                disabled={
                  postCreationHook
                    ? !isFormValid || isCreatingPost
                    : !currentContent.trim()
                }
                icon={isCreatingPost ? <LoadingOutlined /> : undefined}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PostInput;
