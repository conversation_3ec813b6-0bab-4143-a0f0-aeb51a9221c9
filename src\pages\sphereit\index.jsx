import React from "react";
import InnerLayout from "@/components/shared/layout/innerlayout";
import PostInput from "@/components/shared/inputs/postinput";
import SearchBar from "@/components/shared/inputs/searchbar";
import { useNavigate } from "react-router-dom";
import SpherePost from "@/components/shared/card/spherepost";
import { usePostSearchFilterPagination } from "@/hooks/usePostSearchFilterPagination";
import { usePostCreation } from "./hooks";
import { Skeleton, Empty, message } from "antd";
import ReusablePagination from "@/components/shared/ReusablePagination";
import { useMutation } from "@/hooks/reactQuery";
import useSweetAlert from "@/hooks/useSweetAlert";
import PostsFilter from "@/components/shared/PostsFilter";
import { PostFilterProvider } from "@/store/PostFilterContext";
import _ from "lodash";

const ShareItContent = () => {
  const navigate = useNavigate();
  const { showAlert } = useSweetAlert();
  const [editingPost, setEditingPost] = React.useState(null);

  const {
    data,
    isLoading,
    pagination,
    handlePageChange,
    handleFilterClick,
    hasActiveFilters,
  } = usePostSearchFilterPagination({
    pageSize: 12,
  });

  const handleEditComplete = React.useCallback(() => {
    setEditingPost(null);
  }, []);

  const postCreationHook = usePostCreation(editingPost, handleEditComplete);

  const { mutate: deletePost, isPending: isDeleting } = useMutation(
    "deletePost",
    {
      useFormData: false,
      showSuccessNotification: true,
      invalidateQueries: [{ queryKey: ["postItem"], type: "paginated" }],
    }
  );

  const posts = data?.data || [];

  // Filter fields configuration for posts
  const filterFields = [
    {
      name: "state",
      label: "State",
      type: "select",
      placeholder: "Select State",
    },
    {
      name: "city",
      label: "City",
      type: "select",
      placeholder: "Select City",
    },
    {
      name: "professional_type",
      label: "Profession Type",
      type: "radio",
    },
    {
      name: "last_10_days",
      label: "Last 10 days posts only",
      type: "checkbox",
    },
  ];

  const handleEditPost = (postId) => {
    const postToEdit = posts.find((post) => post.id === postId);
    if (postToEdit) {
      setEditingPost(postToEdit);
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handleCancelEdit = () => {
    setEditingPost(null);
    postCreationHook.resetForm();
  };

  const handleDeletePost = async (postId) => {
    const result = await showAlert({
      title: "Are you sure?",
      text: "Are you sure you want to delete this post?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
    });

    if (result.isConfirmed) {
      deletePost({ slug: postId, data: "" });
    }
  };

  const checkPostOwnership = (post) => {
    return true;
  };

  return (
    <InnerLayout>
      <div className="container-fluid">
        {/* Search and Filter Row */}
        <div className="row">
          <div className="col-12">
            <SearchBar onFilterClick={handleFilterClick} />
            <PostInput
              postCreationHook={postCreationHook}
              onCancelEdit={handleCancelEdit}
            />
          </div>
        </div>

        {/* Hidden Filter Component */}
        <PostsFilter fields={filterFields} />

        <div className="row mt-5">
          {isLoading ? (
            Array.from({ length: 12 }).map((_, index) => (
              <div
                key={index}
                className="col-12 col-md-6 col-lg-4 col-xl-3 mb-4"
              >
                <div className="card">
                  <div className="card-body">
                    <div className="d-flex align-items-center mb-3">
                      <Skeleton.Avatar size={40} />
                      <div className="ms-3 flex-grow-1">
                        <Skeleton.Input style={{ width: 120, height: 16 }} />
                        <div className="mt-1">
                          <Skeleton.Input style={{ width: 80, height: 12 }} />
                        </div>
                      </div>
                    </div>

                    <Skeleton.Image
                      active
                      className="w-100 text-center align-items-center d-flex mb-2"
                      style={{ width: "100%", height: 200, display: "block" }}
                    />

                    <Skeleton paragraph={{ rows: 2, width: ["100%", "80%"] }} />
                    <div className="d-flex justify-content-between align-items-center mt-3">
                      <div className="d-flex gap-3">
                        <Skeleton.Input style={{ width: 60, height: 20 }} />
                        <Skeleton.Input style={{ width: 60, height: 20 }} />
                        <Skeleton.Input style={{ width: 60, height: 20 }} />
                      </div>
                      <Skeleton.Input style={{ width: 40, height: 20 }} />
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : !_.isEmpty(posts) ? (
            posts.map((items) => (
              <div
                key={items.id}
                className="col-12 col-md-6 col-lg-4 col-xl-3 mb-4"
              >
                <SpherePost
                  {...items}
                  onClick={() => navigate(`/sphare-it/${items.id}`)}
                  showActions={checkPostOwnership(items)}
                  onEdit={handleEditPost}
                  onDelete={handleDeletePost}
                />
              </div>
            ))
          ) : (
            <div className="col-12 d-flex justify-content-center">
              <Empty
                description={
                  hasActiveFilters
                    ? "No posts match your search criteria"
                    : "No posts available"
                }
              />
            </div>
          )}
        </div>

        {/* Only show pagination when no filters are active */}
        {!hasActiveFilters && (
          <ReusablePagination
            pagination={pagination}
            handlePageChange={handlePageChange}
            isLoading={isLoading}
            itemName="posts"
            pageSizeOptions={["12", "24", "48", "96"]}
            align="end"
          />
        )}
      </div>
    </InnerLayout>
  );
};

const ShareIt = () => {
  return (
    <PostFilterProvider>
      <ShareItContent />
    </PostFilterProvider>
  );
};

export default ShareIt;
