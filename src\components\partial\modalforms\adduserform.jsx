import { Form, message } from "antd";
import React, { useEffect, memo } from "react";
import BaseInput from "../../shared/inputs";
import FlatButton from "../../shared/button/flatbutton";
import { useQuery, useMutation } from "../../../hooks/reactQuery";
import { create_company, create_employee } from "../../../config/rules";

const AddUserForm = ({ onCancel, refreshDataTable, editData }) => {
  const [form] = Form.useForm();

  // React Query for fetching user types with React 19 optimizations
  const { data: userTypes, isLoading: userTypesLoading } = useQuery(
    "user_type",
    {
      staleTime: 10 * 60 * 1000, // Cache for 10 minutes
      select: (data) => data?.data || [], // Transform data
    }
  );

  // React Query mutation for creating employee
  const { mutate: createEmployee, isPending: isCreating } = useMutation(
    "create_employee",
    {
      useFormData: true, // Use FormData for file uploads
      onSuccess: () => {
        message.success(
          editData
            ? "Employee updated successfully!"
            : "Employee created successfully!"
        );
        onCancel();
        form.resetFields();
        refreshDataTable();
      },
      onError: (error) => {
        message.error(
          error?.message || "Failed to save employee. Please try again."
        );
      },
      // Invalidate user-related queries after successful creation/update
      invalidateQueries: ["employees", "user_type"],
    }
  );

  // React Query mutation for updating employee
  const { mutate: updateEmployee, isPending: isUpdating } = useMutation(
    "edit_employee",
    {
      useFormData: true,
      method: "PUT", // Specify PUT method for updates
      onSuccess: () => {
        message.success("Employee updated successfully!");
        onCancel();
        form.resetFields();
        refreshDataTable();
      },
      onError: (error) => {
        message.error(
          error?.message || "Failed to update employee. Please try again."
        );
      },
      invalidateQueries: ["employees", "user_type"],
    }
  );

  // Form submission logic optimized for React Query
  const onFinish = (values) => {
    if (editData) {
      // Edit mode: Use React Query mutation with slug for ID
      updateEmployee({
        slug: editData._id,
        data: {
          ...values,
          _method: "PUT", // Laravel method spoofing
        },
      });
    } else {
      // Create mode: Use React Query mutation
      createEmployee(values);
    }
  };

  // Pre-fill form data when editing
  useEffect(() => {
    if (editData) {
      form.setFieldsValue({
        name: editData.name,
        email: editData.email,
        user_type_id: editData.user_type?._id,
      });
    } else {
      form.resetFields();
    }
  }, [editData, form]);
  return (
    <Form
      form={form}
      name="createemployee"
      layout="vertical"
      onFinish={onFinish}
      initialValues={{
        remember: true,
      }}
      form={form}
      autoComplete="off"
    >
      <div className="row">
        <div className="col-12 col-md-6">
          <BaseInput
            name="name"
            placeholder="Enter name"
            label="Employee Name"
            rules={create_employee.name}
          />
        </div>
        <div className="col-12 col-md-6">
          <BaseInput
            name="email"
            label="Email Address"
            disabled={!!editData}
            placeholder="Enter email"
            rules={create_company.email}
          />
        </div>

        <div className="col-12 col-md-6">
          <BaseInput
            type="select"
            name="user_type_id"
            placeholder="Select user type"
            label="User Type"
            options={userTypes?.map((item) => ({
              value: item._id,
              label: item.title,
            }))}
            loading={userTypesLoading}
            rules={create_employee.user_type}
          />
        </div>
        <div className="text-end mt-4">
          <FlatButton
            title={editData ? "Update" : "Save"}
            className="add-new-btn"
            htmlType="submit"
            loading={editData ? isUpdating : isCreating}
            disabled={editData ? isUpdating : isCreating}
          />
        </div>
      </div>
    </Form>
  );
};

export default memo(AddUserForm);
