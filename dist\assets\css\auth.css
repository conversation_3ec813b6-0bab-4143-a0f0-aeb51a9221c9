@font-face {
    font-family: "PublicSans-Bold";
    src: url("../font/PublicSans-Bold.ttf");
}
@font-face {
    font-family: "PublicSans-Regular";
    src: url("../font/PublicSans-Regular.ttf");
}
@font-face {
    font-family: "PublicSans-Medium";
    src: url("../font/PublicSans-Medium.ttf");
}

@font-face {
    font-family: "Inter-Medium";
    src: url("../font/inter/Inter_18pt-Bold.ttf");
}




/* @import url('https://fonts.cdnfonts.com/css/gilroy-bold'); */

body {
    margin: 0;
    color: #262F3D;
    font-size: 13px;
    position: relative;
    font-family: "PublicSans-Regular" !important;
    overflow-x: hidden;
    background-color: rgb(250, 250, 250);
}



.ant-typography,
.ant-btn,
.ant-input,
.ant-select,
.ant-menu,
.ant-form,
.ant-table,
.ant-modal,
.ant-tooltip,
.ant-tabs,
.ant-card,
.ant-dropdown,
.ant-pagination,
.ant-switch,
.ant-radio,
.ant-checkbox,
.ant-message,
.ant-alert,
.ant-breadcrumb,
.ant-avatar,
.ant-layout,
.ant-collapse,
.ant-list,
.ant-drawer,
.ant-popover ,
.ant-checkbox-label {
    font-family: "PublicSans-Regular" !important;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
ul {
    margin: 0;
    padding: 0;
}
h1,
h2,
h3,
h4,
h5,
h6{
    color: #262F3D;
    font-family: "Inter-Medium" !important;  
}


ol,
ul {
    padding: 0 !important;
    list-style: none !important;
}

p,
ul,
ol,
div,
footer,
header,
main {
    font-family: "PublicSans-Medium" !important;
}

/* total width */
::-webkit-scrollbar {
    background-color: #fff;
    width: 16px;
}

/* background of the scrollbar except button or resizer */
::-webkit-scrollbar-track {
    background-color: #fff;
}

::-webkit-scrollbar-track:hover {
    background-color: #f4f4f4;
}

/* scrollbar itself */
::-webkit-scrollbar-thumb {
    background-color: #babac0;
    border-radius: 16px;
    border: 5px solid #fff;
}

::-webkit-scrollbar-thumb:hover {
    background-color: #a0a0a5;
    border: 4px solid #f4f4f4;
}

/* set button(top and bottom of the scrollbar) */
::-webkit-scrollbar-button {
    display: none;
}



a {
    padding: 0;
    margin: 0;
    transition: all 0.5s ease;
    text-decoration: none !important;
    color: #000126 !important;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 600 !important;
   
}

address,
dl,
ol,
p,
ul {
    margin: 0 !important;
}

a:hover,
a:focus,
a:active {
    outline: none;
    box-shadow: none;
    text-decoration: none;
}
.ant-layout{
    min-height: 100vh;
}
button {
    transition: all 0.5s ease;
}

.btn-theme {
    border-radius: 12px;
    background-color: #1182f1;
    color: #ffffff;
    font-size: 14px;
    padding: 0 40px;
    overflow: hidden;
    position: relative;
    z-index: 0;
    display: block;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    border: 0;
    height: 40px;

}

.btn-theme2 {
    border-radius: 12px;
    background-color: #08ae22;
    color: #ffffff;
    font-size: 14px;
    padding: 0 40px;
    overflow: hidden;
    position: relative;
    z-index: 0;
    display: block;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    border: 0;
    height: 40px;
}

.btn-theme3 {
    border-radius: 12px;
    background-color: #ae0808;
    color: #ffffff;
    font-size: 14px;
    padding: 0 40px;
    overflow: hidden;
    position: relative;
    z-index: 0;
    display: block;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    border: 0;
    height: 40px;
}

.btn-theme::after,
.btn-theme2::after,
.btn-theme3::after {
    background-color: #111114;
    height: 100%;
    left: -35%;
    top: 0;
    transform: skew(40deg);
    transition-duration: 0.6s;
    transform-origin: top left;
    width: 0;
    position: absolute;
    content: "";
    z-index: -1;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}

.btn-theme:hover::after,
.btn-theme2:hover::after,
.btn-theme3:hover::after {
    height: 100%;
    width: 135%;
}

.btn-theme:hover,
.btn-theme2:hover,
.btn-theme3:hover {
    color: #fff !important;
}

.auth-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff !important;
}
.edit-btn{
    border-radius: 10px;
    border: solid 1px rgba(255, 255, 255, 0.55);
    background-color: rgba(255, 255, 255, 0.8);
  padding: 10px 20px;
  color: #000;
}
.container {
    width: 100% !important;
    max-width: 100%;
    padding: 0 100px;
    margin: 0 auto;
}



/* Banner Section  */

/* Feature Section */


/* Work Sec */



/* Why Chosse Us sec */



/* Footer */


/* Auth  */
* {
    padding: 0;
    margin: 0;
}
.auth {
    display: flex;
    min-height: 100vh;
    align-items: stretch;
    flex-direction: row; /* Default for larger screens */
  }
  
  

.add-new-btn{
    background-color: #28569f;
    font-size: 16px;
    color: #fff;
    width: fit-content;
    padding: 19px 40px;
    border: 1px solid transparent;
    margin-left: 20px !important;
}
.reset-btn{
    background-color: #aab6cc;
    font-size: 16px;
    color: #fff;
    width: fit-content;
    padding: 19px 40px;
    border: 1px solid transparent;
    margin-left: 20px !important;
}



.signin-btn {
    width: 100%;
    height: 45px;
    border: 0;
    background-color: #3883E2;
    border-radius: 8px;
    color: #fff;
    font-size: 16px;
    margin-top: 80px;
}
.signup-btn{
    width: fit-content;
    height: 45px;
    border: 0;
    background-color: #3883E2;
    border-radius: 8px;
    color: #fff;
    font-size: 16px;
    margin-top: 10px !important;
    padding: 0 47px;
}
.signin-btn:hover{
    background-color: #1182f1 !important;
    border-radius: 12px; 
    color: #fff;
}

.auth-box {
    width: 85%;
    margin: 0 auto;
    position: relative;
   margin-top:20px ;
   min-height: 80vh;

}




.auth-logo {
    padding-left: 44px;
    margin-top: 55px;
}

.auth-box h1 {
    color: #1b1b1f;
}

.auth-box p {
    color: #717171;
}


.sigup-text {
    text-align: center;
    position: absolute;
    bottom: 20px;
    right: 12%;
}

.auth .container {
    padding: 0;
}

.ant-form-item-label >label{
    color:#344054 !important;
}
label{
    color:#344054 !important;

}

.ant-form-item {
    margin-bottom: 0;
    margin-top: 16px;
    width: 100%;
}
.post-card .ant-form-item{
    margin-top: 0 !important;
}
.post-card {
    cursor: pointer;
}
.signup-text {
    text-align: center;
    margin-top: 20px !important;
    color: #344054 !important;
    margin-bottom: 15px !important;
 }

* {
    box-sizing: border-box;
}
.ant-input-outlined{
    border-color:#A4A9B28C !important ;
}

.ant-input {
    border-radius: 6px;
    margin: 0 !important;
    padding: 7px;
}

.ant-form-item .ant-form-item-label {
    padding-bottom: 4px !important;
}

.container-fluid{
    padding: 0 40px !important;
}
/***********************
 ** Start Sidebar Menu **
 ***********************/


.not-box{
    padding: 10px 10px 20px ;
    border-bottom: 1px solid #F1F4F9;
    margin: 30px;
}
.not-box .notifoication-avatar{
    width: 40px;
    height: 40px;
    overflow: hidden;
}
.not-box .notifoication-avatar img{
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.not-box p{
    color: #666C7E;
}

.border {
  border: 1px solid #e0e0e0;
}

.bg-white {
  background-color: #fff;
}

.fw-bold {
  font-weight: bold;
}

.my-collape .ant-collapse-item {
    margin-bottom: 25px;
    border: 0 !important;
       box-shadow: 0px 4px 20px 0px #0000001a;
}

.my-collape {
    background: transparent;
    border: 0 !important;
}


.my-collape .ant-collapse-item-active .ant-collapse-header {
    background: #EBEBEB;
    border: 0 !important;
    color: #262626;
}
.my-collape .ant-collapse-header-text
{
   color: #262626;  
   font-weight: 600;
   font-size: 18px;
}

.my-collape .ant-collapse-item-active .ant-collapse-content-active {
    border: 0;
    background: #EBEBEB;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}
.my-collape .ant-collapse-item-active .ant-collapse-content-active p{
    font-size: 14px;
    color: #606267;
}
.my-collape  .ant-collapse-expand-icon svg{
    color: #898B8E;
    font-size: 18px;
}
.PhoneInput {
    border: 1px solid #A4A9B28C !important;
    padding: 7px;
    border-radius: 6px;
}

.PhoneInput input {
    border: 0 !important;
}

/* Banner Img */

.banner-show {
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.banner-show h1 {
    font-size: 90px;
    margin-bottom: 90px;
}

.banner-show img {
    width: 310px;
}
.banner-animation {
  animation: fadeZoom 1.5s ease-in-out;
}

@keyframes fadeZoom {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
.banner-animation.zoom-out {
  animation: zoomOut 3s ease-in-out forwards;
}

.lazy-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh; /* full screen height */
  width: 100vw;  /* full screen width */
  background-color: #f9f9f9; /* soft background color */
  z-index: 9999;
  position: fixed;
  top: 0;
  left: 0;
}
input#login_mobile_no {
    outline: none;
    border: 0;
}
.ant-picker {
    width: 100%;
    height: 40px;
}