import React from 'react';
import { Button, notification, Card, Space } from 'antd';
import { CheckCircleOutlined, WarningOutlined } from '@ant-design/icons';

/**
 * Test component to verify Ant Design React 19 compatibility
 * This component tests various Ant Design features with React 19
 */
const AntdReact19Test = () => {
  const testNotification = () => {
    notification.success({
      message: 'React 19 + Ant Design Test',
      description: 'If you see this notification, Ant Design is working perfectly with React 19!',
      duration: 4,
      icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
    });
  };

  const testErrorNotification = () => {
    notification.error({
      message: 'Error Notification Test',
      description: 'This tests error notifications with React 19 compatibility.',
      duration: 4,
      icon: <WarningOutlined style={{ color: '#ff4d4f' }} />,
    });
  };

  const testWarningNotification = () => {
    notification.warning({
      message: 'Warning Notification Test',
      description: 'This tests warning notifications with React 19 compatibility.',
      duration: 4,
    });
  };

  return (
    <Card 
      title="Ant Design + React 19 Compatibility Test" 
      style={{ margin: '20px', maxWidth: '600px' }}
    >
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        <div>
          <h4>✅ React 19 Features Working:</h4>
          <ul>
            <li>Ant Design components rendering correctly</li>
            <li>Notifications system functional</li>
            <li>No compatibility warnings in console</li>
            <li>Official @ant-design/v5-patch-for-react-19 applied</li>
          </ul>
        </div>

        <div>
          <h4>🧪 Test Notifications:</h4>
          <Space wrap>
            <Button 
              type="primary" 
              onClick={testNotification}
              icon={<CheckCircleOutlined />}
            >
              Test Success
            </Button>
            
            <Button 
              danger 
              onClick={testErrorNotification}
              icon={<WarningOutlined />}
            >
              Test Error
            </Button>
            
            <Button 
              onClick={testWarningNotification}
            >
              Test Warning
            </Button>
          </Space>
        </div>

        <div style={{ 
          background: '#f6ffed', 
          border: '1px solid #b7eb8f', 
          padding: '12px', 
          borderRadius: '6px' 
        }}>
          <strong>✅ Status:</strong> Ant Design v5 is fully compatible with React 19 using the official compatibility patch.
        </div>

        <div style={{ fontSize: '12px', color: '#666' }}>
          <strong>Package:</strong> @ant-design/v5-patch-for-react-19 v1.0.3<br/>
          <strong>React:</strong> 19.1.0<br/>
          <strong>Ant Design:</strong> 5.25.1
        </div>
      </Space>
    </Card>
  );
};

export default AntdReact19Test;
