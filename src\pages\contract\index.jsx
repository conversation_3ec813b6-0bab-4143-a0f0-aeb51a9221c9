import React, { useState } from "react";
import InnerLayout from "@/components/shared/layout/innerlayout";
import { CloseOutlined } from "@ant-design/icons";
import PostInput from "@/components/shared/inputs/postinput";
import SearchBar from "@/components/shared/inputs/searchbar";
import SparePost from "@/components/partial/spareit/sparepost";
import { Checkbox, Form, Radio } from "antd";
import CustomModal from "@/components/shared/modal";
import BaseInput from "@/components/shared/inputs";
import FlatButton from "@/components/shared/button/flatbutton";

const posts = [
  {
    id: 1,
    user: {
      name: "Anonymous 1",
      avatar: "/assets/img/avatar-1.png",
      time: "12 hours ago",
    },

    body: "Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmo tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
    likes: 256,
    repost: 10,
    shares: 10,
    comments: 30,
    user_comment: [
      {
        isReply: false,
        name: "Anonymous 2",
        avatar: "/assets/img/avatar-1.png",
        time: "12 hours ago",
        mycomment:
          "Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
        commentlink: "www.loremisume.com",
        likes: 500,
        shares: 500,
      },
      {
        isReply: true,
        name: "Anonymous 3",
        avatar: "/assets/img/avatar-1.png",
        time: "10 hours ago",
        mycomment: "This is a reply to the previous comment. Great post!",
        commentlink: "www.replylink.com",
        likes: 50,
        shares: 5,
      },
      {
        isReply: false,
        name: "Anonymous 2",
        avatar: "/assets/img/avatar-1.png",
        time: "12 hours ago",
        mycomment:
          "Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
        commentlink: "www.loremisume.com",
        likes: 500,
        shares: 500,
      },
    ],

    reactions: [
      "/assets/img/avatar-1.png",
      "/assets/img/avatar-1.png",
      "/assets/img/avatar-1.png",
      "/assets/img/avatar-1.png",
      "/assets/img/avatar-1.png",
    ],
  },
];

const Contract = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const [value, setValue] = useState(null);
  const onFinish = (values) => {};
  const onRadioChange = (e) => {
    setValue(e.target.value);
  };
  const onChange = (e) => {};
  return (
    <InnerLayout>
      <div className="container-fluid">
        <div className="row">
          <div className="col-12">
            <SearchBar onClick={showModal} />
            <PostInput />
          </div>
          {posts.map((items) => (
            <div key={items.id} className="col-12 mt-4">
              <SparePost {...items} type="" />
            </div>
          ))}
        </div>
      </div>
      <CustomModal
        title="Filter"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={700}
        footer={false}
      >
        <Form
          name="login"
          layout="vertical"
          onFinish={onFinish}
          initialValues={{
            remember: true,
          }}
          autoComplete="off"
          className="add-listing"
        >
          <div className="row">
            <div className="col-12 col-sm-6 col-md-6 col-lg-6">
              <BaseInput
                name="state"
                placeholder="Select State"
                label="State"
                type="select"
              />
            </div>
            <div className="col-12 col-sm-6 col-md-6 col-lg-6">
              <BaseInput
                name="city"
                placeholder="Select City"
                label="City"
                type="select"
              />
            </div>
            <div className="col-12 col-sm-6 col-md-6 col-lg-12  mt-3 mb-2">
              <Checkbox onChange={onChange}>Least Commented On</Checkbox>
            </div>
            <div className="col-12 col-sm-6 col-md-6 col-lg-12  mt-3 mb-2">
              <Checkbox onChange={onChange}>Most asked questions</Checkbox>
            </div>

            <div className="col-12 col-sm-12"></div>
          </div>
          <div className="row">
            <div className="col-12 mt-3 text-end">
              <FlatButton
                title="Reset"
                className="gray-btn"
                onClick={handleOk}
              />
              <FlatButton
                title="Apply"
                className="blue-btn ms-3 "
                onClick={handleOk}
              />
            </div>
          </div>
        </Form>
      </CustomModal>
    </InnerLayout>
  );
};

export default Contract;
