import React, { useState, memo } from "react";
import { message, Upload, Avatar, Form } from "antd";
import { UserOutlined, PlusOutlined, InboxOutlined } from "@ant-design/icons";

const { Dragger } = Upload;

const getBase64 = (img, callback) => {
  const reader = new FileReader();
  reader.readAsDataURL(img);
  reader.onloadend = function () {
    callback(reader.result);
  };
};

const CustomUpload = ({
  fileList,
  listType = "picture-circle",
  callback,
  static_img = "/images/upload-profile.png",
  fileType = "image", // "image", "document", "any"
  multiple = false,
  maxSize = 2, // MB
  showErrors = true,
  useFormItem = false,
  formItemProps = {},
  uploadAction = "", // Server endpoint
  ...props
}) => {
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState();
  const [errors, setErrors] = useState([]);

  const getAcceptedTypes = () => {
    switch (fileType) {
      case "image":
        return "image/*";
      case "document":
        return ".pdf,.doc,.docx,.txt";
      case "any":
      default:
        return "*";
    }
  };

  const validateFileType = (file) => {
    switch (fileType) {
      case "image":
        return file.type.startsWith("image/");
      case "document":
        return [
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "text/plain",
        ].includes(file.type);
      case "any":
      default:
        return true;
    }
  };

  const beforeUpload = (file) => {
    const newErrors = [];

    // File type validation
    if (!validateFileType(file)) {
      const errorMsg = `Invalid file type. Only ${fileType} files are allowed.`;
      newErrors.push(errorMsg);
      if (showErrors) message.error(errorMsg);
      return false;
    }

    // File size validation
    const isValidSize = file.size / 1024 / 1024 < maxSize;
    if (!isValidSize) {
      const errorMsg = `File must be smaller than ${maxSize}MB!`;
      newErrors.push(errorMsg);
      if (showErrors) message.error(errorMsg);
      return false;
    }

    setErrors(newErrors);

    // Handle file selection and preview immediately
    const fileWithObj = {
      ...file,
      fileObj: file,
    };

    if (fileType === "image") {
      getBase64(file, (url) => {
        setImageUrl(url);
        callback && callback(fileWithObj);
      });
    } else {
      callback && callback(fileWithObj);
    }

    // Prevent actual upload to server
    return false;
  };

  const handleChange = (info) => {
    // This function is kept for compatibility but main logic is in beforeUpload
    setLoading(false);
  };

  const uploadProps = {
    name: "file",
    multiple,
    action: uploadAction,
    accept: getAcceptedTypes(),
    beforeUpload,
    onChange: handleChange,
    fileList,
    ...props,
  };

  const renderUploadComponent = () => {
    if (listType === "picture-circle" && fileType === "image") {
      return (
        <Upload
          {...uploadProps}
          listType={listType}
          className="avatar-uploader"
          showUploadList={false}
        >
          <div style={{ position: "relative", display: "inline-block" }}>
            <Avatar
              src={imageUrl || static_img}
              size={100}
              icon={!imageUrl && !static_img ? <UserOutlined /> : null}
              style={{
                objectFit: "cover",
                borderRadius: "50%",
              }}
            />
            <div
              style={{
                position: "absolute",
                bottom: 0,
                right: 0,
                backgroundColor: "#1890ff",
                borderRadius: "50%",
                width: 24,
                height: 24,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                color: "#fff",
                border: "2px solid #fff",
                cursor: "pointer",
              }}
            >
              <PlusOutlined style={{ fontSize: 12 }} />
            </div>
          </div>
        </Upload>
      );
    }

    return (
      <Dragger {...uploadProps}>
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">
          Click or drag {fileType} files to this area to upload
        </p>
        <p className="ant-upload-hint">
          {multiple
            ? "Support for multiple files."
            : "Support for single file."}
          {` Max size: ${maxSize}MB`}
        </p>
      </Dragger>
    );
  };

  const uploadComponent = (
    <div>
      {renderUploadComponent()}
      {showErrors && errors.length > 0 && (
        <div style={{ color: "#ff4d4f", marginTop: 8 }}>
          {errors.map((error, index) => (
            <div key={index}>{error}</div>
          ))}
        </div>
      )}
    </div>
  );

  if (useFormItem) {
    return <Form.Item {...formItemProps}>{uploadComponent}</Form.Item>;
  }

  return uploadComponent;
};

export default memo(CustomUpload);
