import { Form } from 'antd'
import React from 'react'
import BaseInput from '../../shared/inputs'
import CustomUpload from '../../shared/upload'

const CreateProjectForm = () => {
  return (
    <Form
          name="login"
          layout="vertical"
          onFinish={() => setIsModalOpen(false)}
          initialValues={{
            remember: true,
          }}
          autoComplete="off"
        >
          <BaseInput
            name="projectName"
            placeholder=""
            label="Project Name"
          />
          <BaseInput
            name="sssignedMembers"
            type="select"
            placeholder=""
            label="Assignee Members"
          />
          <BaseInput
            name="location"
            placeholder=""
            label="Location"
          />
          <label className='color-black font-600 mt-3 mb-1'>Add Image</label>
          <CustomUpload className="" />
        </Form>
  )
}

export default CreateProjectForm
