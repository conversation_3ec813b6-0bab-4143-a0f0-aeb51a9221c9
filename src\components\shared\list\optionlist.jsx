import { Link } from "react-router-dom";

const OptionList = ({ options }) => {
  return (
    <div className="p-3">
      {options.map((item, index) => {
        if (item.link) {
          return (
            <Link
              key={index}
              to={item.link}
              className="d-block text-dark text-decoration-none"
            >
              <div className="py-3 font-18 border-bottom" style={{ cursor: "pointer" }}>
                {item.label}
              </div>
            </Link>
          );
        } else {
          return (
            <div
              key={index}
              className="py-3 font-18 border-bottom text-dark"
              style={{ cursor: "pointer" }}
              onClick={item.action}
            >
              {item.label}
            </div>
          );
        }
      })}
    </div>
  );
};

export default OptionList;
