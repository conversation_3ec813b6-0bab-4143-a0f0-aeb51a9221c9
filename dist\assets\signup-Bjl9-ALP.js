import{u as I,R as j,j as e,L as _,d as h,H as D}from"./index-3mE9H3a0.js";import{A as T}from"./index-CTEMBgeC.js";import{c as r,B as a,P as B,v as l,C as R}from"./index-Cd-Wothc.js";import{F as O}from"./flatbutton-Yo0mdDJ8.js";import{C as M}from"./index-Cypjjnzq.js";import{u as H}from"./useMutation-lvneVzbk.js";import{u as U,a as J}from"./useLocationData-QmC5yoKM.js";import{g as V,t as z}from"./languageUtils-BKYM3hOY.js";import{F as x}from"./react-stripe.esm-CaXK0k-R.js";import"./index-B2p2olBm.js";import"./button-CMBVME-6.js";import"./index-CY48Knbi.js";import"./fade-yKzH711D.js";import"./DeleteOutlined-DCDN0YWx.js";import"./index-BUUUOhAK.js";import"./InboxOutlined-ERhPIBBD.js";import"./useQuery-Bzo2W4ue.js";const te=()=>{var g;const b=I(),[m]=x.useForm(),{data:c}=U(),[p,f]=j.useState([]),{selectedState:N,stateOptions:y,cityOptions:v,statesLoading:w,citiesLoading:k,handleStateChange:P}=J(),{mutate:L,isPending:u}=H("signup",{onSuccess:async s=>{s&&(await D.setStorageData("session",s.data),window.user=s.data,b("/home"))}}),C=j.useMemo(()=>{var s;return V((s=c==null?void 0:c.data)==null?void 0:s.languages)},[(g=c==null?void 0:c.data)==null?void 0:g.languages]),S=s=>{const n=[];s.professional_type&&s.professional_type.length>0&&s.professional_type.forEach(d=>{const o={name:d};d==="broker"&&s.broker_license_number?(o.license_number=s.broker_license_number,o.license_expiry_date=s.broker_license_expiry_date?h.formatForAPI(s.broker_license_expiry_date):""):d==="lender"&&s.lender_license_number?(o.license_number=s.lender_license_number,o.license_expiry_date=s.lender_license_expiry_date?h.formatForAPI(s.lender_license_expiry_date):""):d==="commercial"&&s.commercial_license_number&&(o.license_number=s.commercial_license_number,o.license_expiry_date=s.commercial_license_expiry_date?h.formatForAPI(s.commercial_license_expiry_date):""),n.push(o)});const i={...s,professional_types:JSON.stringify(n),device:"web",device_token:"web-token-"+Date.now()};delete i.professional_type,delete i.broker_license_number,delete i.broker_license_expiry_date,delete i.lender_license_number,delete i.lender_license_expiry_date,delete i.commercial_license_number,delete i.commercial_license_expiry_date,delete i.languages;const t=z(s.languages),E={...i,...t};L(E)},q=s=>{P(s,m)},F=s=>{f(s)},A=s=>{console.log(`checked = ${s.target.checked}`)};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"text-center sign-up-logo",children:e.jsx("img",{src:"../assets/img/logo.png",alt:"Auth Logo"})}),e.jsxs(T,{showSidebar:!1,pageType:"signup",children:[e.jsxs("div",{className:"col-12 text-center",children:[e.jsx("h1",{className:"font-36 color-black mb-4",children:"Sign Up"}),e.jsx("h1",{className:"font-36 color-black",children:"Create An Account"}),e.jsx("p",{children:"Register yourself here to continue"})]}),e.jsxs(x,{name:"register",layout:"vertical",form:m,onFinish:S,scrollToFirstError:!0,initialValues:{remember:!0},autoComplete:"off",children:[e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-12 text-center mt-4",children:e.jsx(M,{static_img:"/assets/img/avatar-img.svg",fileType:"image",multiple:!1,maxSize:5,uploadAction:"",useFormItem:!0,formItemProps:{name:"image_url",rules:r("profile-image",l.required)},callback:s=>{m.setFieldsValue({image_url:s.fileObj||s.originFileObj})}})})}),e.jsxs("div",{className:"row mt-5 justify-content-center",children:[e.jsx("div",{className:"col-12 col-md-6 col-lg-5 offset-lg-1",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12",children:e.jsx("h4",{className:"",children:"Personal Info"})}),e.jsx("div",{className:"col-12 col-md-12 ",children:e.jsx(a,{name:"name",placeholder:"e.g John Doe",label:"Full Name",rules:r("full-name",l.required,l.minLength(2),l.maxLength(25))})}),e.jsx("div",{className:"col-12 col-md-12 ",children:e.jsx(a,{name:"email",placeholder:"<EMAIL>",label:"Email",rules:r("email",l.required,l.email)})}),e.jsx("div",{className:"col-12 col-md-12 ",children:e.jsx(B,{name:"mobile_no",label:"Phone Number",rules:r("Phone Number",l.required,l.phone)})}),e.jsx("div",{className:"col-12 col-md-12 ",children:e.jsx(a,{name:"password",placeholder:"***",label:"Password",type:"password",rules:r("password",l.required,l.password)})}),e.jsx("div",{className:"col-12 col-md-12 ",children:e.jsx(a,{name:"confrimpassword",placeholder:"***",label:"Confirm Password",type:"password",rules:[l.required("confirm-password"),({getFieldValue:s})=>({validator:(n,i)=>{const t=s("password");return!i||!t?Promise.resolve():i!==t?Promise.reject(new Error("Confirm Passwords does not match")):Promise.resolve()}})],dependencies:["password"]})})]})}),e.jsx("div",{className:"col-12 col-md-6 col-lg-5 offset-lg-1",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12",children:e.jsx("h4",{children:"Professional Info"})}),e.jsx("div",{className:"col-12 col-md-12 ",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-6 col-md-6",children:e.jsx(a,{name:"state",placeholder:"State",label:"Location",type:"select",rules:r("state",l.required),options:y,loading:w,handlechange:q,showSearch:!0})}),e.jsx("div",{className:"col-6 col-md-6",children:e.jsx(a,{name:"city",placeholder:"City",label:" ",type:"select",options:v,rules:r("city",l.required),disabled:!N,loading:k,showSearch:!0})})]})}),e.jsx("div",{className:"col-6 col-md-6",children:e.jsx(a,{name:"languages",placeholder:"Select Language",label:"Languages Spoken",type:"select",rules:r("language",l.required),mode:"multiple",options:C})}),e.jsx("div",{className:"col-12 col-md-6 ",children:e.jsx("div",{className:"ant-form-item d-block",children:e.jsx("div",{className:"mt-2",children:e.jsx(a,{type:"radio",name:"multi_state_license",label:"Multi State License",options:[{value:!0,label:"Yes"},{value:!1,label:"No"}],rules:r("multi-state-license",l.required)})})})}),e.jsx("div",{className:"col-12 ",children:e.jsx(a,{type:"checkboxgroup",name:"professional_type",label:"Profession Type",options:[{value:"broker",label:"Real Estate Broker"},{value:"lender",label:"Lender Mortgage Broker"},{value:"commercial",label:"Commercial Agent"}],onChange:F,rules:r("profession-type",l.required)})}),p.includes("broker")&&e.jsxs("div",{className:"col-12",children:[e.jsx("h5",{className:"mt-3 font-18",children:"Real Estate Broker License"}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(a,{name:"broker_license_number",placeholder:"1ABC234",label:"License No.",rules:r("license",l.required)})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(a,{name:"broker_license_expiry_date",placeholder:"Expiry Date",label:"Expiry Date",type:"datepicker",rules:r("expiry",l.required)})})]})]}),p.includes("lender")&&e.jsxs("div",{className:"col-12",children:[e.jsx("h5",{className:"font-18 mt-3",children:"Lender/ mtg Broker License"}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(a,{name:"lender_license_number",placeholder:"1ABC234",label:"License No.",rules:r("license",l.required)})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(a,{name:"lender_license_expiry_date",placeholder:"Expiry Date",label:"Expiry Date",type:"datepicker",rules:r("expiry",l.required)})})]})]}),p.includes("commercial")&&e.jsxs("div",{className:"col-12",children:[e.jsx("h5",{className:"font-18 mt-3",children:"Commercial Agent License"}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(a,{name:"commercial_license_number",placeholder:"1ABC234",label:"License No.",rules:r("license",l.required)})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(a,{name:"commercial_license_expiry_date",placeholder:"Expiry Date",label:"Expiry Date",type:"datepicker",rules:r("expiry",l.required)})})]})]})]})})]}),e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-12",children:e.jsx("div",{className:"text-center",children:e.jsx("div",{className:"mt-4 check-item",children:e.jsx(x.Item,{name:"check",valuePropName:"checked",rules:[{validator:(s,n)=>n?Promise.resolve():Promise.reject(new Error("Please accept the Terms and Conditions"))}],children:e.jsxs(R,{onChange:A,children:["I agree to the",e.jsx(_,{to:"#",className:"color-blue  font-16 ms-1",children:"Terms and Conditions"})]})})})})})}),e.jsx("div",{className:"text-center",children:e.jsx(O,{title:u?"Creating Account...":"Register Now",className:"mx-auto mt-4 signin-btn signup-btn mt-5",htmlType:"submit",loading:u,disabled:u})}),e.jsx("div",{children:e.jsxs("p",{className:"signup-text",children:["Don't have an account?",e.jsx(_,{to:"/login",className:"color-blue  font-16 ms-1",children:"Sign In"})]})})]})]})]})};export{te as default};
