import React from "react";
import AuthSidebar from "./authsidebar";

const AuthLayout = ({
  children,
  logoClass = "auth-logo",
  title,
  detail,
  src = "../admin/assets/img/auth-img.png",
  showSidebar = false,
  pageType = "login", // "login" | "signup" | "forget"
}) => {
  // Dynamic column class based on page type
  let contentColClass = "";
  if (pageType === "signup") {
    contentColClass = "col-12 col-sm-12 col-md-12 col-lg-10 col-xl-10";
  } else if (pageType === "login") {
    contentColClass = "col-12 col-sm-10 col-md-7 col-lg-5 col-xl-4";
  } else if (pageType === "forgot") {
    contentColClass = "col-12 col-sm-12 col-md-8 col-lg-5 col-xl-4";
  } else{
    contentColClass = "col-12 col-sm-12 col-md-12 col-lg-10 col-xl-8"; // fallback
  }

  return (
    <div className="auth">
      <div className="container">
        <div className="row align-items-center gx-0 justify-content-center">
        
          <div className={contentColClass}>
            <div className="auth-box">
              <div className="col-12">
                <p className="font-36 color-black">{title}</p>
                <p>{detail}</p>
              </div>
              {children}
            </div>
          </div>
            {showSidebar && (
            <div className="col-12 col-sm-12 col-md-6  col-lg-7 col-xl-7">
              <AuthSidebar src={src} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
