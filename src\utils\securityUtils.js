/**
 * Security utilities for Remember Me functionality
 * Provides additional security layers for persistent authentication
 */

/**
 * Generate a secure device fingerprint for additional security
 * @returns {string} - Device fingerprint
 */
export const generateDeviceFingerprint = () => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  ctx.textBaseline = 'top';
  ctx.font = '14px Arial';
  ctx.fillText('Device fingerprint', 2, 2);
  
  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL(),
    navigator.hardwareConcurrency || 'unknown',
    navigator.deviceMemory || 'unknown'
  ].join('|');
  
  return btoa(fingerprint).slice(0, 32);
};

/**
 * Validate device fingerprint for security
 * @param {string} storedFingerprint - Previously stored fingerprint
 * @returns {boolean} - Whether device is trusted
 */
export const validateDeviceFingerprint = (storedFingerprint) => {
  const currentFingerprint = generateDeviceFingerprint();
  return currentFingerprint === storedFingerprint;
};

/**
 * Enhanced security check for Remember Me sessions
 * @param {Object} persistentSession - Stored persistent session data
 * @returns {Object} - Security validation result
 */
export const validateRememberMeSecurity = async (persistentSession) => {
  const result = {
    isValid: false,
    reason: '',
    shouldClearSession: false
  };

  if (!persistentSession) {
    result.reason = 'No persistent session found';
    return result;
  }

  // Check expiration
  if (persistentSession.expiresAt <= Date.now()) {
    result.reason = 'Session expired';
    result.shouldClearSession = true;
    return result;
  }

  // Check device fingerprint if available
  if (persistentSession.deviceFingerprint) {
    if (!validateDeviceFingerprint(persistentSession.deviceFingerprint)) {
      result.reason = 'Device fingerprint mismatch - possible security risk';
      result.shouldClearSession = true;
      return result;
    }
  }

  // Check for suspicious activity (too many failed attempts, etc.)
  if (persistentSession.failedAttempts && persistentSession.failedAttempts > 3) {
    result.reason = 'Too many failed attempts';
    result.shouldClearSession = true;
    return result;
  }

  // Check session age - if older than 30 days, require re-authentication
  const sessionAge = Date.now() - persistentSession.timestamp;
  const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
  
  if (sessionAge > maxAge) {
    result.reason = 'Session too old';
    result.shouldClearSession = true;
    return result;
  }

  result.isValid = true;
  return result;
};

/**
 * Create a secure persistent session object
 * @param {Object} credentials - User credentials
 * @returns {Object} - Secure session object
 */
export const createSecurePersistentSession = (credentials) => {
  return {
    enabled: true,
    timestamp: Date.now(),
    expiresAt: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30 days
    deviceFingerprint: generateDeviceFingerprint(),
    userAgent: navigator.userAgent,
    ipHash: null, // Could be set by backend
    failedAttempts: 0,
    lastActivity: Date.now(),
    securityVersion: '1.0'
  };
};

/**
 * Update persistent session activity
 * @param {Object} persistentSession - Current session
 * @returns {Object} - Updated session
 */
export const updateSessionActivity = (persistentSession) => {
  return {
    ...persistentSession,
    lastActivity: Date.now(),
    timestamp: Date.now() // Update timestamp to extend session
  };
};

/**
 * Increment failed login attempts
 * @param {Object} persistentSession - Current session
 * @returns {Object} - Updated session with incremented failures
 */
export const incrementFailedAttempts = (persistentSession) => {
  const failedAttempts = (persistentSession.failedAttempts || 0) + 1;
  
  return {
    ...persistentSession,
    failedAttempts,
    lastFailedAttempt: Date.now()
  };
};

/**
 * Reset failed attempts after successful login
 * @param {Object} persistentSession - Current session
 * @returns {Object} - Updated session with reset failures
 */
export const resetFailedAttempts = (persistentSession) => {
  return {
    ...persistentSession,
    failedAttempts: 0,
    lastFailedAttempt: null,
    lastActivity: Date.now()
  };
};